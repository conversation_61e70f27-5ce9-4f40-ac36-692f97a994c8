package service

import (
	"app/model"
	"app/repo"
	"app/util"
	"context"
	"errors"
	"strconv"

	"gorm.io/gorm"
)

type NpcSyncService struct {
	npcRepo      *repo.NpcRepo
	roleInfoRepo *repo.RoleInfoRepo
}

func NewNpcSyncService(npcRepo *repo.NpcRepo, roleInfoRepo *repo.RoleInfoRepo) *NpcSyncService {
	return &NpcSyncService{
		npcRepo:      npcRepo,
		roleInfoRepo: roleInfoRepo,
	}
}

func (s *NpcSyncService) SyncNpcInfo(ctx context.Context) error {
	db := s.npcRepo.DB
	var npc model.NpcModel
	rows, err := db.Model(&npc).Rows()
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("SyncNpcInfoFail")
	}
	defer rows.Close()

	for rows.Next() {
		var npc model.NpcModel
		// ScanRows scans a row into a struct
		db.ScanRows(rows, &npc)

		// Perform operations on each user
		var roleInfo model.RoleInfoModel
		err := db.Model(roleInfo).Where("RoleId = ?", npc.RoleId).First(&roleInfo).Error
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				util.LogWithContext(ctx).WithError(err).WithField("roleId", npc.RoleId).Error("FindNpcRoleError")
				return err
			}
		}

		roleInfo.RoleId = npc.RoleId
		roleInfo.RoleName = npc.Name
		roleInfo.Avatar = strconv.Itoa(npc.AvatarId)
		roleInfo.CreateTime = npc.Ctime
		db.Save(&roleInfo)
	}

	return nil
}
