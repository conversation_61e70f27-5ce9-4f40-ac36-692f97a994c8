package conn

import (
	"app/config"
	"fmt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"log"
	"sync"
	"time"
)

const DB2 = "db2"

var db2Once = sync.Once{}

func ConnectDB2(conf *config.MySQLConfig) {
	db2Once.Do(func() {
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", conf.Username, conf.Password, conf.Host, conf.Port, conf.DBName)
		var err error
		db, err := gorm.Open(mysql.Open(dsn+"?charset=utf8mb4&parseTime=True&loc=Local"), &gorm.Config{
			TranslateError: true,
			//Logger: logger.New(
			//	log.New(os.Stdout, "\r\n", log.LstdFlags),
			//	logger.Config{
			//		SlowThreshold:             time.Second,
			//		Colorful:                  false,
			//		IgnoreRecordNotFoundError: true,
			//		ParameterizedQueries:      true,
			//		LogLevel:                  logger.Info,
			//	}),
		})
		if err != nil {
			log.Fatal(dsn, err)
		}
		sqlDB, err := db.DB()
		if err != nil {
			log.Fatal(dsn, err)
		}
		sqlDB.SetMaxIdleConns(conf.MaxIdle)
		sqlDB.SetMaxOpenConns(conf.MaxOpen)
		sqlDB.SetConnMaxLifetime(time.Duration(conf.ConnMaxLifetime) * time.Minute)
		dbMap[DB2] = db
		fmt.Printf("database %s conn success ! %v\n", DB2, sqlDB.Ping())
	})
}
