package api

import (
	"os"
	"time"

	"github.com/gin-gonic/gin"
)

func Health(ctx *gin.Context) {
	data := NewHealthApi().Run(ctx)
	HandleResponse(ctx, data, nil)
}

type HealthApi struct{}

func NewHealthApi() *HealthApi {
	return &HealthApi{}
}

func (h *HealthApi) Run(ctx *gin.Context) gin.H {
	hostname, err := os.Hostname()
	if err != nil {
		return gin.H{"time": time.Now().Format(time.RFC3339)}
	} else {
		return gin.H{"hostname": hostname, "time": time.Now().Format(time.RFC3339)}
	}
}
