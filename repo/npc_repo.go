package repo

import (
	"app/api"
	"app/model"
	"app/util"
	"context"
	"errors"

	"gorm.io/gorm"
)

type NpcRepo struct {
	*BaseRepo[model.NpcModel]
}

func NewNpcRepo(db *gorm.DB) *NpcRepo {
	return &NpcRepo{
		BaseRepo: NewBaseRepo[model.NpcModel](db),
	}
}

func (r *NpcRepo) FindByRoleId(ctx context.Context, roleId int64) (model.NpcModel, error) {
	npc, err := r.FindOne(ctx, "role_id= ?", roleId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return npc, api.GetBizErr(api.ErrBizNpcNotFound)
		}
	}
	return npc, nil
}

func (r *NpcRepo) GetRoleInfoMap(ctx context.Context, roleIds []int64) map[int64]model.NpcModel {
	var npcList []model.NpcModel
	err := r.<PERSON>(ctx).
		Model(&model.NpcModel{}).
		Where("role_id in (?)", roleIds).
		Find(&npcList).Error
	if err != nil {
		return nil
	}
	roleInfoMap := make(map[int64]model.NpcModel)
	for _, npc := range npcList {
		roleInfoMap[npc.RoleId] = npc
	}
	return roleInfoMap
}

func (r *NpcRepo) GetIsVipMap(ctx context.Context, roleIds []int64) (map[int64]bool, error) {
	var npcList []model.NpcModel
	err := r.Scope(ctx).
		Model(&model.NpcModel{}).
		Where("role_id in (?)", roleIds).
		Where("is_vip = ?", 1).
		Find(&npcList).Error
	if err != nil {
		return nil, err
	}
	IsVipMap := make(map[int64]bool)
	for _, npc := range npcList {
		IsVipMap[npc.RoleId] = true
	}
	return IsVipMap, nil
}

func (r *NpcRepo) GetNpcInfoMap(ctx context.Context, roleIds []int64) (map[int64]model.NpcModel, error) {
	var npcList []model.NpcModel
	err := r.Scope(ctx).
		Model(&model.NpcModel{}).
		Where("role_id in (?)", roleIds).
		Find(&npcList).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleIds", roleIds).Error("GetNpcInfoMapFail")
		return nil, err
	}
	npcMap := make(map[int64]model.NpcModel)
	for _, npc := range npcList {
		npcMap[npc.RoleId] = npc
	}
	return npcMap, nil
}

// is npc vip
func (r *NpcRepo) IsVip(ctx context.Context, roleId int64) (bool, error) {
	npc, err := r.FindByRoleId(ctx, roleId)
	if err != nil {
		return false, err
	}
	return npc.IsVip, nil
}

func (r *NpcRepo) GetNpcList(ctx context.Context, roleIds []int64) ([]model.NpcModel, error) {
	var npcList []model.NpcModel
	err := r.Scope(ctx).
		Model(&model.NpcModel{}).
		Where("role_id in (?)", roleIds).
		Find(&npcList).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleIds", roleIds).Error("GetNpcListFail")
		return nil, err
	}
	return npcList, nil
}

func (r *NpcRepo) GetNpcListMap(ctx context.Context, roleIds []int64) (map[int64]model.NpcModel, error) {
	npcList, err := r.GetNpcList(ctx, roleIds)
	if err != nil {
		return nil, err
	}
	npcMap := make(map[int64]model.NpcModel)
	for _, npc := range npcList {
		npcMap[npc.RoleId] = npc
	}
	return npcMap, nil
}
