package model

type AdminScope int

const (
	AdminNpcScope AdminScope = 1
)

type AdminRoleType int8

const (
	RoleTypeNormal   AdminRoleType = 0
	RoleTypeAdmin    AdminRoleType = 1
	RoleTypeReadOnly AdminRoleType = 2
)

type OpenIdRoleModel struct {
	BaseModel
	OpenId   string
	FullName string
	Scope    AdminScope    `json:"scope"`
	RoleType AdminRoleType `json:"role_type"`
}

func (m *OpenIdRoleModel) TableName() string {
	return "l50_openid_role"
}
