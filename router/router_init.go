package router

import (
	"app/api"
	"app/config"
	"app/middleware"
	"app/util"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"runtime/debug"
	"time"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/errors"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/kit"
	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"gopkg.in/natefinch/lumberjack.v2"
)

func InitRouter(routeRegister func(r *gin.Engine)) *gin.Engine {
	// 初始化gin access logger、error logger writer
	aw, ew, err := initGinLoggerWriter()
	if nil != err {
		panic(err)
	}

	// 创建gin引擎实例
	router := gin.New()

	// 面向前端需要跨域时需打开此中间件，不要将此中间件下放至子组路由，会使OPTIONS请求跨域头不生效且404
	router.Use(middleware.Cors())

	// 创建gin全局日志记录
	logger := gin.LoggerWithConfig(gin.LoggerConfig{
		Formatter: accessLoggerFormatter,
		Output:    aw,
	})

	// 创建gin崩溃恢复中间件
	recovery := gin.RecoveryWithWriter(ew, recoveryHandler)

	// 设置gin全局中间件
	router.Use(logger, recovery)

	// 设置gin参数
	router.MaxMultipartMemory = int64(config.C.Gin.MaxMultipartMemory) * 1024 * 1024

	// 设置gin pprof
	if 1 == config.C.Gin.Pprof {
		pprof.Register(router)
	}

	// 注册路由
	routeRegister(router)

	return router
}

// initGinLoggerWriter 初始化gin access logger、error logger writer
func initGinLoggerWriter() (io.Writer, io.Writer, error) {
	af := config.C.Gin.Log.LogPath + "/" + config.C.Gin.Log.AccessFileName
	_, err := os.OpenFile(af, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if nil != err {
		return nil, nil, err
	}
	aw := &lumberjack.Logger{
		Filename:   af,
		MaxSize:    config.C.Gin.Log.MaxSize,
		MaxAge:     config.C.Gin.Log.ReserveDays,
		MaxBackups: config.C.Gin.Log.ReserveCount,
		LocalTime:  true,
		Compress:   false,
	}

	ef := config.C.Gin.Log.LogPath + "/" + config.C.Gin.Log.ErrorFileName
	_, err = os.OpenFile(ef, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if nil != err {
		return nil, nil, err
	}
	ew := &lumberjack.Logger{
		Filename:   ef,
		MaxSize:    config.C.Gin.Log.MaxSize,
		MaxAge:     config.C.Gin.Log.ReserveDays,
		MaxBackups: config.C.Gin.Log.ReserveCount,
		LocalTime:  true,
		Compress:   false,
	}

	return aw, ew, nil
}

func accessLoggerFormatter(param gin.LogFormatterParams) string {
	if param.Latency > time.Minute {
		param.Latency = param.Latency.Truncate(time.Second)
	}

	data := map[string]any{
		"app":         config.C.App.AppName,
		"hostname":    kit.Hostname(),
		"schema":      config.C.Gin.Log.Schema,
		"time":        param.TimeStamp.UnixMilli(),
		"ts":          param.TimeStamp.Format(time.DateTime),
		"api":         param.Path,
		"method":      param.Method,
		"client_ip":   param.ClientIP,
		"query":       param.Request.URL.Query(),
		"header":      param.Request.Header,
		"error":       param.ErrorMessage,
		"latency":     param.Latency.String(),
		"status_code": param.StatusCode,
	}
	db, _ := json.Marshal(data)
	return fmt.Sprintf("%s\n", string(db))
}

func recoveryHandler(c *gin.Context, err any) {
	util.LogWithContext(c).WithField("err", err).Error("InRecveryHandler")
	// 打印错误堆栈信息
	fmt.Printf("Recovered from panic: %v\n", err)
	fmt.Println(string(debug.Stack()))

	api.HandleResponse(c, nil, errors.New(errors.UnknownError))
	c.Abort()
}
