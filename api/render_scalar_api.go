package api

import (
	"app/config"
	"app/docs"
	"app/schema"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"

	"github.com/gin-gonic/gin"
)

type ScalarConfigOption struct {
	Servers        []schema.SwaggerServer `json:"servers"`
	Authentication Authentication         `json:"authentication"`
}

type Authentication struct {
	PreferredSecurityScheme string `json:"preferredSecurityScheme"`
	APIKey                  APIKey `json:"apiKey"`
}

type APIKey struct {
	Token string `json:"token"`
}

func RenderScalar() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		docDesc := docs.SwaggerInfo.Description
		docs.SwaggerInfo.Description = docDesc

		if len(config.C.HttpCode) > 0 {
			codeList := []string{}
			for k := range config.C.HttpCode {
				codeList = append(codeList, k)
			}
			sort.Strings(codeList)

			docDesc += "\n\n"
			// generate err code description use markdown
			docDesc += "|code|message|\n"
			docDesc += "|------|------|\n"
			for _, code := range codeList {
				docDesc += fmt.Sprintf("|%s|%s|\n", code, config.C.HttpCode[code])
			}
			docDesc += "\n"
		}
		docs.SwaggerInfo.Description = docDesc

		docContent := docs.SwaggerInfo.ReadDoc()

		configOption := ScalarConfigOption{
			Servers: config.C.Swagger.Servers,
			Authentication: Authentication{
				PreferredSecurityScheme: "api_key",
				APIKey: APIKey{
					Token: config.C.Auth.CheatSkey,
				},
			},
		}

		configOptionStr, err := json.Marshal(configOption)
		if err != nil {
			HandleResponse(ctx, nil, err)
		}

		htmlContent := fmt.Sprintf(`<!doctype html>
<html>
  <head>
    <title>%s</title>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1" />
  </head>
  <body>
    <script
      id="api-reference"
      type="application/yaml">
      %s
    </script>
       <script>
        document.getElementById('api-reference').dataset.configuration = '%s'
    </script>
    <script src="https://cdn.jsdelivr.net/npm/@scalar/api-reference"></script>
  </body>
</html>
`, config.C.App.AppName, docContent, string(configOptionStr))
		ctx.Data(http.StatusOK, "text/html", []byte(htmlContent))
	}
}
