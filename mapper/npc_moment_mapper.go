package mapper

import (
	"app/model"
	"app/schema"

	"github.com/samber/lo"
)

type NpcMomentMapper struct{}

func NewNpcMomentMapper() *NpcMomentMapper {
	return &NpcMomentMapper{}
}

func (m *NpcMomentMapper) ToNpcMomentShow(moment model.MomentModel, npcs map[int64]model.NpcModel, avatarsMap map[int]string) (schema.NpcMomentShowResp, error) {
	publicTime := moment.Ctime
	if moment.PublicTime > 0 {
		publicTime = moment.PublicTime
	}
	var roleInfo *schema.TinyRoleInfo

	npcRole, ok := npcs[moment.RoleId]
	if ok {
		roleInfo = &schema.TinyRoleInfo{
			RoleId:    moment.RoleId,
			RoleName:  npcRole.Name,
			AvatarId:  npcRole.AvatarId,
			AvatarUrl: avatarsMap[npcRole.AvatarId],
		}
	}

	n := schema.NpcMomentShowResp{
		NpcMomentAddReq: schema.NpcMomentAddReq{
			RoleId:         moment.RoleId,
			Text:           moment.Text,
			ImgList:        moment.GetImgList(),
			VideoList:      moment.GetVideoList(),
			VideoCoverList: moment.GetVideoCoverList(),
			BaseLikeCount:  moment.BaseLikeCount,
			ViewCount:      moment.GetShowViewCount(),
			TaskId:         moment.TaskId,
			AreaId:         moment.AreaId,
			IsTop:          moment.TopTime > 0,
			PublicTime:     publicTime,
			CategoryId:     moment.CategoryId,
			IsTemplate:     moment.IsTemplate,
			ParentId:       moment.ParentId,
		},
		NpcMomentAddResp: schema.NpcMomentAddResp{
			BaseAddResp: schema.BaseAddResp{
				Id:         moment.ID,
				CreateTime: moment.Ctime,
				UpdateTime: moment.Utime,
			},
		},
		TopTime:       moment.TopTime,
		RoleInfo:      roleInfo,
		CommentCount:  moment.CommentCount,
		RealLikeCount: moment.RealLikeCount,
		LikeCount:     moment.GetLikeCount(),
	}
	return n, nil
}

func (m *NpcMomentMapper) ToNpcMomentShowList(momentList []model.MomentModel, npcInfoMap map[int64]model.NpcModel, avatarsMap map[int]string, subMomentList []model.MomentModel) ([]schema.NpcMomentShowResp, error) {
	var respList []schema.NpcMomentShowResp = []schema.NpcMomentShowResp{}
	subListMap := lo.GroupBy(subMomentList, func(moment model.MomentModel) int64 {
		return moment.ParentId
	})
	for _, npc := range momentList {
		item, err := m.ToNpcMomentShow(npc, npcInfoMap, avatarsMap)
		if err != nil {
			return nil, err
		}
		subList, ok := subListMap[npc.ID]
		if ok {
			subShowList := lo.Map(subList, func(moment model.MomentModel, index int) schema.NpcMomentShowResp {
				item, err := m.ToNpcMomentShow(moment, npcInfoMap, avatarsMap)
				if err != nil {
					return schema.NpcMomentShowResp{}
				}
				return item
			})
			item.SubList = subShowList
		} else {
			item.SubList = []schema.NpcMomentShowResp{}
		}
		respList = append(respList, item)
	}
	return respList, nil
}
