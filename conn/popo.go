package conn

import (
	"app/config"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/kit"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/z/popo"
	"fmt"
	"sync"
	"time"
)

var p popo.IPopo
var once sync.Once

func GetPopo() popo.IPopo {
	once.Do(func() {
		p = popo.NewPopo(config.C.Popo.Url,
			config.C.Popo.Salt,
			config.C.Popo.Project,
			config.C.Popo.Biz,
			config.C.Popo.Env,
			time.Duration(config.C.Popo.Timeout)*time.Millisecond,
			elog.GetStd(),
			fmt.Sprintf("Hostname: %s\nMessage:", kit.Hostname()),
			nil)
	})
	return p
}
