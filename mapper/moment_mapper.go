package mapper

import (
	"app/model"
	"app/repo"
	"app/schema"
	"app/util"
	"context"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"github.com/samber/lo"
)

type MomentMapper struct {
	avatarRepo *repo.AvatarRepo
	momentRepo *repo.MomentRepo
}

func NewMomentMapper(avatarRepo *repo.AvatarRepo, momentRepo *repo.MomentRepo) *MomentMapper {
	return &MomentMapper{
		avatarRepo: avatarRepo,
		momentRepo: momentRepo,
	}
}

func (m *MomentMapper) ToMomentShow(moment model.MomentModel, coreRoleInfo schema.CoreRoleInfo, isLike bool, isCollect bool, tplMoment model.MomentModel) (schema.MomentShow, error) {
	commentCount := moment.CommentCount
	if tplMoment.ID > 0 {
		commentCount += tplMoment.CommentCount
	}
	resp := schema.MomentShow{
		Id:             moment.ID,
		Text:           moment.Text,
		ImgList:        moment.GetImgList(),
		VideoList:      moment.GetVideoList(),
		VideoCoverList: moment.GetVideoCoverList(),
		LikeCount:      moment.GetLikeCount(),
		ViewCount:      moment.GetShowViewCount(),
		CommentCount:   commentCount,
		TaskId:         moment.TaskId,
		AreaId:         moment.AreaId,
		CategoryId:     moment.CategoryId,
		PublicTime:     moment.PublicTime,
		RoleInfo:       coreRoleInfo,
		IsCollect:      isCollect,
		IsLike:         isLike,
		TemplateId:     moment.TemplateId,
		IsTemplate:     moment.IsTemplate,
	}
	return resp, nil
}

func (m *MomentMapper) ToMomentShowList(ctx context.Context, momentList []model.MomentModel, tinyInfoList []schema.TinyRoleInfo, isCollectMap map[int64]bool, isLikeMap map[int64]bool, isFollowMap map[int64]bool) ([]schema.MomentShow, error) {
	respList := []schema.MomentShow{}
	tinyInfoMap := make(map[int64]schema.TinyRoleInfo)
	for _, v := range tinyInfoList {
		tinyInfoMap[v.RoleId] = v
	}
	tplMomentIdList := lo.Map(momentList, func(v model.MomentModel, _ int) int64 { return v.TemplateId })
	tplMomentIdList = lo.Filter(lo.Uniq(tplMomentIdList), func(v int64, _ int) bool { return v > 0 })
	tplMomentList, err := m.momentRepo.FindByIdsNoOrder(ctx, tplMomentIdList)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("tplMomentIdList", tplMomentIdList).Error("FindTplMomentListFail")
		return respList, err
	}
	tplMomentMap := lo.GroupBy(tplMomentList, func(v model.MomentModel) int64 { return v.ID })
	for _, v := range momentList {
		coreInfo := schema.CoreRoleInfo{
			TinyRoleInfo: tinyInfoMap[v.GetShowRoleId()],
			IsFollow:     isFollowMap[v.GetShowRoleId()],
		}
		_, isLike := isLikeMap[v.ID]
		_, isCollect := isCollectMap[v.ID]
		tplMoment := model.MomentModel{}
		tplMomentInMap, hasTpl := tplMomentMap[v.TemplateId]
		if hasTpl {
			tplMoment = tplMomentInMap[0]
		}
		moment, err := m.ToMomentShow(v, coreInfo, isLike, isCollect, tplMoment)
		if err != nil {
			elog.WithError(err).WithField("moment", v).Error("ToMomentShow fail")
		}
		respList = append(respList, moment)
	}
	return respList, nil
}
