package dataimport

import (
	"app/repo"
	"context"
	"strconv"
	"strings"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// Constants for import configuration
const (
	ImportBatchSize     = 100
	ImportStartSheetRow = 8
	ImportStartSheetCol = "A"
	SheetDataEndMarker  = "##终身码##"
)

// ImportStepResult represents the result of a single import step
type ImportStepResult struct {
	Count      int   `json:"count"`
	DurationMs int64 `json:"duration_ms"`
}

// CleanupDetailResult represents detailed cleanup statistics
type CleanupDetailResult struct {
	TotalCount      int   `json:"total_count"`
	MomentsDeleted  int   `json:"moments_deleted"`
	TopicsDeleted   int   `json:"topics_deleted"`
	CommentsDeleted int   `json:"comments_deleted"`
	NpcsDeleted     int   `json:"npcs_deleted"`
	DurationMs      int64 `json:"duration_ms"`
}

// ImportResult represents the overall import result
type ImportResult struct {
	Npcs            ImportStepResult    `json:"npcs"`
	Categories      ImportStepResult    `json:"categories"`
	Topics          ImportStepResult    `json:"topics"`
	Comments        ImportStepResult    `json:"comments"`
	Moments         ImportStepResult    `json:"moments"`
	MomentTopics    ImportStepResult    `json:"moment_topics"`
	Cleanup         CleanupDetailResult `json:"cleanup"`
	TotalDurationMs int64               `json:"total_duration_ms"`
	ErrorCount      int                 `json:"error_count"`
	WarnCount       int                 `json:"warn_count"`
	Errors          []string            `json:"-"`      // Internal use, not output to JSON
	Warns           []string            `json:"warns"`  // Output warns to JSON
}

// ImportContext holds the shared context for all import operations
type ImportContext struct {
	NpcRepo         *repo.NpcRepo
	MomentRepo      *repo.MomentRepo
	TopicRepo       *repo.TopicRepo
	CommentRepo     *repo.CommentRepo
	MomentTopicRepo *repo.MomentTopicRepo
	RoleInfoRepo    *repo.RoleInfoRepo
}

// SheetImporter defines the interface for sheet-specific importers
type SheetImporter interface {
	ImportSheet(ctx context.Context, file *excelize.File, tx *gorm.DB, result *ImportResult) error
	GetSheetName() string
}

// GetArrayLenFromColumn calculates the length of a semicolon-separated array
func GetArrayLenFromColumn(str string) int {
	arr := strings.Split(str, ";")
	for _, v := range arr {
		if strings.TrimSpace(v) != "" {
			return len(arr)
		}
	}
	return 0
}

// CheckId validates ID rules, special cases: -1 indicates end of reading, 0 skips the row
func CheckId(id string) (int64, error) {
	if id == "" {
		return -1, nil
	}
	if id[0] == '~' {
		return 0, nil
	}

	// Handle id|string format for compatibility
	if strings.Contains(id, "|") {
		id = strings.Split(id, "|")[0]
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		return 0, err
	}
	return idInt, nil
}
