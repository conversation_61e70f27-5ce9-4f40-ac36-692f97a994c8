package model

import (
	"encoding/json"
	"regexp"
	"unicode/utf8"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
)

const (
	TOPIC_REGEX      = "#([^#]+)#"
	MAX_TOPIC_LENGTH = 14

	// 用户创建
	CREATE_TYPE_USER = 0
	// NPC 创建
	CREATE_TYPE_NPC = 1
)

// moment表
type MomentModel struct {
	BaseCoreVersionModel
	RoleId              int64  `gorm:"column:role_id;NOT NULL" json:"roleId"`                             // 角色id
	ShowRoleId          int64  `gorm:"column:show_role_id;NOT NULL" json:"showRoleId"`                    // 展示使用的roleId
	Text                string `gorm:"column:text;NOT NULL" json:"text"`                                  // moment内容
	ImgList             string `gorm:"column:img_list;NOT NULL" json:"imgList"`                           // moment图片
	ImgAuditList        string `gorm:"column:img_audit_list;NOT NULL" json:"imgAuditList"`                // moment图片审核
	VideoList           string `gorm:"column:video_list;NOT NULL" json:"videoList"`                       // 视频列表
	VideoAuditList      string `gorm:"column:video_audit_list;NOT NULL" json:"videoAuditList"`            // moment视频审核
	VideoCoverList      string `gorm:"column:video_cover_list;NOT NULL" json:"videoCoverList"`            // moment视频封面
	VideoCoverAuditList string `gorm:"column:video_cover_audit_list;NOT NULL" json:"videoCoverAuditList"` // moment视频封面审核
	RealLikeCount       int64  `gorm:"column:real_like_count;NOT NULL" json:"realLikeCount"`              // 实际点赞数
	BaseLikeCount       int64  `gorm:"column:base_like_count;NOT NULL" json:"baseLikeCount"`              // 基础点赞数
	LikeCount           int64  `gorm:"column:like_count;NOT NULL" json:"likeCount"`                       // 点赞数
	CommentCount        int64  `gorm:"column:comment_count;NOT NULL" json:"commentCount"`                 // 评论数
	ViewCount           int64  `gorm:"column:view_count;NOT NULL" json:"viewCount"`                       // 浏览量
	TaskId              int    `gorm:"column:task_id;NOT NULL" json:"taskId"`                             // 任务id
	AreaId              int    `gorm:"column:area_id;NOT NULL" json:"areaId"`                             // 地区id
	TopTime             int64  `gorm:"column:top_time;NOT NULL" json:"topTime"`                           // 置顶时间, 最大的时间排在前面
	PublicTime          int64  `gorm:"column:public_time;NOT NULL" json:"publicTime"`                     // 发表时间
	TemplateId          int64  `gorm:"column:template_id;NOT NULL" json:"templateId"`                     // 创建引用的 npc模板id
	CategoryId          int    `gorm:"column:category_id;NOT NULL" json:"categoryId"`                     // 动态所属类别id 1 => 混厄 2 => 支线 3 => 氛围
	Visible             int    `gorm:"column:visible;NOT NULL" json:"visible"`                            // 是否可见
	IsTemplate          bool   `gorm:"column:is_template;NOT NULL" json:"isTemplate"`                     // 是否是npc模板
	ParentId            int64  `gorm:"column:parent_id;NOT NULL" json:"parentId"`                         // 父动态id
}

func (m *MomentModel) TableName() string {
	return "l50_moment"
}

// 当前moment是否已删除
func (m *MomentModel) IsDeleted() bool {
	return m.DeletedAt > 0
}

// getLikeCount
func (m *MomentModel) GetLikeCount() int64 {
	return m.BaseLikeCount + m.RealLikeCount
}

// 展示的viewCount不能小于点赞数
func (m *MomentModel) GetShowViewCount() int64 {
	likeCount := m.GetLikeCount()
	return max(likeCount, m.ViewCount)
}

// increment like count
func (m *MomentModel) IncrementLikeCount() *MomentModel {
	m.RealLikeCount++
	m.LikeCount++
	return m
}

// decrement like count
func (m *MomentModel) DecrementLikeCount() *MomentModel {
	m.RealLikeCount = max(m.RealLikeCount-1, 0)
	m.LikeCount = max(m.LikeCount-1, 0)
	return m
}

// increment comment count
func (m *MomentModel) IncrementCommentCount() *MomentModel {
	m.CommentCount++
	return m
}

// decrement comment count
func (m *MomentModel) DecrementCommentCount() *MomentModel {
	m.CommentCount = max(m.CommentCount-1, 0)
	return m
}

// getTopicTextList
func (m *MomentModel) GetTopicTextList() []string {
	var topicTextList []string
	re := regexp.MustCompile(TOPIC_REGEX)
	matches := re.FindAllStringSubmatch(m.Text, -1)
	for _, match := range matches {
		topicCharlen := utf8.RuneCountInString(match[1])
		if topicCharlen > MAX_TOPIC_LENGTH {
			continue
		}
		topicTextList = append(topicTextList, match[1])
	}
	return topicTextList
}

func (m *MomentModel) GetImgList() []string {
	imgList := []string{}
	err := json.Unmarshal([]byte(m.ImgList), &imgList)
	if err != nil {
		elog.WithError(err).WithField("moment", m).Error("GetImgListFail")
		return imgList
	}
	return imgList
}

// get videoList
func (m *MomentModel) GetVideoList() []string {
	videoList := []string{}
	err := json.Unmarshal([]byte(m.VideoList), &videoList)
	if err != nil {
		elog.WithError(err).WithField("moment", m).Error("GetVideoListFail")
		return videoList
	}
	return videoList
}

// get VideoCoverList
func (m *MomentModel) GetVideoCoverList() []string {
	videoCoverList := []string{}
	err := json.Unmarshal([]byte(m.VideoCoverList), &videoCoverList)
	if err != nil {
		elog.WithError(err).WithField("moment", m).Error("GetVideoCoverListFail")
		return videoCoverList
	}
	return videoCoverList
}

func (m *MomentModel) IsNpcCreated() bool {
	return IsValidNpcId(m.RoleId)
}

func (m *MomentModel) HasTaskId() bool {
	return m.TaskId > 0
}

func (m *MomentModel) GetShowRoleId() int64 {
	if m.ShowRoleId > 0 {
		return m.ShowRoleId
	}
	return m.RoleId
}
