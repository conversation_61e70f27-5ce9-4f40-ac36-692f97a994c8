package dataimport

import (
	"app/mapper"
	"app/model"
	"app/util"
	"context"
	"errors"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// TuiteImporter handles Tuite sheet import
type TuiteImporter struct {
	importCtx *ImportContext
}

// NewTuiteImporter creates a new Tuite importer
func NewTuiteImporter(importCtx *ImportContext) *TuiteImporter {
	return &TuiteImporter{
		importCtx: importCtx,
	}
}

// GetSheetName returns the sheet name for Tuite import
func (t *TuiteImporter) GetSheetName() string {
	return "Tuite"
}

// ImportSheet imports moment data from the Tuite sheet
func (t *TuiteImporter) ImportSheet(ctx context.Context, file *excelize.File, tx *gorm.DB, result *ImportResult) error {
	stepStartTime := time.Now()
	util.LogWithContext(ctx).Info("startProcessingTuiteSheet")
	errScope := "导入Tuite失败："

	momentRows, err := file.GetRows(t.GetSheetName())
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("getMomentRowsError")
		return err
	}
	util.LogWithContext(ctx).WithField("total_rows", len(momentRows)).Info("tuiteSheetTotalRows")

	if len(momentRows) < 3 {
		result.Errors = append(result.Errors, errScope+"Tuite sheet has less than 3 rows, cannot find header row.")
		return nil
	}

	headerDefinitionRow := momentRows[2]
	headerMap := make(map[string]int)
	for idx, headerName := range headerDefinitionRow {
		headerMap[strings.TrimSpace(headerName)] = idx
	}

	// Define expected headers based on user's image and assumptions for other fields
	expectedHeaders := map[string]string{
		"id":            "Id",
		"roleId":        "Publisher",  // 发布人
		"text":          "Txt",        // 文字内容
		"commentIds":    "Comment",    // 评论
		"categoryId":    "TuiteType",  // 动态分类
		"taskId":        "TaskEvent",  // 关联任务Event
		"parentId":      "PreTuiteID", // 关联父动态
		"isTemplate":    "IsTemplate", // 是否模板动态
		"baseLikeCount": "Likes",      // 初始点赞数
		"viewCount":     "View",       // 浏览量
	}

	for i, row := range momentRows {
		if i < ImportStartSheetRow-1 {
			continue
		}

		// Check if reached data end marker
		if len(row) > 0 && strings.Contains(row[0], SheetDataEndMarker) {
			util.LogWithContext(ctx).WithField("row", i+1).Info("tuiteSheetReachedEndMarker")
			break
		}

		if err := t.processTuiteRow(ctx, tx, result, row, headerMap, expectedHeaders, i+1, errScope); err != nil {
			// Error already handled and logged in processTuiteRow
			continue
		}
	}

	// Calculate total duration for Moments and MomentTopics
	totalMomentStepDuration := time.Since(stepStartTime).Milliseconds()
	result.Moments.DurationMs = totalMomentStepDuration
	result.MomentTopics.DurationMs = totalMomentStepDuration // MomentTopics are processed in Moments step
	util.LogWithContext(ctx).WithField("imported_count", result.Moments.Count).WithField("duration_ms", result.Moments.DurationMs).Info("tuiteSheetProcessingCompleted")
	return nil
}

// processTuiteRow processes a single row from the Tuite sheet
func (t *TuiteImporter) processTuiteRow(ctx context.Context, tx *gorm.DB, result *ImportResult, row []string, headerMap map[string]int, expectedHeaders map[string]string, rowNum int, errScope string) error {
	idStr := row[headerMap[expectedHeaders["id"]]]
	util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", idStr).Debug("startProcessingTuiteRow")

	id, err := CheckId(idStr)
	if err != nil {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", idStr).WithError(err).Error("tuiteIdParsingFailed")
		result.Errors = append(result.Errors, errScope+"Invalid Tuite ID at row "+strconv.Itoa(rowNum))
		return err
	}
	if id == -1 {
		util.LogWithContext(ctx).WithField("row", rowNum).Debug("tuiteRowIsEmpty")
		return nil
	}
	if id == 0 {
		util.LogWithContext(ctx).WithField("row", rowNum).Debug("tuiteRowSkippedWithTilde")
		return nil
	}

	roleIdStr := row[headerMap[expectedHeaders["roleId"]]]
	if roleIdStr == "" {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).Error("tuiteRoleIdCannotBeEmpty")
		result.Errors = append(result.Errors, errScope+"Tuite roleId cannot be empty at row "+strconv.Itoa(rowNum))
		return errors.New("roleId cannot be empty")
	}

	roleId, err := strconv.ParseInt(roleIdStr, 10, 64)
	if err != nil {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("roleId", roleIdStr).WithError(err).Error("tuiteRoleIdParsingFailed")
		result.Errors = append(result.Errors, errScope+"Invalid Tuite roleId at row "+strconv.Itoa(rowNum))
		return err
	}

	text := row[headerMap[expectedHeaders["text"]]]
	commentIdsStr := row[headerMap[expectedHeaders["commentIds"]]]

	// Calculate comment count from commentIds string
	commentCnt := int64(GetArrayLenFromColumn(commentIdsStr))

	categoryIdStr := row[headerMap[expectedHeaders["categoryId"]]]
	categoryId, err := strconv.ParseInt(categoryIdStr, 10, 64)
	if err != nil {
		categoryId = 0 // Default category
	}

	taskIdStr := row[headerMap[expectedHeaders["taskId"]]]
	taskId, err := strconv.ParseInt(taskIdStr, 10, 64)
	if err != nil {
		taskId = 0 // Default no task
	}

	parentIdStr := row[headerMap[expectedHeaders["parentId"]]]
	parentId, err := strconv.ParseInt(parentIdStr, 10, 64)
	if err != nil {
		parentId = 0 // Default no parent moment
	}

	isTemplate, err := strconv.ParseBool(row[headerMap[expectedHeaders["isTemplate"]]])
	if err != nil {
		isTemplate = false // Default not template
	}

	// Parse Like count (BaseLikeCount)
	var baseLikeCount int64
	likeHeaderName := expectedHeaders["baseLikeCount"]
	if likeCol, headerFound := headerMap[likeHeaderName]; headerFound {
		if likeCol < len(row) {
			likeStr := strings.TrimSpace(row[likeCol])
			if likeStr != "" {
				baseLikeCount, err = strconv.ParseInt(likeStr, 10, 64)
				if err != nil {
					baseLikeCount = 0 // Default to 0 if parsing fails
				}
			}
		}
	}

	// Parse View count
	var viewCount int64
	viewHeaderName := expectedHeaders["viewCount"]
	if viewCol, headerFound := headerMap[viewHeaderName]; headerFound {
		if viewCol < len(row) {
			viewStr := strings.TrimSpace(row[viewCol])
			if viewStr != "" {
				viewCount, err = strconv.ParseInt(viewStr, 10, 64)
				if err != nil {
					viewCount = 0 // Default to 0 if parsing fails
				}
			}
		}
	}

	util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithField("roleId", roleId).WithField("categoryId", categoryId).WithField("taskId", taskId).WithField("parentId", parentId).WithField("isTemplate", isTemplate).WithField("baseLikeCount", baseLikeCount).WithField("viewCount", viewCount).WithField("text_length", len(text)).WithField("commentIds", commentIdsStr).Debug("tuiteFieldParsingCompleted")

	// Validate topic references in text <a socialHot=1>
	if err := t.validateTopicReferences(ctx, tx, text, rowNum, errScope, result); err != nil {
		return err
	}

	if id >= 100000000 {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).Error("momentIdCannotExceedLimit")
		result.Errors = append(result.Errors, errScope+"Moment ID cant >= 100000000")
		return errors.New("moment ID exceeds limit")
	}

	// Create or update moment
	moment, err := t.createOrUpdateMoment(ctx, tx, id, roleId, text, categoryId, taskId, parentId, isTemplate, commentCnt, baseLikeCount, viewCount, rowNum, errScope, result)
	if err != nil {
		return err
	}

	// Process topic relations
	if err := t.processMomentTopicRelations(ctx, tx, moment, rowNum, errScope, result); err != nil {
		return err
	}

	// Process comment relations
	if err := t.processMomentCommentRelations(ctx, tx, moment, commentIdsStr, roleId, rowNum, errScope, result); err != nil {
		return err
	}

	result.Moments.Count++
	return nil
}

// validateTopicReferences validates topic references in the text
func (t *TuiteImporter) validateTopicReferences(ctx context.Context, tx *gorm.DB, text string, rowNum int, errScope string, result *ImportResult) error {
	topicPattern := regexp.MustCompile(`<a\s+socialHot=(\d+)\s*>`)
	matches := topicPattern.FindAllStringSubmatch(text, -1)
	for _, match := range matches {
		if len(match) >= 2 {
			topicId, err := strconv.ParseInt(match[1], 10, 64)
			if err != nil {
				result.Errors = append(result.Errors, errScope+"Invalid topic ID in text at row "+strconv.Itoa(rowNum))
				return err
			}

			// Verify topic exists
			var topic model.TopicModel
			if err := tx.Where("id = ?", topicId).First(&topic).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					result.Errors = append(result.Errors, errScope+"Topic ID "+match[1]+" referenced in text not found at row "+strconv.Itoa(rowNum))
					return err
				}
			}
		}
	}
	return nil
}

// createOrUpdateMoment creates or updates a moment record
func (t *TuiteImporter) createOrUpdateMoment(ctx context.Context, tx *gorm.DB, id, roleId int64, text string, categoryId, taskId, parentId int64, isTemplate bool, commentCnt, baseLikeCount, viewCount int64, rowNum int, errScope string, result *ImportResult) (*model.MomentModel, error) {
	util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).Debug("preparingToSaveMomentData")

	// Calculate visibility based on template status
	visible := 1
	if isTemplate {
		visible = 0
	}

	// Try to find existing moment
	var moment model.MomentModel
	err := tx.Unscoped().Where("id = ?", id).First(&moment).Error
	isNewMoment := errors.Is(err, gorm.ErrRecordNotFound)

	// Handle database query errors (excluding not found)
	if err != nil && !isNewMoment {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithError(err).Error("momentDatabaseQueryError")
		result.Errors = append(result.Errors, errScope+"Database error for Moment at row "+strconv.Itoa(rowNum)+": "+err.Error())
		return nil, err
	}

	// Populate moment data
	t.populateMomentData(&moment, id, roleId, text, categoryId, taskId, parentId, isTemplate, commentCnt, baseLikeCount, viewCount, visible)

	// Save moment (create or update)
	var saveErr error
	var operation string
	saveErr = tx.Save(&moment).Error

	// Handle save errors
	if saveErr != nil {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithError(saveErr).Error(operation + "MomentFailed")
		result.Errors = append(result.Errors, errScope+"Failed to "+operation+" Moment at row "+strconv.Itoa(rowNum)+": "+saveErr.Error())
		return nil, saveErr
	}

	// Log success
	var successMsg string
	if isNewMoment {
		successMsg = "momentCreatedSuccessfully"
	} else {
		successMsg = "momentUpdatedSuccessfully"
	}
	util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithField("roleId", roleId).Info(successMsg)
	return &moment, nil
}

// populateMomentData populates moment fields with provided data
func (t *TuiteImporter) populateMomentData(moment *model.MomentModel, id, roleId int64, text string, categoryId, taskId, parentId int64, isTemplate bool, commentCnt, baseLikeCount, viewCount int64, visible int) {
	moment.ID = id
	moment.RoleId = roleId
	moment.ShowRoleId = roleId
	moment.Text = text
	moment.CategoryId = int(categoryId)
	moment.TaskId = int(taskId)
	moment.ParentId = parentId
	moment.IsTemplate = isTemplate
	moment.CommentCount = commentCnt
	moment.BaseLikeCount = baseLikeCount
	moment.ViewCount = viewCount
	moment.Visible = visible
	moment.DeletedAt = 0
}

// processMomentTopicRelations processes topic relations for a moment
func (t *TuiteImporter) processMomentTopicRelations(ctx context.Context, tx *gorm.DB, moment *model.MomentModel, rowNum int, errScope string, result *ImportResult) error {
	// Extract topic IDs directly from text using TopicMapper
	topicMapper := mapper.NewTopicMapper()
	topicIds := topicMapper.ExtractTopicIds(moment.Text)

	util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", moment.ID).WithField("topic_count", len(topicIds)).WithField("topic_ids", topicIds).Debug("processingMomentTopicRelations")

	if len(topicIds) > 0 {
		// Convert int to int64 for database operations
		var topicIdsInt64 []int64
		for _, topicId := range topicIds {
			topicIdsInt64 = append(topicIdsInt64, int64(topicId))
		}

		// Create moment-topic relations
		// First delete all topic relations for this moment (clean old relations when updating)
		if err := tx.Where("moment_id = ?", moment.ID).Delete(&model.MomentTopicModel{}).Error; err != nil {
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("moment_id", moment.ID).WithError(err).Error("clearExistingTopicRelationsFailed")
			result.Errors = append(result.Errors, errScope+"Failed to clear existing topic relations for Moment at row "+strconv.Itoa(rowNum)+": "+err.Error())
			return err
		}

		// Create new relation records
		var momentTopicList []model.MomentTopicModel
		for _, topicId := range topicIdsInt64 {
			momentTopicList = append(momentTopicList, model.MomentTopicModel{
				MomentId:      moment.ID,
				MomentRoleId:  moment.RoleId,
				MomentVisible: moment.Visible,
				TopicId:       topicId,
			})
		}

		if len(momentTopicList) > 0 {
			// Use upsert to prevent unique index conflicts
			if err := tx.Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "moment_id"}, {Name: "topic_id"}},
				DoUpdates: clause.Assignments(map[string]interface{}{
					"moment_role_id": gorm.Expr("VALUES(moment_role_id)"),
					"moment_visible": gorm.Expr("VALUES(moment_visible)"),
					"deleted_at":     gorm.Expr("VALUES(deleted_at)"),
				}),
			}).Create(&momentTopicList).Error; err != nil {
				util.LogWithContext(ctx).WithField("row", rowNum).WithField("moment_id", moment.ID).WithField("topic_count", len(momentTopicList)).WithError(err).Error("createTopicRelationsFailed")
				result.Errors = append(result.Errors, errScope+"Failed to create topic relations for Moment at row "+strconv.Itoa(rowNum)+": "+err.Error())
				return err
			}
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("moment_id", moment.ID).WithField("topic_count", len(momentTopicList)).WithField("topic_ids", topicIdsInt64).Info("topicRelationsCreatedSuccessfully")
			result.MomentTopics.Count += len(momentTopicList)
		}
	}
	return nil
}

// processMomentCommentRelations processes comment relations for a moment
func (t *TuiteImporter) processMomentCommentRelations(ctx context.Context, tx *gorm.DB, moment *model.MomentModel, commentIdsStr string, roleId int64, rowNum int, errScope string, result *ImportResult) error {
	if commentIdsStr != "" {
		commentIds := strings.Split(commentIdsStr, ";")
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("moment_id", moment.ID).WithField("comment_count", len(commentIds)).WithField("comment_ids", commentIds).Debug("processingMomentCommentRelations")

		for _, commentIdStr := range commentIds {
			commentIdStr = strings.TrimSpace(commentIdStr)
			if commentIdStr == "" {
				continue
			}

			commentId, err := strconv.ParseInt(commentIdStr, 10, 64)
			if err != nil {
				util.LogWithContext(ctx).WithField("row", rowNum).WithField("comment_id_str", commentIdStr).WithError(err).Error("commentIdParsingFailedInMoment")
				result.Errors = append(result.Errors, errScope+"Invalid Comment ID "+commentIdStr+" in Moment at row "+strconv.Itoa(rowNum))
				continue
			}

			util.LogWithContext(ctx).WithField("row", rowNum).WithField("moment_id", moment.ID).WithField("comment_id", commentId).Debug("lookingUpCommentForRelation")
			var comment model.CommentModel
			if err := tx.Where("id = ?", commentId).First(&comment).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					util.LogWithContext(ctx).WithField("row", rowNum).WithField("comment_id", commentId).Warn("commentNotFoundForMoment")
					result.Warns = append(result.Warns, "Comment ID "+commentIdStr+" not found for Moment at row "+strconv.Itoa(rowNum))
				} else {
					util.LogWithContext(ctx).WithField("row", rowNum).WithField("comment_id", commentId).WithError(err).Error("commentDatabaseQueryErrorForMoment")
					result.Errors = append(result.Errors, errScope+"Database error for Comment "+commentIdStr+" at row "+strconv.Itoa(rowNum)+": "+err.Error())
				}
				continue
			}
			comment.MomentId = moment.ID
			comment.MomentRoleId = roleId
			if err := tx.Save(&comment).Error; err != nil {
				util.LogWithContext(ctx).WithField("row", rowNum).WithField("comment_id", commentId).WithError(err).Error("updateCommentRelationFailed")
				result.Errors = append(result.Errors, errScope+"Failed to update Comment "+commentIdStr+" for Moment at row "+strconv.Itoa(rowNum)+": "+err.Error())
				continue
			}
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("moment_id", moment.ID).WithField("comment_id", commentId).Debug("commentRelatedToMomentSuccessfully")
		}
	}
	return nil
}
