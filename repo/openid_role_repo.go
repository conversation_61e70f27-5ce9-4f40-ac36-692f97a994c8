package repo

import (
	"app/model"
	"context"

	"gorm.io/gorm"
)

type OpenIdRoleRepo struct {
	*BaseRepo[model.OpenIdRoleModel]
}

func NewOpenIdRoleRepo(db *gorm.DB) *OpenIdRoleRepo {
	return &OpenIdRoleRepo{
		BaseRepo: NewBaseRepo[model.OpenIdRoleModel](db),
	}
}

func (r *OpenIdRoleRepo) GetByOpenIdAndScope(ctx context.Context, openId string, scope model.AdminScope) (model.OpenIdRoleModel, error) {
	var role model.OpenIdRoleModel
	err := r.DB.Where("open_id = ? and scope = ?", openId, scope).First(&role).Error
	if err != nil {
		return role, err
	}
	return role, nil
}

func (r *OpenIdRoleRepo) CheckOpenIdPermission(ctx context.Context, openId string, scope model.AdminScope, needAdmin bool) (bool, error) {
	role, err := r.GetByOpenIdAndScope(ctx, openId, scope)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		return false, err
	}
	if needAdmin {
		return role.RoleType == model.RoleTypeAdmin, nil
	} else {
		return role.ID > 0, nil
	}
}
