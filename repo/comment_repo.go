package repo

import (
	"app/api"
	"app/model"
	"app/schema"
	"app/util"
	"context"
	"errors"

	"gorm.io/gorm"
)

type CommentRepo struct {
	*BaseRepo[model.CommentModel]
}

func NewCommentRepo(db *gorm.DB) *CommentRepo {
	return &CommentRepo{
		BaseRepo: NewBaseRepo[model.CommentModel](db),
	}
}

func (r *CommentRepo) DeleteByRoleId(ctx context.Context, roleId int64) (int64, error) {
	return r.DeleteBy<PERSON>uery(ctx, "role_id = ?", roleId)
}

func (r *CommentRepo) GetListByMoment(ctx context.Context, moment model.MomentModel, pagination schema.Pagination) ([]model.CommentModel, error) {
	momentIds := []int64{moment.ID}
	if moment.TemplateId > 0 {
		momentIds = append(momentIds, moment.TemplateId)
	}
	query := r.<PERSON>(ctx).Where("moment_id in ?", momentIds).Order("like_count desc").Order("id desc")
	rows, err := r.FindWithPagination(ctx, query, pagination)
	return rows, err
}

func (r *CommentRepo) GetCountByMomentId(ctx context.Context, moment model.MomentModel) (int64, error) {
	momentIds := []int64{moment.ID}
	if moment.TemplateId > 0 {
		momentIds = append(momentIds, moment.TemplateId)
	}
	query := r.Scope(ctx).Where("moment_id in ?", momentIds)
	return r.CountByQuery(ctx, query)
}

func (r *CommentRepo) FindCommentById(ctx context.Context, commentId int64) (model.CommentModel, error) {
	var m model.CommentModel
	m, err := r.FindOne(ctx, "id = ?", commentId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return m, api.GetBizErr(api.ErrBizCommentNotFound)
		}
	}
	return m, err
}

func (r *CommentRepo) FindUserCommentById(ctx context.Context, roleId int64, commentId int64) (model.CommentModel, error) {
	var m model.CommentModel
	m, err := r.FindOne(ctx, "role_id = ? AND id = ?", roleId, commentId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return m, api.GetBizErr(api.ErrBizCommentNotFound)
		}
	}
	return m, err
}

// increment like count
func (r *CommentRepo) IncrementLikeCount(ctx context.Context, comment model.CommentModel) (int64, error) {
	c := comment.IncrementLikeCount()
	err := r.DB.Model(&c).Updates(map[string]any{"like_count": c.LikeCount, "real_like_count": c.RealLikeCount}).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("comment", comment).Error("IncrementLikeCountFail")
		return 0, err
	}
	return c.GetLikeCount(), nil
}

// decrement like count
func (r *CommentRepo) DecrementLikeCount(ctx context.Context, comment model.CommentModel) (int64, error) {
	c := comment.DecrementLikeCount()
	err := r.DB.Model(&c).Updates(map[string]any{"like_count": c.LikeCount, "real_like_count": c.RealLikeCount}).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("comment", comment).Error("DecrementLikeCountFail")
		return 0, err
	}
	return c.GetLikeCount(), nil
}
