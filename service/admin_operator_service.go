package service

import (
	"app/api"
	"app/model"
	"app/repo"
	"app/schema"
	"app/util"
	"errors"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/login"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

type AdminOperatorService struct {
	operatorRepo *repo.OpenIdRoleRepo
}

func NewAdminOperatorService(openIdRoleRepo *repo.OpenIdRoleRepo) *AdminOperatorService {
	return &AdminOperatorService{
		operatorRepo: openIdRoleRepo,
	}
}

func (s *AdminOperatorService) AddOperator(ctx *gin.Context, req *schema.AdminOperatorAddReq) (resp *schema.AdminOperatorAddResp, err error) {
	r, err := s.operatorRepo.GetByOpenIdAndScope(ctx, req.OpenId, model.AdminNpcScope)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// continue
		} else {
			return nil, err
		}
	}
	if r.ID > 0 {
		return nil, api.GetBizErr(api.ErrAdminOperatorExist)
	}
	newR, err := s.operatorRepo.Create(ctx, &model.OpenIdRoleModel{
		BaseModel: model.BaseModel{},
		OpenId:    req.OpenId,
		FullName:  req.FullName,
		RoleType:  model.AdminRoleType(req.RoleType),
		Scope:     model.AdminNpcScope,
	})
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("openId", req.OpenId).WithField("roleType", req.RoleType).Error("AddAdminOperator")
		return nil, err
	}
	resp = &schema.AdminOperatorAddResp{
		Id:         newR.ID,
		CreateTime: newR.Ctime,
	}
	return resp, nil
}

func (s *AdminOperatorService) DeleteOperator(ctx *gin.Context, openId string) (resp *schema.AdminOperatorDeleteResp, err error) {
	r, err := s.operatorRepo.GetByOpenIdAndScope(ctx, openId, model.AdminNpcScope)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, api.GetBizErr(api.ErrAdminOperatorNotExist)
		}
		return nil, err
	}
	delR, err := s.operatorRepo.DeleteByID(ctx, r.ID)
	if err != nil {
		return nil, err
	}
	resp = &schema.AdminOperatorDeleteResp{
		Id: delR.ID,
	}
	return resp, nil
}

func (s *AdminOperatorService) UpdateOperator(ctx *gin.Context, req *schema.AdminOperatorUpdateReq) (resp *schema.AdminOperatorUpdateResp, err error) {
	r, err := s.operatorRepo.GetByOpenIdAndScope(ctx, req.OpenId, model.AdminNpcScope)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, api.GetBizErr(api.ErrAdminOperatorNotExist)
		}
		return nil, err
	}
	_, err = s.operatorRepo.UpdateById(ctx, r.ID, map[string]any{
		"RoleType": model.AdminRoleType(req.RoleType),
		"FullName": req.FullName,
	})
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("openId", req.OpenId).WithField("roleType", req.RoleType).Error("UpdateAdminOperatorError")
		return nil, err
	}
	resp = &schema.AdminOperatorUpdateResp{
		Id: r.ID,
	}
	return resp, nil
}

func (s *AdminOperatorService) GetOperatorList(ctx *gin.Context, req *schema.AdminOperatorListReq) (resp *schema.AdminOperatorListResp, err error) {
	resp = &schema.AdminOperatorListResp{
		Total: 0,
		List:  []schema.AdminOperator{},
	}
	baseQuery := func() *gorm.DB {
		query := s.operatorRepo.Scope(ctx).Where("scope = ?", model.AdminNpcScope)
		return query
	}
	rows, err := s.operatorRepo.FindWithPagination(ctx, baseQuery(), schema.Pagination{
		Page:     req.Page,
		PageSize: req.PageSize,
	})
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("GetAdminOperatorListFail")
		return nil, err
	}
	listQuery := baseQuery().Order("id desc")
	total, err := s.operatorRepo.CountByQuery(ctx, listQuery)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("GetAdminOperatorListCountFail")
		return nil, err
	}
	resp.Total = total
	list := lo.Map(rows, func(r model.OpenIdRoleModel, _ int) schema.AdminOperator {
		return schema.AdminOperator{
			AdminOperatorAddReq: schema.AdminOperatorAddReq{
				OpenId:   r.OpenId,
				RoleType: int8(r.RoleType),
				FullName: r.FullName,
			},
			Id:         r.ID,
			CreateTime: r.Ctime,
		}
	})
	resp.List = list
	return resp, nil
}

func (s *AdminOperatorService) CheckIsAdmin(ctx *gin.Context, openId string, scope model.AdminScope) bool {
	r, err := s.operatorRepo.GetByOpenIdAndScope(ctx, openId, model.AdminNpcScope)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false
		}
		util.LogWithContext(ctx).WithError(err).WithField("openId", openId).Error("CheckIsAdminFail")
		return false
	}
	return r.RoleType == model.RoleTypeAdmin
}

func (s *AdminOperatorService) GetLoginInfo(ctx *gin.Context, loginInfo login.CorpInfo) (resp *schema.AdminAuthLoginInfoResp, err error) {
	r, err := s.operatorRepo.GetByOpenIdAndScope(ctx, loginInfo.CorpMail, model.AdminNpcScope)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, api.GetBizErr(api.ErrAdminOperatorNotExist)
		}
		return nil, err
	}
	resp = &schema.AdminAuthLoginInfoResp{
		Id:         r.ID,
		OpenId:     r.OpenId,
		CreateTime: r.Ctime,
		RoleType:   int8(r.RoleType),
	}
	return resp, nil
}

func (s *AdminOperatorService) CheckWritePermission(ctx *gin.Context, openId string, scope model.AdminScope) bool {
	r, err := s.operatorRepo.GetByOpenIdAndScope(ctx, openId, model.AdminNpcScope)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false
		}
		util.LogWithContext(ctx).WithError(err).WithField("openId", openId).Error("CheckHasWritePermissionFail")
		return false
	}
	return r.RoleType == model.RoleTypeAdmin || r.RoleType == model.RoleTypeNormal
}
