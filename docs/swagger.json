{"swagger": "2.0", "info": {"description": "L50社媒API", "title": "L50社媒API", "contact": {"name": "hzwangzhenhua", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0"}, "basePath": "/l50/social_media", "paths": {"/admin/assets/avatars/list": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_asserts"], "summary": "获取头像列表", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/schema.AvatarItem"}}}}}, "/admin/auth/login_info": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_operator"], "summary": "获取登录用户信息", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.AdminAuthLoginInfoResp"}}}]}}}}}, "/admin/common/export_to_excel": {"get": {"tags": ["admin_common"], "summary": "导出Excel", "responses": {}}}, "/admin/common/get_fp_token": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_common"], "summary": "获取FP Token", "parameters": [{"enum": ["image", "video"], "type": "string", "description": "获取token类型", "name": "type", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.GetFpTokenResp"}}}]}}}}}, "/admin/common/import": {"post": {"tags": ["admin_common"], "summary": "导入配置", "responses": {}}}, "/admin/npc/add": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc"], "summary": "新增NPC", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.NpcAddReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcAddResp"}}}]}}}}}, "/admin/npc/comment/add": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc_comment"], "summary": "新增NPC评论", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.NpcCommentAddReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcCommentAddResp"}}}]}}}}}, "/admin/npc/comment/delete": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc_comment"], "summary": "删除NPC评论", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.NpcCommentDeleteReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.BaseDeleteResp"}}}]}}}}}, "/admin/npc/comment/list": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc_comment"], "summary": "获取NPC评论列表", "parameters": [{"type": "integer", "description": "动态id", "name": "momentId", "in": "query"}, {"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 20, "minimum": 1, "type": "integer", "name": "pageSize", "in": "query"}, {"enum": ["all", "npc", "player"], "type": "string", "name": "roleType", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcCommentListResp"}}}]}}}}}, "/admin/npc/comment/update": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc_comment"], "summary": "更新NPC评论数据", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.NpcCommentUpdateReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.BaseUpdateResp"}}}]}}}}}, "/admin/npc/delete": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc"], "summary": "删除NPC", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.NpcDeleteReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcDeleteResp"}}}]}}}}}, "/admin/npc/list": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc"], "summary": "获取NPC列表", "parameters": [{"type": "string", "description": "搜索关键字， 支持roleId和roleName", "name": "kw", "in": "query"}, {"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "description": "管理后台分页数量最大限制为100", "name": "pageSize", "in": "query"}, {"type": "integer", "description": "npc 角色Id,唯一,不可重复", "name": "roleId", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcListResp"}}}]}}}}}, "/admin/npc/moment/add": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc_moment"], "summary": "新增NPC动态", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.NpcMomentAddReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcMomentAddResp"}}}]}}}}}, "/admin/npc/moment/delete": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc_moment"], "summary": "删除NPC动态", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.NpcMomentDeleteReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcMomentDeleteResp"}}}]}}}}}, "/admin/npc/moment/filters": {"get": {"produces": ["application/json"], "tags": ["admin_npc_moment"], "summary": "获取NPC动态列表过滤器", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/schema.NpcMomentListFilterResp"}}}}}, "/admin/npc/moment/list": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc_moment"], "summary": "获取NPC动态列表", "parameters": [{"type": "integer", "description": "动态所属地区id", "name": "areaId", "in": "query"}, {"type": "integer", "description": "动态所属类别id", "name": "categoryId", "in": "query"}, {"type": "boolean", "description": "是否有任务", "name": "hasTask", "in": "query"}, {"type": "boolean", "name": "onlyShowNpc", "in": "query"}, {"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "description": "管理后台分页数量最大限制为100", "name": "pageSize", "in": "query"}, {"type": "integer", "description": "动态所属npcId", "name": "roleId", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcMomentListResp"}}}]}}}}}, "/admin/npc/moment/show": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc_moment"], "summary": "查看单个npc动态", "parameters": [{"type": "integer", "description": "动态id", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcMomentShowResp"}}}]}}}}}, "/admin/npc/moment/sub_list": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc_moment"], "summary": "获取NPC子动态列表", "parameters": [{"type": "integer", "description": "动态所属地区id", "name": "areaId", "in": "query"}, {"type": "integer", "description": "动态所属类别id", "name": "categoryId", "in": "query"}, {"type": "boolean", "description": "是否有任务", "name": "hasTask", "in": "query"}, {"type": "boolean", "name": "onlyShowNpc", "in": "query"}, {"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "description": "管理后台分页数量最大限制为100", "name": "pageSize", "in": "query"}, {"type": "integer", "description": "动态所属npcId", "name": "roleId", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcMomentListResp"}}}]}}}}}, "/admin/npc/moment/top": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc_moment"], "summary": "设置NPC动态置顶", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.NpcMomentTopReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcMomentTopResp"}}}]}}}}}, "/admin/npc/moment/untop": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc_moment"], "summary": "设置NPC动态取消置顶", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.NpcMomentTopReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcMomentTopResp"}}}]}}}}}, "/admin/npc/moment/update": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc_moment"], "summary": "更新NPC动态", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.NpcMomentUpdateReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcMomentUpdateResp"}}}]}}}}}, "/admin/npc/show": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc"], "summary": "查看单个npc", "parameters": [{"type": "string", "description": "roleId", "name": "roleId", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcShow"}}}]}}}}}, "/admin/npc/update": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_npc"], "summary": "更新NPC", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.NpcUpdateReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.NpcUpdateResp"}}}]}}}}}, "/admin/operator/add": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_operator"], "summary": "新增用户", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.AdminOperatorAddReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.AdminOperatorAddResp"}}}]}}}}}, "/admin/operator/del": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_operator"], "summary": "删除用户", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.AdminOperatorDeleteReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.BaseDeleteResp"}}}]}}}}}, "/admin/operator/list": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_operator"], "summary": "获取用户列表", "parameters": [{"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "description": "管理后台分页数量最大限制为100", "name": "pageSize", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.AdminOperatorListResp"}}}]}}}}}, "/admin/operator/update": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin_operator"], "summary": "更新用户", "parameters": [{"description": "Request body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.AdminOperatorUpdateReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.AdminOperatorUpdateResp"}}}]}}}}}, "/api/comment/add": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["comment"], "summary": "添加评论", "parameters": [{"description": "body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.CommentAddReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommentAddResp"}}}]}}}}}, "/api/comment/cancel_like": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["comment"], "summary": "取消点赞评论", "parameters": [{"description": "body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.CommentLikeReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommentLikeResp"}}}]}}}}}, "/api/comment/delete": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["comment"], "summary": "删除评论", "parameters": [{"description": "body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.CommentDeleteReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommentDeleteResp"}}}]}}}}}, "/api/comment/like": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["comment"], "summary": "点赞评论", "parameters": [{"description": "body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.CommentLikeReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommentLikeResp"}}}]}}}}}, "/api/comment/list": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["comment"], "summary": "获取评论列表", "parameters": [{"type": "integer", "name": "momentId", "in": "query", "required": true}, {"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 20, "minimum": 1, "type": "integer", "name": "pageSize", "in": "query"}, {"type": "integer", "description": "玩家角色id", "name": "roleId", "in": "query", "required": true}, {"type": "string", "description": "玩家授权skey", "name": "skey", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommentListResp"}}}]}}}}}, "/api/follow/add": {"post": {"description": "关注用户", "tags": ["follow"], "summary": "关注用户", "parameters": [{"description": "req", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.FollowReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.FollowResp"}}}]}}}}}, "/api/follow/delete": {"post": {"description": "取消关注用户", "tags": ["follow"], "summary": "取消关注用户", "parameters": [{"description": "req", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.FollowReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.FollowResp"}}}]}}}}}, "/api/follow/fans_list": {"get": {"description": "获取粉丝列表", "tags": ["follow"], "summary": "获取粉丝列表", "parameters": [{"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 20, "minimum": 1, "type": "integer", "name": "pageSize", "in": "query"}, {"type": "integer", "description": "玩家角色id", "name": "roleId", "in": "query", "required": true}, {"type": "string", "description": "玩家授权skey", "name": "skey", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.FanListResp"}}}]}}}}}, "/api/follow/follow_list": {"get": {"description": "获取关注列表", "tags": ["follow"], "summary": "获取关注列表", "parameters": [{"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 20, "minimum": 1, "type": "integer", "name": "pageSize", "in": "query"}, {"type": "integer", "description": "玩家角色id", "name": "roleId", "in": "query", "required": true}, {"type": "string", "description": "玩家授权skey", "name": "skey", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.FollowListResp"}}}]}}}}}, "/api/moment/cancel_collect": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["moment_collect"], "summary": "取消收藏动态", "parameters": [{"description": "req", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.MomentCollectReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.MomentActionResp"}}}]}}}}}, "/api/moment/cancel_like": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["moment_like"], "summary": "取消点赞动态", "parameters": [{"description": "req", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.MomentLikeReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.MomentActionResp"}}}]}}}}}, "/api/moment/collect": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["moment_collect"], "summary": "收藏动态", "parameters": [{"description": "req", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.MomentCollectReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.MomentActionResp"}}}]}}}}}, "/api/moment/collect_list": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["moment_collect"], "summary": "获取动态收藏列表", "parameters": [{"maximum": 4, "minimum": 0, "type": "integer", "description": "动态分类id  0-所有 1-默认 2-混厄 3-支线 4-氛围", "name": "categoryId", "in": "query", "required": true}, {"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 20, "minimum": 1, "type": "integer", "name": "pageSize", "in": "query"}, {"type": "integer", "description": "玩家角色id", "name": "roleId", "in": "query", "required": true}, {"type": "string", "description": "玩家授权skey", "name": "skey", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.MomentCollectListResp"}}}]}}}}}, "/api/moment/detail": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["moment"], "summary": "动态-获取详情", "parameters": [{"type": "integer", "description": "动态id", "name": "momentId", "in": "query"}, {"type": "integer", "description": "玩家角色id", "name": "roleId", "in": "query", "required": true}, {"type": "string", "description": "玩家授权skey", "name": "skey", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.MomentShow"}}}]}}}}}, "/api/moment/follow_list": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["moment"], "summary": "动态-获取关注的动态列表", "parameters": [{"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 20, "minimum": 1, "type": "integer", "name": "pageSize", "in": "query"}, {"type": "integer", "description": "玩家角色id", "name": "roleId", "in": "query", "required": true}, {"type": "string", "description": "玩家授权skey", "name": "skey", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.MomentListResp"}}}]}}}}}, "/api/moment/like": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["moment_like"], "summary": "点赞动态", "parameters": [{"description": "req", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.MomentLikeReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.MomentActionResp"}}}]}}}}}, "/api/moment/list_by_topic": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["moment"], "summary": "动态-话题下动态列表", "parameters": [{"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 20, "minimum": 1, "type": "integer", "name": "pageSize", "in": "query"}, {"type": "integer", "description": "玩家角色id", "name": "roleId", "in": "query", "required": true}, {"type": "string", "description": "玩家授权skey", "name": "skey", "in": "query", "required": true}, {"type": "integer", "name": "topicId", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.MomentListResp"}}}]}}}}}, "/api/moment/recc_list": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["moment"], "summary": "动态-获取推荐的动态列表", "parameters": [{"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 20, "minimum": 1, "type": "integer", "name": "pageSize", "in": "query"}, {"type": "integer", "description": "玩家角色id", "name": "roleId", "in": "query", "required": true}, {"type": "string", "description": "玩家授权skey", "name": "skey", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.MomentListResp"}}}]}}}}}, "/api/server/moment/add_by_npc": {"post": {"description": "新增npc发送给玩家的动态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["moment"], "summary": "动态-新增npc发送给玩家的动态", "parameters": [{"description": "req", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.MomentAddByNpcReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.MomentAddByNpcResp"}}}]}}}}}, "/api/server/moment/add_by_template": {"post": {"security": [], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["moment"], "summary": "动态-服务器通过模板新增动态", "parameters": [{"description": "req", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.MomentAddByTemplateReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.MomentAddByTemplateResp"}}}]}}}}}, "/api/trend/list": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["trend"], "summary": "获取热门趋势话题", "parameters": [{"type": "integer", "name": "areaId", "in": "query"}, {"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 20, "minimum": 1, "type": "integer", "name": "pageSize", "in": "query"}, {"type": "integer", "description": "玩家角色id", "name": "roleId", "in": "query", "required": true}, {"type": "string", "description": "玩家授权skey", "name": "skey", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.TrendListResp"}}}]}}}}}, "/api/trend/random": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["trend"], "summary": "获取随机热门趋势话题", "parameters": [{"type": "integer", "name": "areaId", "in": "query"}, {"minimum": 1, "type": "integer", "name": "page", "in": "query"}, {"maximum": 20, "minimum": 1, "type": "integer", "name": "pageSize", "in": "query"}, {"type": "integer", "description": "玩家角色id", "name": "roleId", "in": "query", "required": true}, {"type": "string", "description": "玩家授权skey", "name": "skey", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.TrendListResp"}}}]}}}}}, "/api/user/profile": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "获取用户详情", "parameters": [{"type": "integer", "description": "玩家角色id", "name": "roleId", "in": "query", "required": true}, {"type": "string", "description": "玩家授权skey", "name": "skey", "in": "query", "required": true}, {"type": "integer", "description": "查看的角色id", "name": "viewRoleId", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/api.RespBody"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.UserProfileResp"}}}]}}}}}, "/web/tuite_upload": {"get": {"tags": ["web"], "summary": "Tuite上传页面", "responses": {}}}}, "definitions": {"api.RespBody": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "msg": {"type": "string"}}}, "schema.AdminAuthLoginInfoResp": {"type": "object", "properties": {"createTime": {"type": "integer"}, "id": {"type": "integer"}, "openId": {"type": "string"}, "roleType": {"type": "integer"}}}, "schema.AdminOperator": {"type": "object", "properties": {"createTime": {"description": "创建时间", "type": "integer"}, "fullName": {"description": "全名", "type": "string"}, "id": {"description": "id", "type": "integer"}, "openId": {"description": "openId", "type": "string"}, "roleType": {"description": "角色类型 0 普通运营  1 管理员  2 只读账号", "type": "integer", "enum": [0, 1, 2]}}}, "schema.AdminOperatorAddReq": {"type": "object", "properties": {"fullName": {"description": "全名", "type": "string"}, "openId": {"description": "openId", "type": "string"}, "roleType": {"description": "角色类型 0 普通运营  1 管理员  2 只读账号", "type": "integer", "enum": [0, 1, 2]}}}, "schema.AdminOperatorAddResp": {"type": "object", "properties": {"createTime": {"description": "创建时间", "type": "integer"}, "id": {"description": "id", "type": "integer"}}}, "schema.AdminOperatorDeleteReq": {"type": "object", "properties": {"openId": {"description": "openId", "type": "string"}}}, "schema.AdminOperatorListResp": {"type": "object", "properties": {"list": {"description": "列表", "type": "array", "items": {"$ref": "#/definitions/schema.AdminOperator"}}, "total": {"description": "总数", "type": "integer"}}}, "schema.AdminOperatorUpdateReq": {"type": "object", "properties": {"fullName": {"description": "全名", "type": "string"}, "openId": {"description": "openId", "type": "string"}, "roleType": {"description": "角色类型 0 普通运营  1 管理员 2 只读账号", "type": "integer", "enum": [0, 1, 2]}}}, "schema.AdminOperatorUpdateResp": {"type": "object", "properties": {"id": {"description": "id", "type": "integer"}}}, "schema.AvatarItem": {"type": "object", "properties": {"id": {"type": "integer"}, "url": {"type": "string"}}}, "schema.BaseDeleteResp": {"type": "object", "properties": {"deletedAt": {"type": "integer"}, "id": {"type": "integer"}}}, "schema.BaseUpdateResp": {"type": "object", "properties": {"id": {"type": "integer"}, "updateTime": {"type": "integer"}}}, "schema.CommentAddReq": {"type": "object", "required": ["roleId", "skey", "text"], "properties": {"momentId": {"type": "integer"}, "roleId": {"description": "玩家角色id", "type": "integer"}, "skey": {"description": "玩家授权skey", "type": "string"}, "text": {"type": "string", "maxLength": 50}}}, "schema.CommentAddResp": {"type": "object", "properties": {"createTime": {"type": "integer"}, "id": {"type": "integer"}, "updateTime": {"type": "integer"}}}, "schema.CommentDeleteReq": {"type": "object", "required": ["roleId", "skey"], "properties": {"commentId": {"description": "评论id", "type": "integer"}, "roleId": {"description": "玩家角色id", "type": "integer"}, "skey": {"description": "玩家授权skey", "type": "string"}}}, "schema.CommentDeleteResp": {"type": "object", "properties": {"deletedAt": {"type": "integer"}, "id": {"type": "integer"}}}, "schema.CommentLikeReq": {"type": "object", "required": ["roleId", "skey"], "properties": {"commentId": {"description": "评论id", "type": "integer"}, "roleId": {"description": "玩家角色id", "type": "integer"}, "skey": {"description": "玩家授权skey", "type": "string"}}}, "schema.CommentLikeResp": {"type": "object", "properties": {"isOk": {"type": "boolean"}, "likeCount": {"type": "integer"}}}, "schema.CommentListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/schema.CommentShow"}}, "total": {"type": "integer"}}}, "schema.CommentShow": {"type": "object", "properties": {"createTime": {"description": "创建时间", "type": "integer"}, "id": {"description": "评论id", "type": "integer"}, "isLike": {"description": "是否点赞", "type": "boolean"}, "likeCount": {"description": "评论点赞数", "type": "integer"}, "momentId": {"description": "评论所属的动态id", "type": "integer"}, "roleId": {"description": "评论所属的角色id", "type": "integer"}, "roleInfo": {"description": "评论所属的角色信息", "allOf": [{"$ref": "#/definitions/schema.TinyRoleInfo"}]}, "text": {"description": "评论内容", "type": "string"}}}, "schema.CoreRoleInfo": {"type": "object", "properties": {"account": {"description": "推特账号名字", "type": "string"}, "avatarId": {"type": "integer"}, "avatarUrl": {"type": "string"}, "isCertified": {"description": "是否加v认证", "type": "boolean"}, "isFollow": {"description": "是否已关注", "type": "boolean"}, "isNpc": {"description": "是否为NPC角色", "type": "boolean"}, "name": {"type": "string"}, "roleId": {"type": "integer"}}}, "schema.FanListResp": {"type": "object", "properties": {"list": {"description": "粉丝列表", "type": "array", "items": {"$ref": "#/definitions/schema.FollowInfo"}}, "total": {"type": "integer"}}}, "schema.FollowInfo": {"type": "object", "properties": {"followTime": {"type": "integer"}, "id": {"type": "integer"}, "roleInfo": {"$ref": "#/definitions/schema.TinyRoleInfo"}, "targetId": {"type": "integer"}}}, "schema.FollowListResp": {"type": "object", "properties": {"list": {"description": "关注列表", "type": "array", "items": {"$ref": "#/definitions/schema.FollowInfo"}}, "total": {"type": "integer"}}}, "schema.FollowReq": {"type": "object", "required": ["roleId", "skey", "targetId"], "properties": {"roleId": {"description": "玩家角色id", "type": "integer"}, "skey": {"description": "玩家授权skey", "type": "string"}, "targetId": {"description": "关注者角色id", "type": "integer"}}}, "schema.FollowResp": {"type": "object", "properties": {"isOk": {"type": "boolean"}}}, "schema.GetFpTokenResp": {"type": "object", "properties": {"expires": {"description": "过期时间", "type": "integer"}, "project": {"description": "项目名称", "type": "string"}, "token": {"description": "token", "type": "string"}, "uploadUrl": {"description": "上传地址", "type": "string"}}}, "schema.MomentActionResp": {"type": "object", "properties": {"isOk": {"type": "boolean"}, "likeCount": {"description": "点赞数", "type": "integer"}}}, "schema.MomentAddByNpcReq": {"type": "object", "required": ["categoryId", "createTime", "npcId", "roleId", "text"], "properties": {"categoryId": {"description": "CategoryId 动态所属类别id 1 => 默认 2 => 混厄 3 => 支线 4 => 氛围", "type": "integer", "enum": [1, 2, 3, 4], "example": 1}, "createTime": {"description": "时间戳(ms)", "type": "integer", "example": 1734770382564}, "imgList": {"description": "发表动态的图片列表", "type": "array", "items": {"type": "string"}}, "npcId": {"description": "NpcId 发送动态的npcId", "type": "integer", "example": 10085}, "roleId": {"description": "接受动态的玩家角色id", "type": "integer", "example": 3365016015}, "text": {"description": "发表动态的内容", "type": "string", "example": "ai content send to player"}, "token": {"description": "计算得到的token md5(createTime + npcId + roleId + text + tokenSecret)", "type": "string", "example": "5cbb1bbf02635610b97419ddc70dd446"}}}, "schema.MomentAddByNpcResp": {"type": "object", "properties": {"id": {"description": "创建的动态id", "type": "integer"}}}, "schema.MomentAddByTemplateReq": {"type": "object", "required": ["roleId", "time"], "properties": {"roleId": {"description": "玩家角色id", "type": "integer"}, "templateId": {"description": "模板id, 传入npc模板的动态id", "type": "integer"}, "time": {"description": "时间戳", "type": "integer"}, "token": {"description": "计算得到的token (roleId + templateId + time + tokenSecret)", "type": "string"}}}, "schema.MomentAddByTemplateResp": {"type": "object", "properties": {"firstCreate": {"description": "是否已经创建过了(当Deduplicate为true时，如果已经创建过，则返回false)", "type": "boolean"}, "id": {"description": "创建的动态id", "type": "integer"}, "templateId": {"description": "模板id", "type": "integer"}}}, "schema.MomentCategory": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}}, "schema.MomentCollectListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/schema.MomentShow"}}, "total": {"type": "integer"}}}, "schema.MomentCollectReq": {"type": "object", "required": ["roleId", "skey"], "properties": {"momentId": {"type": "integer"}, "roleId": {"description": "玩家角色id", "type": "integer"}, "skey": {"description": "玩家授权skey", "type": "string"}}}, "schema.MomentLikeReq": {"type": "object", "required": ["roleId", "skey"], "properties": {"momentId": {"type": "integer"}, "roleId": {"description": "玩家角色id", "type": "integer"}, "skey": {"description": "玩家授权skey", "type": "string"}}}, "schema.MomentListResp": {"type": "object", "properties": {"list": {"description": "动态列表", "type": "array", "items": {"$ref": "#/definitions/schema.MomentShow"}}, "total": {"description": "总数", "type": "integer"}}}, "schema.MomentShow": {"type": "object", "properties": {"areaId": {"description": "动态关联的地区id", "type": "integer"}, "categoryId": {"description": "动态所属类别id", "type": "integer"}, "commentCount": {"description": "评论数", "type": "integer"}, "id": {"description": "动态id", "type": "integer"}, "imgList": {"description": "动态图片", "type": "array", "items": {"type": "string"}}, "isCollect": {"description": "是否收藏", "type": "boolean"}, "isLike": {"description": "是否点赞", "type": "boolean"}, "isTemplate": {"type": "boolean"}, "likeCount": {"description": "动态点赞数", "type": "integer"}, "publicTime": {"description": "发表时间", "type": "integer"}, "roleInfo": {"description": "动态所属的角色信息", "allOf": [{"$ref": "#/definitions/schema.CoreRoleInfo"}]}, "taskId": {"description": "动态关联的任务id", "type": "integer"}, "templateId": {"description": "引用的动态模板id", "type": "integer"}, "text": {"description": "动态文本", "type": "string"}, "videoCoverList": {"description": "动态视频封面", "type": "array", "items": {"type": "string"}}, "videoList": {"description": "动态视频", "type": "array", "items": {"type": "string"}}, "viewCount": {"description": "动态浏览量", "type": "integer"}}}, "schema.NpcAddReq": {"type": "object", "required": ["accountId", "avatarId", "baseFollowerCount", "baseFollowingCount", "name", "roleId", "signature"], "properties": {"accountId": {"description": "accountId 账号id", "type": "string", "maxLength": 20, "minLength": 1}, "avatarId": {"description": "npc头像id", "type": "integer"}, "baseFollowerCount": {"description": "npc基础粉丝数", "type": "integer"}, "baseFollowingCount": {"description": "npc基础关注数 最小值为0\nnpc基础关注数 最小值为0", "type": "integer"}, "isVip": {"description": "IsVip 是否为vip账号", "type": "boolean"}, "name": {"description": "npc名称", "type": "string"}, "roleId": {"description": "npc 角色Id,唯一,不可重复", "type": "integer"}, "signature": {"description": "npc签名", "type": "string"}}}, "schema.NpcAddResp": {"type": "object", "properties": {"createTime": {"type": "integer"}, "id": {"type": "integer"}, "updateTime": {"type": "integer"}}}, "schema.NpcCommentAddReq": {"type": "object", "required": ["momentId", "roleId", "text"], "properties": {"baseLikeCount": {"description": "评论基础点赞数", "type": "integer"}, "momentId": {"description": "动态id", "type": "integer"}, "roleId": {"description": "角色id", "type": "integer"}, "text": {"description": "评论内容", "type": "string", "maxLength": 100}}}, "schema.NpcCommentAddResp": {"type": "object", "properties": {"createTime": {"type": "integer"}, "id": {"type": "integer"}, "updateTime": {"type": "integer"}}}, "schema.NpcCommentDeleteReq": {"type": "object", "required": ["id"], "properties": {"id": {"description": "评论id", "type": "integer"}}}, "schema.NpcCommentListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/schema.NpcCommentShow"}}, "total": {"type": "integer"}}}, "schema.NpcCommentShow": {"type": "object", "required": ["momentId", "roleId", "text"], "properties": {"baseLikeCount": {"description": "评论基础点赞数", "type": "integer"}, "createTime": {"type": "integer"}, "id": {"type": "integer"}, "likeCount": {"description": "合计点赞数", "type": "integer"}, "momentId": {"description": "动态id", "type": "integer"}, "realLikeCount": {"description": "真实点赞数", "type": "integer"}, "roleId": {"description": "角色id", "type": "integer"}, "roleInfo": {"$ref": "#/definitions/schema.TinyRoleInfo"}, "text": {"description": "评论内容", "type": "string", "maxLength": 100}, "updateTime": {"type": "integer"}}}, "schema.NpcCommentUpdateReq": {"type": "object", "required": ["id"], "properties": {"baseLikeCount": {"type": "integer", "minimum": 0}, "id": {"type": "integer"}, "text": {"type": "string", "maxLength": 50}}}, "schema.NpcDeleteReq": {"type": "object", "properties": {"roleId": {"type": "integer"}}}, "schema.NpcDeleteResp": {"type": "object", "properties": {"delCommentCount": {"description": "删除的评论数量", "type": "integer"}, "delMomentCount": {"description": "删除的动态数量", "type": "integer"}, "deletedAt": {"type": "integer"}, "id": {"type": "integer"}}}, "schema.NpcListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/schema.NpcShow"}}, "total": {"type": "integer"}}}, "schema.NpcMomentAddReq": {"type": "object", "required": ["roleId", "text"], "properties": {"areaId": {"description": "动态关联的地区id", "type": "integer"}, "baseLikeCount": {"description": "发表动态的基础点赞数", "type": "integer"}, "categoryId": {"description": "动态所属类别id", "type": "integer"}, "imgList": {"description": "发表动态的图片列表", "type": "array", "items": {"type": "string"}}, "isTemplate": {"description": "是否是动态模板", "type": "boolean"}, "isTop": {"description": "动态是否置顶", "type": "boolean"}, "parentId": {"description": "父动态id", "type": "integer"}, "publicTime": {"description": "发表时间", "type": "integer"}, "roleId": {"description": "发表动态的npc的RoleId", "type": "integer"}, "taskId": {"description": "动态关联的任务id", "type": "integer"}, "text": {"description": "发表动态的内容", "type": "string"}, "videoCoverList": {"description": "发表动态的视频封面列表", "type": "array", "items": {"type": "string"}}, "videoList": {"description": "发表动态的视频列表", "type": "array", "items": {"type": "string"}}, "viewCount": {"description": "发表动态的浏览量", "type": "integer"}}}, "schema.NpcMomentAddResp": {"type": "object", "properties": {"createTime": {"type": "integer"}, "id": {"type": "integer"}, "updateTime": {"type": "integer"}}}, "schema.NpcMomentDeleteReq": {"type": "object", "required": ["id"], "properties": {"id": {"description": "动态id", "type": "integer"}}}, "schema.NpcMomentDeleteResp": {"type": "object", "properties": {"delCollectCount": {"type": "integer"}, "deletedAt": {"type": "integer"}, "id": {"type": "integer"}}}, "schema.NpcMomentListFilterResp": {"type": "object", "properties": {"categories": {"type": "array", "items": {"$ref": "#/definitions/schema.MomentCategory"}}}}, "schema.NpcMomentListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/schema.NpcMomentShowResp"}}, "total": {"type": "integer"}}}, "schema.NpcMomentShowResp": {"type": "object", "required": ["roleId", "text"], "properties": {"areaId": {"description": "动态关联的地区id", "type": "integer"}, "baseLikeCount": {"description": "发表动态的基础点赞数", "type": "integer"}, "categoryId": {"description": "动态所属类别id", "type": "integer"}, "commentCount": {"description": "动态评论数", "type": "integer"}, "createTime": {"type": "integer"}, "id": {"type": "integer"}, "imgList": {"description": "发表动态的图片列表", "type": "array", "items": {"type": "string"}}, "isTemplate": {"description": "是否是动态模板", "type": "boolean"}, "isTop": {"description": "动态是否置顶", "type": "boolean"}, "likeCount": {"description": "合计点赞数", "type": "integer"}, "parentId": {"description": "父动态id", "type": "integer"}, "publicTime": {"description": "发表时间", "type": "integer"}, "realLikeCount": {"description": "真实点赞数", "type": "integer"}, "roleId": {"description": "发表动态的npc的RoleId", "type": "integer"}, "roleInfo": {"description": "发表动态所属的npc信息", "allOf": [{"$ref": "#/definitions/schema.TinyRoleInfo"}]}, "subList": {"description": "子动态列表", "type": "array", "items": {"$ref": "#/definitions/schema.NpcMomentShowResp"}}, "taskId": {"description": "动态关联的任务id", "type": "integer"}, "text": {"description": "发表动态的内容", "type": "string"}, "toptime": {"description": "置顶时间", "type": "integer"}, "updateTime": {"type": "integer"}, "videoCoverList": {"description": "发表动态的视频封面列表", "type": "array", "items": {"type": "string"}}, "videoList": {"description": "发表动态的视频列表", "type": "array", "items": {"type": "string"}}, "viewCount": {"description": "发表动态的浏览量", "type": "integer"}}}, "schema.NpcMomentTopReq": {"type": "object", "required": ["id"], "properties": {"id": {"description": "动态id", "type": "integer"}}}, "schema.NpcMomentTopResp": {"type": "object", "properties": {"id": {"type": "integer"}, "updateTime": {"type": "integer"}}}, "schema.NpcMomentUpdateReq": {"type": "object", "required": ["id"], "properties": {"areaId": {"description": "动态关联的地区id", "type": "integer"}, "baseLikeCount": {"description": "动态点赞数", "type": "integer"}, "categoryId": {"description": "动态所属类别id", "type": "integer"}, "id": {"description": "动态id", "type": "integer"}, "imgList": {"description": "动态图片", "type": "array", "items": {"type": "string"}}, "isTemplate": {"description": "是否是模板动态", "type": "boolean"}, "isTop": {"description": "动态是否置顶", "type": "boolean"}, "parentId": {"description": "父动态id", "type": "integer"}, "publicTime": {"description": "发表时间", "type": "integer"}, "roleId": {"type": "integer"}, "taskId": {"description": "动态关联的任务id", "type": "integer"}, "text": {"description": "动态文本", "type": "string"}, "videoCoverList": {"description": "动态视频封面列表", "type": "array", "items": {"type": "string"}}, "videoList": {"description": "动态视频列表", "type": "array", "items": {"type": "string"}}, "viewCount": {"description": "动态浏览量", "type": "integer"}}}, "schema.NpcMomentUpdateResp": {"type": "object", "properties": {"id": {"type": "integer"}, "updateTime": {"type": "integer"}}}, "schema.NpcShow": {"type": "object", "required": ["accountId", "avatarId", "baseFollowerCount", "baseFollowingCount", "name", "roleId", "signature"], "properties": {"accountId": {"description": "accountId 账号id", "type": "string", "maxLength": 20, "minLength": 1}, "avatarId": {"description": "npc头像id", "type": "integer"}, "avatarUrl": {"type": "string"}, "baseFollowerCount": {"description": "npc基础粉丝数", "type": "integer"}, "baseFollowingCount": {"description": "npc基础关注数 最小值为0\nnpc基础关注数 最小值为0", "type": "integer"}, "createTime": {"type": "integer"}, "id": {"type": "integer"}, "isPlayerNpc": {"type": "boolean"}, "isVip": {"description": "IsVip 是否为vip账号", "type": "boolean"}, "name": {"description": "npc名称", "type": "string"}, "roleId": {"description": "npc 角色Id,唯一,不可重复", "type": "integer"}, "signature": {"description": "npc签名", "type": "string"}, "updateTime": {"type": "integer"}}}, "schema.NpcUpdateReq": {"type": "object", "required": ["avatarId", "name", "roleId"], "properties": {"accountId": {"description": "账号id", "type": "string"}, "avatarId": {"description": "npc头像id", "type": "integer"}, "baseFollowerCount": {"description": "npc基础粉丝数", "type": "integer"}, "baseFollowingCount": {"description": "npc基础关注数 最小值为0", "type": "integer"}, "isVip": {"description": "IsVip 是否为vip账号", "type": "boolean"}, "name": {"description": "npc名称", "type": "string"}, "roleId": {"description": "npc 角色Id,唯一,不可重复", "type": "integer"}, "signature": {"description": "npc签名", "type": "string"}}}, "schema.NpcUpdateResp": {"type": "object", "properties": {"updateTime": {"type": "integer"}}}, "schema.TinyRoleInfo": {"type": "object", "properties": {"account": {"description": "推特账号名字", "type": "string"}, "avatarId": {"type": "integer"}, "avatarUrl": {"type": "string"}, "isCertified": {"description": "是否加v认证", "type": "boolean"}, "isNpc": {"description": "是否为NPC角色", "type": "boolean"}, "name": {"type": "string"}, "roleId": {"type": "integer"}}}, "schema.TrendInfo": {"type": "object", "properties": {"areaId": {"type": "integer"}, "hot": {"type": "integer"}, "id": {"type": "integer"}, "name": {"type": "string"}}}, "schema.TrendListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/schema.TrendInfo"}}, "total": {"type": "integer"}}}, "schema.UserProfileResp": {"type": "object", "properties": {"account": {"description": "推特账号名字", "type": "string"}, "avatarId": {"type": "integer"}, "avatarUrl": {"type": "string"}, "followerCount": {"description": "粉丝数", "type": "integer"}, "followingCount": {"description": "关注数", "type": "integer"}, "isCertified": {"description": "是否加v认证", "type": "boolean"}, "isNpc": {"description": "是否为NPC角色", "type": "boolean"}, "name": {"type": "string"}, "roleId": {"type": "integer"}}}}, "securityDefinitions": {"api_key": {"description": "Authentication key", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "skey", "in": "query"}}}