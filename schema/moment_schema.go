package schema

type ReccMomentListReq struct {
	BaseAuthReq
	Pagination
}

type FollowMomentListReq struct {
	BaseAuthReq
	Pagination
}

type MomentListByTopicReq struct {
	BaseAuthReq
	Pagination
	TopicId int64 `json:"topicId" form:"topicId"`
}

type MomentListResp struct {
	// 动态列表
	List []MomentShow `json:"list"`
	// 总数
	Total int64 `json:"total"`
}

type MomentShow struct {
	// 动态id
	Id int64 `json:"id"`
	// 动态文本
	Text string `json:"text"`
	// 动态图片
	ImgList []string `json:"imgList"`
	// 动态视频
	VideoList []string `json:"videoList"`
	// 动态视频封面
	VideoCoverList []string `json:"videoCoverList"`
	// 动态点赞数
	LikeCount int64 `json:"likeCount"`
	// 动态浏览量
	ViewCount int64 `json:"viewCount"`
	// 评论数
	CommentCount int64 `json:"commentCount"`
	// 动态关联的任务id
	TaskId int `json:"taskId"`
	// 动态关联的地区id
	AreaId int `json:"areaId"`
	// 动态所属类别id
	CategoryId int `json:"categoryId"`
	// 发表时间
	PublicTime int64 `json:"publicTime"`
	// 动态所属的角色信息
	RoleInfo CoreRoleInfo `json:"roleInfo"`
	// 是否收藏
	IsCollect bool `json:"isCollect"`
	// 是否点赞
	IsLike bool `json:"isLike"`
	// 引用的动态模板id
	TemplateId int64 `json:"templateId"`
	IsTemplate bool  `json:"isTemplate"`
}

type MomentDetailReq struct {
	BaseAuthReq
	// 动态id
	MomentId int64 `json:"momentId" form:"momentId"`
}

type MomentDetailResp struct {
	MomentShow
	CommentList []*CommentShow
}

type MomentCollectListReq struct {
	BaseAuthReq
	Pagination
	// 动态分类id  0-所有 1-默认 2-混厄 3-支线 4-氛围
	CategoryId *int `json:"categoryId" form:"categoryId" binding:"required,gte=0,lte=4"`
}

type MomentCollectListResp struct {
	List  []MomentShow `json:"list"`
	Total int64        `json:"total"`
}

type MomentActionResp struct {
	BaseActionResp
	// 点赞数
	LikeCount int64 `json:"likeCount"`
}

type MomentActionReq struct {
	BaseAuthReq
	MomentId int64 `json:"momentId" form:"momentId"`
}

type MomentCollectReq = MomentActionReq

type MomentLikeReq = MomentActionReq

type MomentAddByTemplateReq struct {
	// 玩家角色id
	RoleId int64 `json:"roleId" binding:"required" form:"roleId"`
	// 模板id, 传入npc模板的动态id
	TemplateId int64 `json:"templateId" form:"templateId"`
	// 时间戳
	Time int64 `json:"time" form:"time" binding:"required"`
	// 计算得到的token (roleId + templateId + time + tokenSecret)
	Token string `json:"token" form:"token"`
}

type MomentAddByTemplateResp struct {
	// 创建的动态id
	Id int64 `json:"id"`
	// 模板id
	TemplateId int64 `json:"templateId"`
	// 是否已经创建过了(当Deduplicate为true时，如果已经创建过，则返回false)
	FirstCreate bool `json:"firstCreate"`
}

// npc添加动态，提供给伏羲的接口，用于添加动态
type MomentAddByNpcReq struct {
	//NpcId 发送动态的npcId
	NpcId int64 `json:"npcId" binding:"required" form:"npcId" example:"10085"`
	// 接受动态的玩家角色id
	RoleId int64 `json:"roleId" binding:"required" form:"roleId" example:"3365016015"`
	// 发表动态的内容
	Text string `json:"text" binding:"required" example:"ai content send to player"`
	// 发表动态的图片列表
	ImgList []string `json:"imgList"`
	// CategoryId 动态所属类别id 1 => 默认 2 => 混厄 3 => 支线 4 => 氛围
	CategoryId int `json:"categoryId" binding:"required" form:"categoryId"  enums:"1,2,3,4" example:"1"`
	// 时间戳(ms)
	CreateTime int64 `json:"createTime" form:"createTime" binding:"required" example:"1734770382564"`
	// 计算得到的token md5(createTime + npcId + roleId + text + tokenSecret)
	Token string `json:"token" form:"token" example:"5cbb1bbf02635610b97419ddc70dd446"`
}

type MomentAddByNpcResp struct {
	// 创建的动态id
	Id int64 `json:"id"`
}
