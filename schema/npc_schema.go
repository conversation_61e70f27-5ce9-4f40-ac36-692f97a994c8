package schema

type NpcAddReq struct {
	// npc 角色Id,唯一,不可重复
	RoleId int64 `json:"roleId" binding:"required"`
	// accountId 账号id
	AccountId string `json:"accountId" binding:"required,min=1,max=20"`
	// npc名称
	Name string `json:"name" binding:"required"`
	// npc头像id
	AvatarId int `json:"avatarId" binding:"required"`
	// npc基础关注数 最小值为0
	// npc基础关注数 最小值为0
	BaseFollowingCount int64 `json:"baseFollowingCount" binding:"required"`
	// npc基础粉丝数
	BaseFollowerCount int64 `json:"baseFollowerCount" binding:"required"`
	// npc签名
	Signature string `json:"signature" binding:"required"`
	// IsVip 是否为vip账号
	IsVip bool `json:"isVip"`
}

type NpcAddResp struct {
	BaseAddResp
}

type NpcUpdateReq struct {
	// npc 角色Id,唯一,不可重复
	RoleId int64 `json:"roleId" binding:"required"`
	// npc名称
	Name *string `json:"name" binding:"required"`
	// npc头像id
	AvatarId *int `json:"avatarId" binding:"required"`
	// npc基础关注数 最小值为0
	BaseFollowingCount *int64 `json:"baseFollowingCount"`
	// npc基础粉丝数
	BaseFollowerCount *int64 `json:"baseFollowerCount"`
	// npc签名
	Signature *string `json:"signature"`
	// 账号id
	AccountId *string `json:"accountId"`
	// IsVip 是否为vip账号
	IsVip *bool `json:"isVip"`
}

type NpcUpdateResp struct {
	UpdateTime int64 `json:"updateTime"`
}

type NpcShow struct {
	NpcAddReq
	NpcAddResp
	AvatarUrl   string `json:"avatarUrl"`
	IsPlayerNpc bool   `json:"isPlayerNpc"`
}

type NpcListReq struct {
	AdminPagination
	// npc 角色Id,唯一,不可重复
	RoleId int64 `json:"roleId" form:"roleId"`
	// 搜索关键字， 支持roleId和roleName
	Kw string `json:"kw" form:"kw"`
}

type NpcListResp struct {
	List  []NpcShow `json:"list"`
	Total int64     `json:"total"`
}

type NpcShowReq struct {
	RoleId int64 `json:"roleId" form:"roleId"`
}

type NpcDeleteReq struct {
	RoleId int64 `json:"roleId" form:"roleId"`
}

type NpcDeleteResp struct {
	BaseDeleteResp
	// 删除的动态数量
	DelMomentCount int64 `json:"delMomentCount"`
	// 删除的评论数量
	DelCommentCount int64 `json:"delCommentCount"`
}
