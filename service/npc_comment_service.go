package service

import (
	"app/api"
	"app/mapper"
	"app/model"
	"app/repo"
	"app/schema"
	"app/util"
	"context"
)

type NpcCommentService struct {
	commentRepo *repo.CommentRepo
	momentRepo  *repo.MomentRepo
	npcRepo     *repo.NpcRepo
	avatarRepo  *repo.AvatarRepo
	userService *UserService
}

func NewNpcCommentService(commentRepo *repo.CommentRepo, momentRepo *repo.MomentRepo, npcRepo *repo.NpcRepo, avatarRepo *repo.AvatarRepo, userService *UserService) *NpcCommentService {
	return &NpcCommentService{momentRepo: momentRepo, commentRepo: commentRepo, npcRepo: npcRepo, avatarRepo: avatarRepo, userService: userService}
}

func (s *NpcCommentService) AddNpcComment(ctx context.Context, req *schema.NpcCommentAddReq) (*schema.NpcCommentAddResp, error) {
	resp := &schema.NpcCommentAddResp{}
	npc, err := s.npcRepo.FindByRoleId(ctx, req.RoleId)
	if err != nil {
		return nil, err
	}
	moment, err := s.momentRepo.FindMomentById(ctx, req.MomentId)
	if err != nil {
		return nil, err
	}
	addComment := &model.CommentModel{
		MomentId:      req.MomentId,
		RoleId:        npc.RoleId,
		MomentRoleId:  moment.RoleId,
		Text:          req.Text,
		BaseLikeCount: req.BaseLikeCount,
	}
	err = s.commentRepo.DB.WithContext(ctx).Create(addComment).Error
	if err != nil {
		return nil, err
	}

	_, err = s.momentRepo.IncrementCommentCount(ctx, moment)
	if err != nil {
		return resp, err
	}

	resp.Id = addComment.ID
	resp.CreateTime = addComment.Ctime
	resp.UpdateTime = addComment.Utime
	return resp, nil
}

func (s *NpcCommentService) GetNpcComment(ctx context.Context, commentId int64) (*schema.NpcCommentShow, error) {
	comment, err := s.commentRepo.FindByID(ctx, commentId)
	if err != nil {
		return nil, err
	}
	resp := &schema.NpcCommentShow{
		NpcCommentAddReq: schema.NpcCommentAddReq{
			MomentId:      comment.MomentId,
			RoleId:        comment.RoleId,
			Text:          comment.Text,
			BaseLikeCount: comment.BaseLikeCount,
		},
		NpcCommentAddResp: schema.NpcCommentAddResp{
			BaseAddResp: schema.BaseAddResp{
				Id:         commentId,
				CreateTime: comment.Ctime,
				UpdateTime: comment.Utime,
			},
		},
	}
	return resp, nil
}

func (s *NpcCommentService) GetNpcCommentList(ctx context.Context, req *schema.NpcCommentListReq) (*schema.NpcCommentListResp, error) {
	baseQuery := s.commentRepo.Scope(ctx).Where("moment_id", req.MomentId)
	if req.RoleType != "" {
		if req.RoleType == schema.RoleTypeNpc {
			baseQuery = baseQuery.Where("role_id >= ? AND role_id < ?", model.NPC_MIN_ID, model.NPC_MAX_ID)
		} else if req.RoleType == schema.RoleTypePlayer {
			baseQuery = baseQuery.Where("role_id > ?", model.NPC_MAX_ID)
		}
	}
	comments, err := s.commentRepo.FindWithPagination(ctx, baseQuery.Order("like_count desc").Order("id desc"), req.Pagination)
	if err != nil {
		return nil, err
	}
	mapper := mapper.NewNpcCommentMapper()

	roleIds := make([]int64, 0)
	for _, comment := range comments {
		roleIds = append(roleIds, comment.RoleId)
	}

	roleInfos, err := s.userService.GetUserInfoList(ctx, roleIds)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleIds", roleIds).Error("GetUserInfoListFail")
		return nil, err
	}
	avatarMap := s.avatarRepo.GetAvatarUrlMap(ctx)
	list, err := mapper.ToNpcCommentShowList(comments, roleInfos, avatarMap)
	if err != nil {
		return nil, err
	}
	count, err := s.commentRepo.CountByQuery(ctx, baseQuery)
	if err != nil {
		return nil, err
	}
	resp := &schema.NpcCommentListResp{
		List:  list,
		Total: count,
	}
	return resp, nil
}

func (s *NpcCommentService) DeleteNpcComment(ctx context.Context, commentId int64) (schema.CommentDeleteResp, error) {
	comment, err := s.commentRepo.FindCommentById(ctx, commentId)
	resp := schema.CommentDeleteResp{}
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("commentId", commentId).Error("DeleteCommentError")
		return resp, err
	}
	moment, err := s.momentRepo.FindMomentById(ctx, comment.MomentId)
	if err != nil {
		return resp, err
	}
	err = s.commentRepo.DB.WithContext(ctx).Delete(&comment).Error
	if err != nil {
		return resp, err
	}
	_, err = s.momentRepo.DecrementCommentCount(ctx, moment)
	if err != nil {
		return resp, err
	}
	resp.Id = comment.ID
	resp.DeletedAt = int64(comment.DeletedAt)
	return resp, nil
}

func (s *NpcCommentService) UpdateNpcComment(ctx context.Context, req *schema.NpcCommentUpdateReq) (*schema.BaseUpdateResp, error) {
	comment, err := s.commentRepo.FindCommentById(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	if req.Text != nil {
		comment.Text = *req.Text
		if !model.IsValidNpcId(comment.RoleId) {
			return nil, api.GetBizErr(api.ErrBizPlayerCommentDisallowModify)
		}
	}
	if req.BaseLikeCount != nil {
		comment.BaseLikeCount = *req.BaseLikeCount
		comment.LikeCount = comment.GetLikeCount()
	}
	err = s.commentRepo.DB.WithContext(ctx).Save(&comment).Error
	if err != nil {
		return nil, err
	}
	return &schema.BaseUpdateResp{
		Id:         comment.ID,
		UpdateTime: comment.Utime,
	}, nil
}
