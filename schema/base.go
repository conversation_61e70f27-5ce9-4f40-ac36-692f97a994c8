package schema

type BaseAddResp struct {
	Id         int64 `json:"id"`
	CreateTime int64 `json:"createTime" desc:"创建时间"`
	UpdateTime int64 `json:"updateTime" desc:"更新时间"`
}

type BaseUpdateResp struct {
	Id         int64 `json:"id"`
	UpdateTime int64 `json:"updateTime" desc:"更新时间"`
}

type BaseActionResp struct {
	IsOk bool `json:"isOk"`
}

type Pagination struct {
	Page     int `json:"page" binding:"gte=1" form:"page,default=1"`
	PageSize int `json:"pageSize" binding:"gte=1,lte=20" form:"pageSize,default=10"`
}

type AdminPagination struct {
	Page int `json:"page" binding:"gte=1" form:"page,default=1"`
	// 管理后台分页数量最大限制为100
	PageSize int `json:"pageSize,omitempty" binding:"gte=1,lte=100" form:"pageSize,default=100"`
}

type BaseDeleteResp struct {
	Id        int64 `json:"id"`
	DeletedAt int64 `json:"deletedAt"`
}

type BaseAuthReq struct {
	// 玩家角色id
	RoleId int64 `json:"roleId" binding:"required" form:"roleId"`
	// 玩家授权skey
	Skey string `json:"skey" binding:"required" form:"skey"`
}

type LoginUserInfo struct {
	RoleId   int64  `json:"role_id" binding:"required"`
	RoleName string `json:"role_name" binding:"required"`
}

type UpdateResult struct {
	RowsAffected int64 `json:"rowsAffected"`
	RowsChanged  int64 `json:"rowsChanged"`
}

type SwaggerServer struct {
	Url         string `json:"url"`
	Description string `json:"description"`
}
