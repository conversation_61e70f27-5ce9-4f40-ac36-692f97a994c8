package repo

import (
	"app/api"
	"app/model"
	"app/schema"
	"app/util"
	"context"
	"errors"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type MomentCollectRepo struct {
	*BaseRepo[model.MomentCollectModel]
}

func NewMomentCollectRepo(db *gorm.DB) *MomentCollectRepo {
	return &MomentCollectRepo{
		BaseRepo: NewBaseRepo[model.MomentCollectModel](db),
	}
}

// add collect
func (r *MomentCollectRepo) AddCollect(ctx context.Context, roleId int64, moment model.MomentModel) (*model.MomentCollectModel, error) {
	curCollect, err := r.<PERSON>y(ctx, r.Scope(ctx).Unscoped().Where("role_id = ? and moment_id = ?", roleId, moment.ID))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		} else {
			return nil, err
		}
	}
	if curCollect.ID > 0 && curCollect.DeletedAt == 0 {
		return nil, api.GetBizErr(api.ErrBizMomentCollected)
	}
	addCollect := &model.MomentCollectModel{
		RoleId:       roleId,
		MomentId:     moment.ID,
		MomentRoleId: moment.RoleId,
		CategoryId:   moment.CategoryId,
	}
	addCollect.ID = curCollect.ID
	err = r.DB.WithContext(ctx).Save(&addCollect).Error
	if err != nil {
		return nil, err
	}
	return addCollect, nil
}

// isCollect
func (r *MomentCollectRepo) IsCollect(ctx context.Context, roleId int64, momentId int64) (bool, error) {
	isCollectMap, err := r.GetIsCollectMap(ctx, roleId, []int64{momentId})
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).WithField("momentId", momentId).Error("GetIsCollectMapFail")
		return false, err
	}
	return isCollectMap[momentId], nil
}

// delete collect
func (r *MomentCollectRepo) DeleteCollect(ctx context.Context, roleId int64, momentId int64) (int64, error) {
	return r.DeleteByQuery(ctx, "role_id = ? and moment_id = ?", roleId, momentId)
}

// get isCollectMap by roleId and momentIds
func (r *MomentCollectRepo) GetIsCollectMap(ctx context.Context, roleId int64, momentIds []int64) (map[int64]bool, error) {
	var entityList []model.MomentCollectModel
	isCollectMap := make(map[int64]bool)
	if len(momentIds) == 0 {
		return isCollectMap, nil
	}
	query := r.Scope(ctx).Where("role_id = ?", roleId).Where("moment_id IN (?)", momentIds).Select("moment_id")
	err := query.Find(&entityList).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).WithField("momentIds", momentIds).Error("GetIsCollectMapFail")
		return nil, err
	}
	for _, v := range entityList {
		isCollectMap[v.MomentId] = true
	}
	return isCollectMap, nil
}

// list collect not deleted momentIds by roleId and
func (r *MomentCollectRepo) ListCollectMomentIds(ctx context.Context, roleId int64, categoryId int, pagination schema.Pagination) ([]int64, error) {
	query := r.DB.WithContext(ctx).Where("role_id = ?", roleId).Order("id desc")
	if categoryId > 0 {
		query = query.Where("category_id = ?", categoryId)
	}
	rows, err := r.FindWithPagination(ctx, query, pagination)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).Error("GetCollectMomentIdsFail")
		return nil, err
	}
	momentIds := lo.Map(rows, func(v model.MomentCollectModel, _ int) int64 { return v.MomentId })
	return momentIds, nil
}

// Get user collect momentIds count
func (r *MomentCollectRepo) GetMomentCollectListCount(ctx context.Context, roleId int64, categoryId int) (int64, error) {
	query := r.Scope(ctx).Where("role_id = ?", roleId)
	if categoryId > 0 {
		query = query.Where("category_id = ?", categoryId)
	}
	cnt, err := r.CountByQuery(ctx, query)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).Error("GetMomentCollectCountFail")
	}
	return cnt, err
}

// Delete by momentId
func (r *MomentCollectRepo) DeleteByMomentId(ctx context.Context, momentId int64) (int64, error) {
	return r.DeleteByQuery(ctx, "moment_id = ?", momentId)
}
