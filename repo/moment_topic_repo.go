package repo

import (
	"app/model"
	"app/schema"
	"app/util"
	"context"
	"errors"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type MomentTopicRepo struct {
	*BaseRepo[model.MomentTopicModel]
}

func NewMomentTopicRepo(db *gorm.DB) *MomentTopicRepo {
	return &MomentTopicRepo{
		BaseRepo: NewBaseRepo[model.MomentTopicModel](db),
	}
}

// save moment topicIds relation batch
func (r *MomentTopicRepo) SaveMomentTopicsRelation(ctx context.Context, moment model.MomentModel, topicIds []int64) error {
	if len(topicIds) == 0 {
		return nil
	}
	topicList := []model.MomentTopicModel{}
	for _, topicId := range topicIds {
		topicList = append(topicList, model.MomentTopicModel{
			BaseCoreModel: model.BaseCoreModel{},
			MomentId:      moment.ID,
			MomentRoleId:  moment.RoleId,
			MomentVisible: moment.Visible,
			TopicId:       topicId,
		})
	}
	err := r.DB.Clauses(clause.OnConflict{
		DoUpdates: clause.Assignments(map[string]any{"moment_visible": moment.Visible, "deleted_at": 0}), // column needed to be updated
	}).
		Create(&topicList).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("moment", moment).WithField("topicIds", topicIds).Error("SaveMomentTopicsError")
		return err
	}
	return nil
}

// remove moment topicIds relation, like saveMomentTopicsRelation, but reverse
func (r *MomentTopicRepo) DelMomentTopicsRelation(ctx context.Context, momentId int64, topicIds []int64) error {
	if len(topicIds) == 0 {
		return nil
	}
	delCnt, err := r.DeleteByQuery(ctx, "moment_id = ? and topic_id in (?)", momentId, topicIds)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("momentId", momentId).WithField("topicIds", topicIds).Error("DeleteMomentTopicsError")
		return err
	}
	// log delCnt
	util.LogWithContext(ctx).WithError(err).WithField("momentId", momentId).WithField("topicId", topicIds).WithField("delCnt", delCnt).Info("DeleteMomentTopicsOk")

	return nil
}

// when moment is deleted, delete moment topicIds realtion
func (r *MomentTopicRepo) DeleteByMomentId(ctx context.Context, momentId int64) error {
	delCnt, err := r.DeleteByQuery(ctx, map[string]interface{}{"moment_id": momentId})
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("momentId", momentId).Error("DeleteMomentTopicsError")
		return err
	}
	util.LogWithContext(ctx).WithError(err).WithField("momentId", momentId).WithField("delCnt", delCnt).Error("DeleteMomentTopicsOk")
	return nil
}

func (r *MomentTopicRepo) FindByTopicId(ctx context.Context, topicId int64, pagination schema.Pagination) ([]model.MomentTopicModel, error) {
	query := r.Scope(ctx).
		Where("topic_id = ?", topicId)
	momentTopicList, err := r.FindWithPagination(ctx, query, pagination)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("topicId", topicId).Error("FindMomentTopicsError")
		return nil, err
	}
	return momentTopicList, nil
}

// count by topicId
func (r *MomentTopicRepo) CountByTopicId(ctx context.Context, topicId int64) (int64, error) {
	query := r.Scope(ctx).
		Where("topic_id = ?", topicId)
	cnt, err := r.CountByQuery(ctx, query)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("topicId", topicId).Error("CountTopicMomentTotalError")
		return 0, err
	}
	return cnt, nil
}

// get topicIds by momentIds, use gorm pluck method
func (r *MomentTopicRepo) GetTopicIdsByMomentId(ctx context.Context, momentId int64) ([]int64, error) {
	var topicIds []int64
	err := r.Scope(ctx).Where("moment_id = ? ", momentId).Pluck("topic_id", &topicIds).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("moment_id", momentId).Error("GetTopicIdsByMomentIdError")
		return nil, err
	}
	return topicIds, nil
}

// checck if topic contain any moment
func (r *MomentTopicRepo) IsTopicContainMoment(ctx context.Context, topicId int64) (bool, error) {
	var entity model.MomentTopicModel
	err := r.Scope(ctx).Where("topic_id = ?", topicId).First(&entity).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, err
	}
	return entity.ID > 0, nil
}

// 单机模式下看到的话题是通过roleId和topicId共同确定的
func (r *MomentTopicRepo) PlayerTopicScope(ctx context.Context, roleId int64, topicId int64) *gorm.DB {
	query := r.Scope(ctx).
		Where("(moment_role_id = ? or moment_role_id < ? ) and moment_visible = 1", roleId, model.NPC_MAX_ID).
		Where("topic_id = ?", topicId)
	return query
}

func (r *MomentTopicRepo) PlayerTopicHotScope(ctx context.Context, roleId int64, areaId int64) *gorm.DB {
	query := r.DB.Table("l50_moment_topic as t").Joins("INNER JOIN l50_moment as m on t.moment_id = m.id").
		Where("(t.moment_role_id = ? or t.moment_role_id < ?) and t.moment_visible = 1", roleId, model.NPC_MAX_ID).
		Where("t.deleted_at = 0").
		Group("t.topic_id").
		Select("t.topic_id, sum(m.view_count) as hot")
	if areaId > 0 {
		query = query.Where("m.area_id = ?", areaId)
	}
	return query
}

// get player max topic id by roleId
func (r *MomentTopicRepo) GetPlayerMaxSearchId(ctx context.Context, roleId int64) (int64, error) {
	var maxId int64
	err := r.Scope(ctx).
		Where("(moment_role_id = ? or moment_role_id < ? ) and moment_visible = 1", roleId, model.NPC_MAX_ID).
		Select("MAX(id)").Scan(&maxId).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).Error("GetPlayerMaxTopicIdError")
		return 0, err
	}
	return maxId, nil
}

// get topic ids by range [start, end) with limit size
func (r *MomentTopicRepo) GetTopicIdsByRange(ctx context.Context, roleId int64, start int64, end int64, size int64) ([]int64, error) {
	var topicIds []int64
	err := r.Scope(ctx).
		Where("(moment_role_id = ? or moment_role_id < ? ) and moment_visible = 1", roleId, model.NPC_MAX_ID).
		Where("id >= ? AND id < ?", start, end).Limit(int(size)).Pluck("distinct topic_id", &topicIds).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).WithField("start", start).WithField("end", end).WithField("size", size).Error("GetTopicIdsByRangeError")
		return nil, err
	}
	return topicIds, nil
}

func (r *MomentTopicRepo) CopyRelationByMomentId(ctx context.Context, roleId int64, srcMomentId int64, dstMomentId int64) (int, error) {
	var momentTopicList []*model.MomentTopicModel
	logger := util.LogWithContext(ctx).WithField("roleId", roleId).WithField("roleId", roleId).WithField("srcMomentId", srcMomentId)
	err := r.Scope(ctx).Where("moment_id = ?", srcMomentId).Find(&momentTopicList).Error
	if err != nil {
		logger.WithError(err).Error("CopyRelationByMomentIdFindError")
		return 0, err
	}

	for _, momentTopic := range momentTopicList {
		momentTopic.ID = 0
		momentTopic.MomentId = dstMomentId
		momentTopic.MomentRoleId = roleId
		momentTopic.MomentVisible = 1
	}
	if len(momentTopicList) == 0 {
		return 0, nil
	}
	err = r.DB.Create(&momentTopicList).Error
	if err != nil {
		logger.WithError(err).Error("CopyRelationByMomentIdSaveError")
		return 0, err
	}
	return 0, nil
}
