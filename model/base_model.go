package model

import (
	"app/conn"
	"context"
	"encoding/json"
	"time"

	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
	"gorm.io/plugin/soft_delete"
)

const (
	TTL = 5 * time.Minute
)

type BaseModel struct {
	ID    int64 `gorm:"primary_key" json:"id"`
	Ctime int64 `json:"ctime" gorm:"autoCreateTime:milli"`
	Utime int64 `json:"utime" gorm:"autoUpdateTime:milli"`
}

type QueryFn[V any] func(v *V) error

func addExtraSpaceIfExist(str string) string {
	if str != "" {
		return " " + str
	}
	return ""
}

func (u *BaseModel) BeforeCreate(tx *gorm.DB) (err error) {
	t := time.Now().UnixNano() / 1e6
	tx.Statement.SetColumn("ctime", t)
	tx.Statement.SetColumn("utime", t)
	return nil
}

func (u *BaseModel) BeforeUpdate(tx *gorm.DB) (err error) {
	t := time.Now().UnixNano() / 1e6
	tx.Statement.SetColumn("utime", t)
	return nil
}

func QueryRowsCache[V any](ctx context.Context, v *V, key string, query QueryFn[V]) error {
	// STEP 1 GET CACHE
	cache := conn.GetRedis().GetString(ctx, key)
	if cache != "" {
		err := json.Unmarshal([]byte(cache), v)
		if err == nil {
			return nil
		}
	}
	// STEP 2 GET DB
	err := query(v)
	if err == nil {
		// STEP 3 SET CACHE
		bytes, _ := json.Marshal(v)
		_, _ = conn.GetRedis().SetNXEX(ctx, key, string(bytes), TTL)
	}
	return err
}

func ClearRowsCache(ctx context.Context, key string) error {
	return conn.GetRedis().Del(ctx, key)
}

func (u *BaseModel) Paginate(page, pageSize int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if page == 0 {
			page = 1
		}
		switch {
		case pageSize > 100:
			pageSize = 100
		case pageSize <= 0:
			pageSize = 10
		}

		offset := (page - 1) * pageSize
		return db.Offset(offset).Limit(pageSize)
	}
}

type BaseCoreModel struct {
	BaseModel
	DeletedAt soft_delete.DeletedAt `gorm:"softDelete:milli" json:"deletedAt"`
}

type BaseCoreVersionModel struct {
	BaseCoreModel
	Version optimisticlock.Version `json:"version"`
}
