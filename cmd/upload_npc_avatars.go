package cmd

import (
	"app/service"
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"

	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(upload_npc_avatars)
}

var upload_npc_avatars = &cobra.Command{
	Use:   "upload_npc_avatars",
	Short: "upload npc avatars",
	Long:  "upload npc avatars",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()
		fpClient := service.GetFPClient()
		currentDir, err := os.Getwd()
		if err != nil {
			fmt.Println("Error:", err)
			return
		}

		// 需要读取的文件夹路径
		folderPath := currentDir + "/assets/npc_avatars/"

		// 构建匹配 PNG 文件的模式
		pattern := filepath.Join(folderPath, "*.png")

		// 使用 filepath.Glob 匹配所有 PNG 文件
		files, err := filepath.Glob(pattern)
		if err != nil {
			log.Fatal(err)
		}
		ctx := context.Background()

		// 遍历并打印所有匹配到的文件路径
		for _, file := range files {
			fmt.Println(file)
			upRet, err := fpClient.UploadFile(ctx, file, nil)
			if err != nil {
				log.Fatal(err)
			}

			log.Printf("Uploaded file %s with ID %s", file, upRet.URL)
		}
	},
}
