package repo

import (
	"context"
	"testing"
)

func TestAvatarRepo_GetAvatarUrl(t *testing.T) {
	avatarRepo := NewAvatarRepo()
	ctx := context.Background()
	avatars, err := avatarRepo.ListAvatars(ctx)
	if len(avatars) == 0 {
		t.<PERSON><PERSON>("avatars is empty")
	}
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	url := avatarRepo.GetAvatarUrl(ctx, avatars[0].Id)
	if url == "" {
		t.Fatal("get avatar url by id is empty")
	}
}
