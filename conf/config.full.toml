# 应用配置
[App]
AppName = "l50-social-media" # 应用名称，同时影响日志的基础app字段属性
HttpListen = ":9992"         # Http Server监听地址，默认:9992

# OpenAPI(swagger)文档配置
[Doc]
Title = "接口文档Title"             # 项目Title
Summary = "接口文档Summary"         # 项目Summary
Description = "接口文档Description" # 项目Description
Version = "v0.0.1"              # 当前版本
GroupKeyStart = 2               # 接口分组路由开始段
GroupKeyEnd = 3                 # 接口分组路由结束段

# OpenAPI(swagger)文档配置-项目Servers列表
[[Doc.Servers]]
Url = "https://localhost:9992"
Description = "本地环境1"
[[Doc.Servers]]
Url = "https://localhost:9993"
Description = "本地环境2"

# Gin框架相关开发配置
[Gin]
MaxMultipartMemory = 8 # 具体见gin.Engine.MaxMultipartMemory，单位：MB，默认8
Pprof = 0              # Gin pprof开关，0-关；1-打开，默认0

# Gin日志配置
[Gin.Log]
LogPath = "./logs"                                                   # Gin日志路径，默认./logs
AccessFileName = "access.log"                                        # Gin access日志文件名，默认access.log
ErrorFileName = "error.log"                                          # Gin error日志文件名，默认error.log
ReserveDays = 30                                                     # 日志保留天数，默认30
ReserveCount = 300                                                   # 日志保留数量，默认300
MaxSize = 1024                                                       # 触发日志切割大小，单位：MB，默认1024
Schema = "com.netease.leihuo.ccc.base.model.tables.v1.GwSitesAllLog" # 伏羲采集指定Schema，根据项目自行修改

# 应用日志配置
[Log]
LogPath = "./logs"                                                                # 日志路径，默认./logs
FileName = "app.log"                                                              # 日志文件，默认app.log
ReserveDays = 30                                                                  # 日志保留天数，默认30
ReserveCount = 300                                                                # 日志保留数量，默认300
MaxSize = 1024                                                                    # 触发日志切割大小，单位：MB，默认1024
Level = 4                                                                         # 日志等级，默认(elog.LvINFO)4
GlobalFields.Schema = "com.netease.leihuo.ccc.base.model.tables.v1.GwSitesAllLog" # 伏羲采集指定Schema，根据项目自行修改

# 数据库配置
[Mysql]
Host = "127.0.0.1"  # 默认127.0.0.1
Port = "3306"       # 默认3306
DBName = ""
Username = ""
Password = ""
MaxOpen = 500       # 最大可打开连接数，上限后并发进来新的DB请求将阻塞等待其他连接空闲释放，默认500
MaxIdle = 200       # 最大空闲连接数，上限后并发进来新的DB请求将新创建一次性连接承接，直至连接数到达MaxOpen限制，默认200
ConnMaxLifetime = 5 # 连接最大存活生命周期，单位：分钟，默认5

# Redis配置
[Redis]
Addr = "127.0.0.1:6379" # 默认127.0.0.1:6379
Password = ""
DB = 0                  # 默认0
MaxActive = 0           # 最大连接数，默认0
IsTLS = 0               # tls开关，0-关闭；1-打开，默认0

# HTTP Client配置
[HttpClient]
Timeout = 5000            # 请求超时时限，单位：毫秒，默认5000
MaxIdleConns = 0          # 最大空闲连接，默认0
MaxIdleConnsPerHost = 200 # 单Host最大空闲连接，默认200
MaxConnsPerHost = 500     # 单Host最大打开连接，默认500
IdleConnTimeout = 5       # 连接最大空闲生命周期，单位：分钟，默认5

# POPO配置
[Popo]
Url = "https://lhpp-popo-server.apps-hp.danlu.netease.com/popo/popo/msg/send"
Salt = "lOMvNtTgjrT7WJrmXOknYA=="
Project = "comm"                                                              # 项目key，比如yjwj
Biz = "demo"                                                                  # 业务key，比如card-2024
Env = "test"                                                                  # 环境标识，可选项：preview|test|release
Timeout = 5000                                                                # 请求超时时限，单位：毫秒，默认5000
