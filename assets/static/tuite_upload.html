<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tuite Excel 文件上传</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        .file-input {
            display: none;
        }
        .upload-btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
            width: 100%;
        }
        .upload-btn:hover {
            background-color: #0056b3;
        }
        .upload-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 20px;
            display: none;
        }
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s;
            width: 0%;
        }
        .message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .import-result {
            margin-top: 15px;
        }
        .import-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .import-item {
            background: white;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            text-align: center;
        }
        .import-item-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .import-item-count {
            font-size: 20px;
            color: #007bff;
            font-weight: bold;
        }
        .import-item-time {
            font-size: 12px;
            color: #666;
            margin-top: 3px;
        }
        .cleanup-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 6px;
            margin-top: 15px;
        }
        .total-time {
            text-align: center;
            margin-top: 15px;
            font-weight: bold;
            color: #28a745;
        }
        .file-info {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tuite Excel 文件上传</h1>

        <div style="background-color: #e7f3ff; padding: 15px; border-radius: 6px; margin-bottom: 20px; border-left: 4px solid #007bff;">
            <h3 style="margin: 0 0 10px 0; color: #0056b3;">📋 上传说明</h3>
            <ul style="margin: 0; padding-left: 20px; color: #333;">
                <li>此页面用于上传 Tuite 系统的 Excel 配置文件</li>
                <li>请确保 Excel 文件格式正确，包含所需的工作表和列</li>
                <li>上传的文件将自动导入到 Tuite 系统中</li>
                <li>支持的文件格式：.xlsx, .xlsm</li>
                <li>文件大小限制：最大 10MB</li>
            </ul>
        </div>

        <div class="upload-area" id="uploadArea">
            <p>点击选择文件或拖拽 Tuite Excel 文件到这里</p>
            <p style="font-size: 14px; color: #666;">支持 .xlsx, .xlsm 格式，最大 10MB</p>
            <input type="file" class="file-input" id="fileInput" accept=".xlsx,.xlsm">
        </div>

        <div class="file-info" id="fileInfo"></div>

        <button class="upload-btn" id="uploadBtn" disabled>上传文件</button>

        <div class="progress-bar" id="progressBar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="message" id="message"></div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const uploadBtn = document.getElementById('uploadBtn');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const message = document.getElementById('message');
        const fileInfo = document.getElementById('fileInfo');

        let selectedFile = null;

        // 点击上传区域选择文件
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // 文件选择变化
        fileInput.addEventListener('change', handleFileSelect);

        // 拖拽功能
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 上传按钮点击
        uploadBtn.addEventListener('click', uploadFile);

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            // 验证文件类型
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
                'application/vnd.ms-excel.sheet.macroEnabled.12' // .xlsm
            ];
            if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xlsm)$/i)) {
                showMessage('请选择 Excel 文件 (.xlsx 或 .xlsm)', 'error');
                return;
            }

            // 验证文件大小 (最大 10MB)
            if (file.size > 10 * 1024 * 1024) {
                showMessage('文件大小不能超过 10MB', 'error');
                return;
            }

            selectedFile = file;
            fileInfo.textContent = `已选择: ${file.name} (${formatFileSize(file.size)})`;
            uploadBtn.disabled = false;
            hideMessage();
        }

        function uploadFile() {
            if (!selectedFile) return;

            const formData = new FormData();
            formData.append('file', selectedFile);

            // 显示进度条
            progressBar.style.display = 'block';
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';

            // 创建 XMLHttpRequest 用于显示进度
            const xhr = new XMLHttpRequest();

            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    progressFill.style.width = percentComplete + '%';
                }
            });

            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.code === 0) {
                            showImportResult(response.data);
                        } else {
                            showMessage(`上传失败: ${response.msg || response.message || '未知错误'}`, 'error');
                        }
                    } catch (e) {
                        showMessage('文件解析错误，但可能上传成功', 'error');
                    }
                } else {
                    showMessage(`上传失败: HTTP ${xhr.status}`, 'error');
                }
                resetUpload();
            });

            xhr.addEventListener('error', () => {
                showMessage('上传过程中发生错误', 'error');
                resetUpload();
            });

            // 发送请求到后端 API
            const apiPath = window.location.pathname.replace('/web/tuite_upload', '/api/common/import');
            xhr.open('POST', apiPath);
            xhr.send(formData);
        }

        function resetUpload() {
            progressBar.style.display = 'none';
            progressFill.style.width = '0%';
            uploadBtn.disabled = false;
            uploadBtn.textContent = '上传文件';
            selectedFile = null;
            fileInfo.textContent = '';
            fileInput.value = '';
        }

        function showMessage(text, type) {
            message.innerHTML = text;
            message.className = `message ${type}`;
            message.style.display = 'block';
        }

        function showImportResult(data) {
            let resultHtml = `
                <div style="margin-bottom: 15px;">
                    <strong>🎉 导入成功完成！</strong>
                </div>
            `;

            // 构建导入统计
            const importItems = [];
            
            if (data.npcs) {
                importItems.push({
                    title: 'NPC角色',
                    count: data.npcs.count,
                    time: data.npcs.duration_ms
                });
            }
            
            if (data.moments) {
                importItems.push({
                    title: '动态内容',
                    count: data.moments.count,
                    time: data.moments.duration_ms
                });
            }
            
            if (data.comments) {
                importItems.push({
                    title: '评论内容',
                    count: data.comments.count,
                    time: data.comments.duration_ms
                });
            }
            
            if (data.topics) {
                importItems.push({
                    title: '话题标签',
                    count: data.topics.count,
                    time: data.topics.duration_ms
                });
            }
            
            if (data.categories) {
                importItems.push({
                    title: '分类信息',
                    count: data.categories.count,
                    time: data.categories.duration_ms
                });
            }
            
            if (data.moment_topics) {
                importItems.push({
                    title: '动态话题关联',
                    count: data.moment_topics.count,
                    time: data.moment_topics.duration_ms
                });
            }

            if (importItems.length > 0) {
                resultHtml += '<div class="import-summary">';
                importItems.forEach(item => {
                    resultHtml += `
                        <div class="import-item">
                            <div class="import-item-title">${item.title}</div>
                            <div class="import-item-count">${item.count}</div>
                            <div class="import-item-time">耗时: ${item.time}ms</div>
                        </div>
                    `;
                });
                resultHtml += '</div>';
            }

            // 清理信息
            if (data.cleanup && data.cleanup.total_count > 0) {
                resultHtml += `
                    <div class="cleanup-info">
                        <strong>🗑️ 数据清理统计</strong><br>
                        清理总数: ${data.cleanup.total_count} | 
                        动态: ${data.cleanup.moments_deleted || 0} | 
                        话题: ${data.cleanup.topics_deleted || 0} | 
                        评论: ${data.cleanup.comments_deleted || 0} | 
                        NPC: ${data.cleanup.npcs_deleted || 0}
                        <div style="margin-top: 5px; font-size: 12px;">清理耗时: ${data.cleanup.duration_ms}ms</div>
                    </div>
                `;
            }

            // 总体统计
            resultHtml += `
                <div class="total-time">
                    ⏱️ 总耗时: ${data.total_duration_ms}ms
                </div>
            `;

            // 警告信息
            if (data.warn_count > 0 && data.warns && data.warns.length > 0) {
                resultHtml += `
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 6px; margin-top: 15px;">
                        <strong>⚠️ 警告信息 (${data.warn_count}条)</strong><br>
                        <ul style="margin: 5px 0 0 20px; font-size: 14px;">
                `;
                data.warns.forEach(warn => {
                    resultHtml += `<li>${warn}</li>`;
                });
                resultHtml += '</ul></div>';
            }

            showMessage(resultHtml, 'success');
        }

        function hideMessage() {
            message.style.display = 'none';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>