// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package router

import (
	"app/config"
	"app/controller"
	"app/repo"
	"app/service"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/redis"
	"github.com/google/wire"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitAppController(db *gorm.DB, redis2 *redis.Pools, envSdkCfg config.EnvSdkCfg) (*AppController, error) {
	momentRepo := repo.NewMomentRepo(db)
	momentCollectRepo := repo.NewMomentCollectRepo(db)
	momentLikeRepo := repo.NewMomentLikeRepo(db)
	roleInfoRepo := repo.NewRoleInfoRepo(db)
	followRepo := repo.NewFollowRepo(db)
	avatarRepo := repo.NewAvatarRepo()
	npcRepo := repo.NewNpcRepo(db)
	topicRepo := repo.NewTopicRepo(db)
	momentTopicRepo := repo.NewMomentTopicRepo(db)
	topicIdCache := repo.NewTopicIdCache(redis2)
	userService := service.NewUserService(roleInfoRepo, npcRepo, avatarRepo, followRepo)
	topicService := service.NewTopicService(topicRepo, topicIdCache, momentTopicRepo)
	momentService := service.NewMomentService(momentRepo, momentCollectRepo, momentLikeRepo, roleInfoRepo, followRepo, avatarRepo, npcRepo, topicRepo, momentTopicRepo, topicIdCache, userService, topicService)
	momentController := controller.NewMomentController(momentService)
	commentRepo := repo.NewCommentRepo(db)
	commentLikeRepo := repo.NewCommentLikeRepo(db)
	commentService := service.NewCommentService(commentRepo, commentLikeRepo, momentRepo, roleInfoRepo, avatarRepo, npcRepo)
	envSdkService := service.NewEnvSdkService(envSdkCfg)
	commentController := controller.NewCommentController(commentService, envSdkService)
	userController := controller.NewUserController(userService)
	followService := service.NewFollowService(followRepo)
	followController := controller.NewFollowController(followService)
	trendController := controller.NewTrendController(topicService)
	npcService := service.NewNpcService(npcRepo, momentRepo, commentRepo, avatarRepo, roleInfoRepo)
	adminNpcController := controller.NewAdminNpcController(npcService)
	assetsService := service.NewAssetsService(avatarRepo)
	assertController := controller.NewAssetController(assetsService)
	dataExportService := service.NewDataExportService(npcRepo, momentRepo, topicRepo, commentRepo, momentTopicRepo, roleInfoRepo)
	dataImportService := service.NewDataImportService(npcRepo, momentRepo, topicRepo, commentRepo, momentTopicRepo, roleInfoRepo)
	adminCommonController := controller.NewAdminCommonController(dataExportService, dataImportService)
	npcMomentService := service.NewNpcMomentService(momentRepo, npcRepo, momentCollectRepo, momentLikeRepo, avatarRepo, momentTopicRepo, topicRepo, topicIdCache, topicService)
	adminNpcMomentController := controller.NewAdminNpcMomentController(npcMomentService, momentService)
	npcCommentService := service.NewNpcCommentService(commentRepo, momentRepo, npcRepo, avatarRepo, userService)
	adminNpcCommentController := controller.NewAdminNpcCommentController(npcCommentService, envSdkService)
	openIdRoleRepo := repo.NewOpenIdRoleRepo(db)
	adminOperatorService := service.NewAdminOperatorService(openIdRoleRepo)
	adminOperatorController := controller.NewAdminOperatorController(adminOperatorService)
	webController := controller.NewWebController()
	appController := NewAppController(momentController, commentController, userController, followController, trendController, adminNpcController, assertController, adminCommonController, adminNpcMomentController, adminNpcCommentController, adminOperatorController, webController)
	return appController, nil
}

// wire.go:

var controllers = wire.NewSet(controller.NewAdminNpcController, controller.NewAdminNpcMomentController, controller.NewAdminNpcCommentController, controller.NewAdminCommonController, controller.NewAssetController, controller.NewCommentController, controller.NewFollowController, controller.NewMomentController, controller.NewUserController, controller.NewTrendController, controller.NewAdminOperatorController, controller.NewWebController)

var repos = wire.NewSet(repo.NewAvatarRepo, repo.NewCommentLikeRepo, repo.NewCommentRepo, repo.NewFollowRepo, repo.NewMomentCollectRepo, repo.NewMomentLikeRepo, repo.NewMomentTopicRepo, repo.NewMomentRepo, repo.NewNpcRepo, repo.NewRoleInfoRepo, repo.NewTopicRepo, repo.NewTopicIdCache, repo.NewOpenIdRoleRepo)

var services = wire.NewSet(service.NewAssetsService, service.NewCommentService, service.NewFollowService, service.NewMomentService, service.NewNpcCommentService, service.NewNpcMomentService, service.NewNpcService, service.NewTopicService, service.NewUserService, service.NewEnvSdkService, service.NewDataExportService, service.NewDataImportService, service.NewAdminOperatorService)
