package service

import (
	"app/api"
	"app/mapper"
	"app/model"
	"app/repo"
	"app/schema"
	"app/util"
	"context"
)

type CommentService struct {
	commentRepo     *repo.CommentRepo
	commentLikeRepo *repo.CommentLikeRepo
	momentRepo      *repo.MomentRepo
	roleInfoRepo    *repo.RoleInfoRepo
	avatarRepo      *repo.AvatarRepo
	npcRepo         *repo.NpcRepo
}

func NewCommentService(commentRepo *repo.CommentRepo, commentLikeRepo *repo.CommentLikeRepo, momentRepo *repo.MomentRepo, roleInfoRepo *repo.RoleInfoRepo, avatarRepo *repo.AvatarRepo, npcRepo *repo.NpcRepo) *CommentService {
	return &CommentService{
		commentRepo:     commentRepo,
		commentLikeRepo: commentLikeRepo,
		momentRepo:      momentRepo,
		roleInfoRepo:    roleInfoRepo,
		avatarRepo:      avatarRepo,
		npcRepo:         npcRepo,
	}
}

func (c *CommentService) GetCommentList(ctx context.Context, req *schema.CommentListReq) (schema.CommentListResp, error) {
	curRoleId := req.RoleId
	resp := schema.CommentListResp{}
	moment, err := c.momentRepo.FindMomentById(ctx, req.MomentId)
	tplMoment := model.MomentModel{}
	if moment.TemplateId > 0 {
		tplMoment, err = c.momentRepo.FindMomentById(ctx, moment.TemplateId)
		if err != nil {
			return resp, err
		}
	}
	if err != nil {
		return resp, err
	}
	comments, err := c.commentRepo.GetListByMoment(ctx, moment, req.Pagination)
	if err != nil {
		return resp, err
	}
	mapper := mapper.NewCommentMapper(c.avatarRepo)

	roleIds := []int64{}
	commentIds := []int64{}
	for _, comment := range comments {
		roleIds = append(roleIds, comment.RoleId)
		commentIds = append(commentIds, comment.ID)
	}

	roleInfoMap, err := c.roleInfoRepo.GetRoleInfoMap(ctx, roleIds)
	if err != nil {
		return resp, err
	}

	isLikeMap, err := c.commentLikeRepo.GetIsLikeMap(ctx, curRoleId, commentIds)
	if err != nil {
		return resp, err
	}

	npcInfoMap, err := c.npcRepo.GetNpcInfoMap(ctx, roleIds)
	if err != nil {
		return resp, err
	}
	list, err := mapper.ToCommentShowList(ctx, comments, moment, roleInfoMap, isLikeMap, npcInfoMap)
	if err != nil {
		return resp, err
	}
	total, err := c.commentRepo.GetCountByMomentId(ctx, moment)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("momentId", req.MomentId).Error("GetCountByMomentIdError")
		return resp, nil
	}
	// 如果发现动态的评论数不等于总数减去模板的评论数,需要修正
	if moment.CommentCount != total-tplMoment.CommentCount {
		util.LogWithContext(ctx).WithField("momentId", req.MomentId).WithField("total", total).WithField("commentCount", moment.CommentCount).Warn("DetailCommentCountNotMatchAutoFix")
		realCommentCount := max(total-tplMoment.CommentCount, 0)
		_, err := c.momentRepo.UpdateById(ctx, moment.ID, map[string]any{"comment_count": realCommentCount})
		if err != nil {
			util.LogWithContext(ctx).WithError(err).WithField("momentId", req.MomentId).Error("UpdateMomentCommentCountError")
			return resp, err
		}
	}
	resp = schema.CommentListResp{List: list, Total: total}
	return resp, nil
}

func (c *CommentService) LikeComment(ctx context.Context, req *schema.CommentLikeReq) (schema.CommentLikeResp, error) {
	resp := schema.CommentLikeResp{}

	comment, err := c.commentRepo.FindCommentById(ctx, req.CommentId)
	if err != nil {
		return resp, err
	}
	util.LogWithContext(ctx).WithField("comment", comment).Info("LikeComment")
	ml, err := c.commentLikeRepo.AddLike(ctx, req.RoleId, comment)
	if err != nil {
		return resp, err
	}
	likeCount, err := c.commentRepo.IncrementLikeCount(ctx, comment)
	if err != nil {
		return resp, err
	}

	resp.IsOk = ml.ID > 0
	resp.LikeCount = likeCount
	return resp, nil
}

func (c *CommentService) CancelLikeComment(ctx context.Context, req *schema.CommentLikeReq) (schema.CommentLikeResp, error) {
	resp := schema.CommentLikeResp{}
	comment, err := c.commentRepo.FindCommentById(ctx, req.CommentId)
	if err != nil {
		return resp, err
	}
	delCnt, err := c.commentLikeRepo.DeleteLike(ctx, req.RoleId, comment.ID)
	if err != nil {
		return resp, err
	}
	if delCnt == 0 {
		return resp, api.GetBizErr(api.ErrBizCommentNotLiked)
	}

	likeCOunt, err := c.commentRepo.DecrementLikeCount(ctx, comment)
	if err != nil {
		return resp, err
	}
	resp.IsOk = delCnt > 0
	resp.LikeCount = likeCOunt
	return resp, nil
}

func (c *CommentService) AddComment(ctx context.Context, req *schema.CommentAddReq) (schema.CommentAddResp, error) {
	moment, err := c.momentRepo.FindMomentById(ctx, req.MomentId)
	if err != nil {
		return schema.CommentAddResp{}, err
	}

	resp := schema.CommentAddResp{}
	addComment := &model.CommentModel{
		MomentId:     moment.ID,
		RoleId:       req.RoleId,
		MomentRoleId: moment.RoleId,
		Text:         req.Text,
	}
	err = c.commentRepo.DB.WithContext(ctx).Create(addComment).Error
	if err != nil {
		return resp, err
	}
	_, err = c.momentRepo.IncrementCommentCount(ctx, moment)
	if err != nil {
		return resp, err
	}

	resp.Id = addComment.ID
	resp.CreateTime = addComment.Ctime
	resp.UpdateTime = addComment.Utime
	return resp, nil
}

func (c *CommentService) DeleteComment(ctx context.Context, req *schema.CommentDeleteReq) (schema.CommentDeleteResp, error) {
	curRoleId := req.RoleId
	resp := schema.CommentDeleteResp{}
	comment, err := c.commentRepo.FindUserCommentById(ctx, curRoleId, req.CommentId)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", curRoleId).WithField("commentId", req.CommentId).Error("DeleteCommentError")
		return resp, err
	}
	moment, err := c.momentRepo.FindMomentById(ctx, comment.MomentId)
	if err != nil {
		return resp, err
	}
	err = c.commentRepo.DB.WithContext(ctx).Delete(&comment).Error
	if err != nil {
		return resp, err
	}
	_, err = c.momentRepo.DecrementCommentCount(ctx, moment)
	if err != nil {
		return resp, err
	}
	resp.Id = comment.ID
	resp.DeletedAt = int64(comment.DeletedAt)
	return resp, nil
}
