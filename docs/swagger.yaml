basePath: /l50/social_media
definitions:
  api.RespBody:
    properties:
      code:
        type: integer
      data: {}
      msg:
        type: string
    type: object
  schema.AdminAuthLoginInfoResp:
    properties:
      createTime:
        type: integer
      id:
        type: integer
      openId:
        type: string
      roleType:
        type: integer
    type: object
  schema.AdminOperator:
    properties:
      createTime:
        description: 创建时间
        type: integer
      fullName:
        description: 全名
        type: string
      id:
        description: id
        type: integer
      openId:
        description: openId
        type: string
      roleType:
        description: 角色类型 0 普通运营  1 管理员  2 只读账号
        enum:
        - 0
        - 1
        - 2
        type: integer
    type: object
  schema.AdminOperatorAddReq:
    properties:
      fullName:
        description: 全名
        type: string
      openId:
        description: openId
        type: string
      roleType:
        description: 角色类型 0 普通运营  1 管理员  2 只读账号
        enum:
        - 0
        - 1
        - 2
        type: integer
    type: object
  schema.AdminOperatorAddResp:
    properties:
      createTime:
        description: 创建时间
        type: integer
      id:
        description: id
        type: integer
    type: object
  schema.AdminOperatorDeleteReq:
    properties:
      openId:
        description: openId
        type: string
    type: object
  schema.AdminOperatorListResp:
    properties:
      list:
        description: 列表
        items:
          $ref: '#/definitions/schema.AdminOperator'
        type: array
      total:
        description: 总数
        type: integer
    type: object
  schema.AdminOperatorUpdateReq:
    properties:
      fullName:
        description: 全名
        type: string
      openId:
        description: openId
        type: string
      roleType:
        description: 角色类型 0 普通运营  1 管理员 2 只读账号
        enum:
        - 0
        - 1
        - 2
        type: integer
    type: object
  schema.AdminOperatorUpdateResp:
    properties:
      id:
        description: id
        type: integer
    type: object
  schema.AvatarItem:
    properties:
      id:
        type: integer
      url:
        type: string
    type: object
  schema.BaseDeleteResp:
    properties:
      deletedAt:
        type: integer
      id:
        type: integer
    type: object
  schema.BaseUpdateResp:
    properties:
      id:
        type: integer
      updateTime:
        type: integer
    type: object
  schema.CommentAddReq:
    properties:
      momentId:
        type: integer
      roleId:
        description: 玩家角色id
        type: integer
      skey:
        description: 玩家授权skey
        type: string
      text:
        maxLength: 50
        type: string
    required:
    - roleId
    - skey
    - text
    type: object
  schema.CommentAddResp:
    properties:
      createTime:
        type: integer
      id:
        type: integer
      updateTime:
        type: integer
    type: object
  schema.CommentDeleteReq:
    properties:
      commentId:
        description: 评论id
        type: integer
      roleId:
        description: 玩家角色id
        type: integer
      skey:
        description: 玩家授权skey
        type: string
    required:
    - roleId
    - skey
    type: object
  schema.CommentDeleteResp:
    properties:
      deletedAt:
        type: integer
      id:
        type: integer
    type: object
  schema.CommentLikeReq:
    properties:
      commentId:
        description: 评论id
        type: integer
      roleId:
        description: 玩家角色id
        type: integer
      skey:
        description: 玩家授权skey
        type: string
    required:
    - roleId
    - skey
    type: object
  schema.CommentLikeResp:
    properties:
      isOk:
        type: boolean
      likeCount:
        type: integer
    type: object
  schema.CommentListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/schema.CommentShow'
        type: array
      total:
        type: integer
    type: object
  schema.CommentShow:
    properties:
      createTime:
        description: 创建时间
        type: integer
      id:
        description: 评论id
        type: integer
      isLike:
        description: 是否点赞
        type: boolean
      likeCount:
        description: 评论点赞数
        type: integer
      momentId:
        description: 评论所属的动态id
        type: integer
      roleId:
        description: 评论所属的角色id
        type: integer
      roleInfo:
        allOf:
        - $ref: '#/definitions/schema.TinyRoleInfo'
        description: 评论所属的角色信息
      text:
        description: 评论内容
        type: string
    type: object
  schema.CoreRoleInfo:
    properties:
      account:
        description: 推特账号名字
        type: string
      avatarId:
        type: integer
      avatarUrl:
        type: string
      isCertified:
        description: 是否加v认证
        type: boolean
      isFollow:
        description: 是否已关注
        type: boolean
      isNpc:
        description: 是否为NPC角色
        type: boolean
      name:
        type: string
      roleId:
        type: integer
    type: object
  schema.FanListResp:
    properties:
      list:
        description: 粉丝列表
        items:
          $ref: '#/definitions/schema.FollowInfo'
        type: array
      total:
        type: integer
    type: object
  schema.FollowInfo:
    properties:
      followTime:
        type: integer
      id:
        type: integer
      roleInfo:
        $ref: '#/definitions/schema.TinyRoleInfo'
      targetId:
        type: integer
    type: object
  schema.FollowListResp:
    properties:
      list:
        description: 关注列表
        items:
          $ref: '#/definitions/schema.FollowInfo'
        type: array
      total:
        type: integer
    type: object
  schema.FollowReq:
    properties:
      roleId:
        description: 玩家角色id
        type: integer
      skey:
        description: 玩家授权skey
        type: string
      targetId:
        description: 关注者角色id
        type: integer
    required:
    - roleId
    - skey
    - targetId
    type: object
  schema.FollowResp:
    properties:
      isOk:
        type: boolean
    type: object
  schema.GetFpTokenResp:
    properties:
      expires:
        description: 过期时间
        type: integer
      project:
        description: 项目名称
        type: string
      token:
        description: token
        type: string
      uploadUrl:
        description: 上传地址
        type: string
    type: object
  schema.MomentActionResp:
    properties:
      isOk:
        type: boolean
      likeCount:
        description: 点赞数
        type: integer
    type: object
  schema.MomentAddByNpcReq:
    properties:
      categoryId:
        description: CategoryId 动态所属类别id 1 => 默认 2 => 混厄 3 => 支线 4 => 氛围
        enum:
        - 1
        - 2
        - 3
        - 4
        example: 1
        type: integer
      createTime:
        description: 时间戳(ms)
        example: 1734770382564
        type: integer
      imgList:
        description: 发表动态的图片列表
        items:
          type: string
        type: array
      npcId:
        description: NpcId 发送动态的npcId
        example: 10085
        type: integer
      roleId:
        description: 接受动态的玩家角色id
        example: 3365016015
        type: integer
      text:
        description: 发表动态的内容
        example: ai content send to player
        type: string
      token:
        description: 计算得到的token md5(createTime + npcId + roleId + text + tokenSecret)
        example: 5cbb1bbf02635610b97419ddc70dd446
        type: string
    required:
    - categoryId
    - createTime
    - npcId
    - roleId
    - text
    type: object
  schema.MomentAddByNpcResp:
    properties:
      id:
        description: 创建的动态id
        type: integer
    type: object
  schema.MomentAddByTemplateReq:
    properties:
      roleId:
        description: 玩家角色id
        type: integer
      templateId:
        description: 模板id, 传入npc模板的动态id
        type: integer
      time:
        description: 时间戳
        type: integer
      token:
        description: 计算得到的token (roleId + templateId + time + tokenSecret)
        type: string
    required:
    - roleId
    - time
    type: object
  schema.MomentAddByTemplateResp:
    properties:
      firstCreate:
        description: 是否已经创建过了(当Deduplicate为true时，如果已经创建过，则返回false)
        type: boolean
      id:
        description: 创建的动态id
        type: integer
      templateId:
        description: 模板id
        type: integer
    type: object
  schema.MomentCategory:
    properties:
      id:
        type: integer
      name:
        type: string
    type: object
  schema.MomentCollectListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/schema.MomentShow'
        type: array
      total:
        type: integer
    type: object
  schema.MomentCollectReq:
    properties:
      momentId:
        type: integer
      roleId:
        description: 玩家角色id
        type: integer
      skey:
        description: 玩家授权skey
        type: string
    required:
    - roleId
    - skey
    type: object
  schema.MomentLikeReq:
    properties:
      momentId:
        type: integer
      roleId:
        description: 玩家角色id
        type: integer
      skey:
        description: 玩家授权skey
        type: string
    required:
    - roleId
    - skey
    type: object
  schema.MomentListResp:
    properties:
      list:
        description: 动态列表
        items:
          $ref: '#/definitions/schema.MomentShow'
        type: array
      total:
        description: 总数
        type: integer
    type: object
  schema.MomentShow:
    properties:
      areaId:
        description: 动态关联的地区id
        type: integer
      categoryId:
        description: 动态所属类别id
        type: integer
      commentCount:
        description: 评论数
        type: integer
      id:
        description: 动态id
        type: integer
      imgList:
        description: 动态图片
        items:
          type: string
        type: array
      isCollect:
        description: 是否收藏
        type: boolean
      isLike:
        description: 是否点赞
        type: boolean
      isTemplate:
        type: boolean
      likeCount:
        description: 动态点赞数
        type: integer
      publicTime:
        description: 发表时间
        type: integer
      roleInfo:
        allOf:
        - $ref: '#/definitions/schema.CoreRoleInfo'
        description: 动态所属的角色信息
      taskId:
        description: 动态关联的任务id
        type: integer
      templateId:
        description: 引用的动态模板id
        type: integer
      text:
        description: 动态文本
        type: string
      videoCoverList:
        description: 动态视频封面
        items:
          type: string
        type: array
      videoList:
        description: 动态视频
        items:
          type: string
        type: array
      viewCount:
        description: 动态浏览量
        type: integer
    type: object
  schema.NpcAddReq:
    properties:
      accountId:
        description: accountId 账号id
        maxLength: 20
        minLength: 1
        type: string
      avatarId:
        description: npc头像id
        type: integer
      baseFollowerCount:
        description: npc基础粉丝数
        type: integer
      baseFollowingCount:
        description: |-
          npc基础关注数 最小值为0
          npc基础关注数 最小值为0
        type: integer
      isVip:
        description: IsVip 是否为vip账号
        type: boolean
      name:
        description: npc名称
        type: string
      roleId:
        description: npc 角色Id,唯一,不可重复
        type: integer
      signature:
        description: npc签名
        type: string
    required:
    - accountId
    - avatarId
    - baseFollowerCount
    - baseFollowingCount
    - name
    - roleId
    - signature
    type: object
  schema.NpcAddResp:
    properties:
      createTime:
        type: integer
      id:
        type: integer
      updateTime:
        type: integer
    type: object
  schema.NpcCommentAddReq:
    properties:
      baseLikeCount:
        description: 评论基础点赞数
        type: integer
      momentId:
        description: 动态id
        type: integer
      roleId:
        description: 角色id
        type: integer
      text:
        description: 评论内容
        maxLength: 100
        type: string
    required:
    - momentId
    - roleId
    - text
    type: object
  schema.NpcCommentAddResp:
    properties:
      createTime:
        type: integer
      id:
        type: integer
      updateTime:
        type: integer
    type: object
  schema.NpcCommentDeleteReq:
    properties:
      id:
        description: 评论id
        type: integer
    required:
    - id
    type: object
  schema.NpcCommentListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/schema.NpcCommentShow'
        type: array
      total:
        type: integer
    type: object
  schema.NpcCommentShow:
    properties:
      baseLikeCount:
        description: 评论基础点赞数
        type: integer
      createTime:
        type: integer
      id:
        type: integer
      likeCount:
        description: 合计点赞数
        type: integer
      momentId:
        description: 动态id
        type: integer
      realLikeCount:
        description: 真实点赞数
        type: integer
      roleId:
        description: 角色id
        type: integer
      roleInfo:
        $ref: '#/definitions/schema.TinyRoleInfo'
      text:
        description: 评论内容
        maxLength: 100
        type: string
      updateTime:
        type: integer
    required:
    - momentId
    - roleId
    - text
    type: object
  schema.NpcCommentUpdateReq:
    properties:
      baseLikeCount:
        minimum: 0
        type: integer
      id:
        type: integer
      text:
        maxLength: 50
        type: string
    required:
    - id
    type: object
  schema.NpcDeleteReq:
    properties:
      roleId:
        type: integer
    type: object
  schema.NpcDeleteResp:
    properties:
      delCommentCount:
        description: 删除的评论数量
        type: integer
      delMomentCount:
        description: 删除的动态数量
        type: integer
      deletedAt:
        type: integer
      id:
        type: integer
    type: object
  schema.NpcListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/schema.NpcShow'
        type: array
      total:
        type: integer
    type: object
  schema.NpcMomentAddReq:
    properties:
      areaId:
        description: 动态关联的地区id
        type: integer
      baseLikeCount:
        description: 发表动态的基础点赞数
        type: integer
      categoryId:
        description: 动态所属类别id
        type: integer
      imgList:
        description: 发表动态的图片列表
        items:
          type: string
        type: array
      isTemplate:
        description: 是否是动态模板
        type: boolean
      isTop:
        description: 动态是否置顶
        type: boolean
      parentId:
        description: 父动态id
        type: integer
      publicTime:
        description: 发表时间
        type: integer
      roleId:
        description: 发表动态的npc的RoleId
        type: integer
      taskId:
        description: 动态关联的任务id
        type: integer
      text:
        description: 发表动态的内容
        type: string
      videoCoverList:
        description: 发表动态的视频封面列表
        items:
          type: string
        type: array
      videoList:
        description: 发表动态的视频列表
        items:
          type: string
        type: array
      viewCount:
        description: 发表动态的浏览量
        type: integer
    required:
    - roleId
    - text
    type: object
  schema.NpcMomentAddResp:
    properties:
      createTime:
        type: integer
      id:
        type: integer
      updateTime:
        type: integer
    type: object
  schema.NpcMomentDeleteReq:
    properties:
      id:
        description: 动态id
        type: integer
    required:
    - id
    type: object
  schema.NpcMomentDeleteResp:
    properties:
      delCollectCount:
        type: integer
      deletedAt:
        type: integer
      id:
        type: integer
    type: object
  schema.NpcMomentListFilterResp:
    properties:
      categories:
        items:
          $ref: '#/definitions/schema.MomentCategory'
        type: array
    type: object
  schema.NpcMomentListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/schema.NpcMomentShowResp'
        type: array
      total:
        type: integer
    type: object
  schema.NpcMomentShowResp:
    properties:
      areaId:
        description: 动态关联的地区id
        type: integer
      baseLikeCount:
        description: 发表动态的基础点赞数
        type: integer
      categoryId:
        description: 动态所属类别id
        type: integer
      commentCount:
        description: 动态评论数
        type: integer
      createTime:
        type: integer
      id:
        type: integer
      imgList:
        description: 发表动态的图片列表
        items:
          type: string
        type: array
      isTemplate:
        description: 是否是动态模板
        type: boolean
      isTop:
        description: 动态是否置顶
        type: boolean
      likeCount:
        description: 合计点赞数
        type: integer
      parentId:
        description: 父动态id
        type: integer
      publicTime:
        description: 发表时间
        type: integer
      realLikeCount:
        description: 真实点赞数
        type: integer
      roleId:
        description: 发表动态的npc的RoleId
        type: integer
      roleInfo:
        allOf:
        - $ref: '#/definitions/schema.TinyRoleInfo'
        description: 发表动态所属的npc信息
      subList:
        description: 子动态列表
        items:
          $ref: '#/definitions/schema.NpcMomentShowResp'
        type: array
      taskId:
        description: 动态关联的任务id
        type: integer
      text:
        description: 发表动态的内容
        type: string
      toptime:
        description: 置顶时间
        type: integer
      updateTime:
        type: integer
      videoCoverList:
        description: 发表动态的视频封面列表
        items:
          type: string
        type: array
      videoList:
        description: 发表动态的视频列表
        items:
          type: string
        type: array
      viewCount:
        description: 发表动态的浏览量
        type: integer
    required:
    - roleId
    - text
    type: object
  schema.NpcMomentTopReq:
    properties:
      id:
        description: 动态id
        type: integer
    required:
    - id
    type: object
  schema.NpcMomentTopResp:
    properties:
      id:
        type: integer
      updateTime:
        type: integer
    type: object
  schema.NpcMomentUpdateReq:
    properties:
      areaId:
        description: 动态关联的地区id
        type: integer
      baseLikeCount:
        description: 动态点赞数
        type: integer
      categoryId:
        description: 动态所属类别id
        type: integer
      id:
        description: 动态id
        type: integer
      imgList:
        description: 动态图片
        items:
          type: string
        type: array
      isTemplate:
        description: 是否是模板动态
        type: boolean
      isTop:
        description: 动态是否置顶
        type: boolean
      parentId:
        description: 父动态id
        type: integer
      publicTime:
        description: 发表时间
        type: integer
      roleId:
        type: integer
      taskId:
        description: 动态关联的任务id
        type: integer
      text:
        description: 动态文本
        type: string
      videoCoverList:
        description: 动态视频封面列表
        items:
          type: string
        type: array
      videoList:
        description: 动态视频列表
        items:
          type: string
        type: array
      viewCount:
        description: 动态浏览量
        type: integer
    required:
    - id
    type: object
  schema.NpcMomentUpdateResp:
    properties:
      id:
        type: integer
      updateTime:
        type: integer
    type: object
  schema.NpcShow:
    properties:
      accountId:
        description: accountId 账号id
        maxLength: 20
        minLength: 1
        type: string
      avatarId:
        description: npc头像id
        type: integer
      avatarUrl:
        type: string
      baseFollowerCount:
        description: npc基础粉丝数
        type: integer
      baseFollowingCount:
        description: |-
          npc基础关注数 最小值为0
          npc基础关注数 最小值为0
        type: integer
      createTime:
        type: integer
      id:
        type: integer
      isPlayerNpc:
        type: boolean
      isVip:
        description: IsVip 是否为vip账号
        type: boolean
      name:
        description: npc名称
        type: string
      roleId:
        description: npc 角色Id,唯一,不可重复
        type: integer
      signature:
        description: npc签名
        type: string
      updateTime:
        type: integer
    required:
    - accountId
    - avatarId
    - baseFollowerCount
    - baseFollowingCount
    - name
    - roleId
    - signature
    type: object
  schema.NpcUpdateReq:
    properties:
      accountId:
        description: 账号id
        type: string
      avatarId:
        description: npc头像id
        type: integer
      baseFollowerCount:
        description: npc基础粉丝数
        type: integer
      baseFollowingCount:
        description: npc基础关注数 最小值为0
        type: integer
      isVip:
        description: IsVip 是否为vip账号
        type: boolean
      name:
        description: npc名称
        type: string
      roleId:
        description: npc 角色Id,唯一,不可重复
        type: integer
      signature:
        description: npc签名
        type: string
    required:
    - avatarId
    - name
    - roleId
    type: object
  schema.NpcUpdateResp:
    properties:
      updateTime:
        type: integer
    type: object
  schema.TinyRoleInfo:
    properties:
      account:
        description: 推特账号名字
        type: string
      avatarId:
        type: integer
      avatarUrl:
        type: string
      isCertified:
        description: 是否加v认证
        type: boolean
      isNpc:
        description: 是否为NPC角色
        type: boolean
      name:
        type: string
      roleId:
        type: integer
    type: object
  schema.TrendInfo:
    properties:
      areaId:
        type: integer
      hot:
        type: integer
      id:
        type: integer
      name:
        type: string
    type: object
  schema.TrendListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/schema.TrendInfo'
        type: array
      total:
        type: integer
    type: object
  schema.UserProfileResp:
    properties:
      account:
        description: 推特账号名字
        type: string
      avatarId:
        type: integer
      avatarUrl:
        type: string
      followerCount:
        description: 粉丝数
        type: integer
      followingCount:
        description: 关注数
        type: integer
      isCertified:
        description: 是否加v认证
        type: boolean
      isNpc:
        description: 是否为NPC角色
        type: boolean
      name:
        type: string
      roleId:
        type: integer
    type: object
info:
  contact:
    email: <EMAIL>
    name: hzwangzhenhua
  description: L50社媒API
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  title: L50社媒API
  version: "1.0"
paths:
  /admin/assets/avatars/list:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/schema.AvatarItem'
      summary: 获取头像列表
      tags:
      - admin_asserts
  /admin/auth/login_info:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.AdminAuthLoginInfoResp'
              type: object
      summary: 获取登录用户信息
      tags:
      - admin_operator
  /admin/common/export_to_excel:
    get:
      responses: {}
      summary: 导出Excel
      tags:
      - admin_common
  /admin/common/get_fp_token:
    get:
      consumes:
      - application/json
      parameters:
      - description: 获取token类型
        enum:
        - image
        - video
        in: query
        name: type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.GetFpTokenResp'
              type: object
      summary: 获取FP Token
      tags:
      - admin_common
  /admin/common/import:
    post:
      responses: {}
      summary: 导入配置
      tags:
      - admin_common
  /admin/npc/add:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.NpcAddReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcAddResp'
              type: object
      summary: 新增NPC
      tags:
      - admin_npc
  /admin/npc/comment/add:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.NpcCommentAddReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcCommentAddResp'
              type: object
      summary: 新增NPC评论
      tags:
      - admin_npc_comment
  /admin/npc/comment/delete:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.NpcCommentDeleteReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.BaseDeleteResp'
              type: object
      summary: 删除NPC评论
      tags:
      - admin_npc_comment
  /admin/npc/comment/list:
    get:
      consumes:
      - application/json
      parameters:
      - description: 动态id
        in: query
        name: momentId
        type: integer
      - in: query
        minimum: 1
        name: page
        type: integer
      - in: query
        maximum: 20
        minimum: 1
        name: pageSize
        type: integer
      - enum:
        - all
        - npc
        - player
        in: query
        name: roleType
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcCommentListResp'
              type: object
      summary: 获取NPC评论列表
      tags:
      - admin_npc_comment
  /admin/npc/comment/update:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.NpcCommentUpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.BaseUpdateResp'
              type: object
      summary: 更新NPC评论数据
      tags:
      - admin_npc_comment
  /admin/npc/delete:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.NpcDeleteReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcDeleteResp'
              type: object
      summary: 删除NPC
      tags:
      - admin_npc
  /admin/npc/list:
    get:
      consumes:
      - application/json
      parameters:
      - description: 搜索关键字， 支持roleId和roleName
        in: query
        name: kw
        type: string
      - in: query
        minimum: 1
        name: page
        type: integer
      - description: 管理后台分页数量最大限制为100
        in: query
        maximum: 100
        minimum: 1
        name: pageSize
        type: integer
      - description: npc 角色Id,唯一,不可重复
        in: query
        name: roleId
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcListResp'
              type: object
      summary: 获取NPC列表
      tags:
      - admin_npc
  /admin/npc/moment/add:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.NpcMomentAddReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcMomentAddResp'
              type: object
      summary: 新增NPC动态
      tags:
      - admin_npc_moment
  /admin/npc/moment/delete:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.NpcMomentDeleteReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcMomentDeleteResp'
              type: object
      summary: 删除NPC动态
      tags:
      - admin_npc_moment
  /admin/npc/moment/filters:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/schema.NpcMomentListFilterResp'
      summary: 获取NPC动态列表过滤器
      tags:
      - admin_npc_moment
  /admin/npc/moment/list:
    get:
      consumes:
      - application/json
      parameters:
      - description: 动态所属地区id
        in: query
        name: areaId
        type: integer
      - description: 动态所属类别id
        in: query
        name: categoryId
        type: integer
      - description: 是否有任务
        in: query
        name: hasTask
        type: boolean
      - in: query
        name: onlyShowNpc
        type: boolean
      - in: query
        minimum: 1
        name: page
        type: integer
      - description: 管理后台分页数量最大限制为100
        in: query
        maximum: 100
        minimum: 1
        name: pageSize
        type: integer
      - description: 动态所属npcId
        in: query
        name: roleId
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcMomentListResp'
              type: object
      summary: 获取NPC动态列表
      tags:
      - admin_npc_moment
  /admin/npc/moment/show:
    get:
      consumes:
      - application/json
      parameters:
      - description: 动态id
        in: query
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcMomentShowResp'
              type: object
      summary: 查看单个npc动态
      tags:
      - admin_npc_moment
  /admin/npc/moment/sub_list:
    get:
      consumes:
      - application/json
      parameters:
      - description: 动态所属地区id
        in: query
        name: areaId
        type: integer
      - description: 动态所属类别id
        in: query
        name: categoryId
        type: integer
      - description: 是否有任务
        in: query
        name: hasTask
        type: boolean
      - in: query
        name: onlyShowNpc
        type: boolean
      - in: query
        minimum: 1
        name: page
        type: integer
      - description: 管理后台分页数量最大限制为100
        in: query
        maximum: 100
        minimum: 1
        name: pageSize
        type: integer
      - description: 动态所属npcId
        in: query
        name: roleId
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcMomentListResp'
              type: object
      summary: 获取NPC子动态列表
      tags:
      - admin_npc_moment
  /admin/npc/moment/top:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.NpcMomentTopReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcMomentTopResp'
              type: object
      summary: 设置NPC动态置顶
      tags:
      - admin_npc_moment
  /admin/npc/moment/untop:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.NpcMomentTopReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcMomentTopResp'
              type: object
      summary: 设置NPC动态取消置顶
      tags:
      - admin_npc_moment
  /admin/npc/moment/update:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.NpcMomentUpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcMomentUpdateResp'
              type: object
      summary: 更新NPC动态
      tags:
      - admin_npc_moment
  /admin/npc/show:
    get:
      consumes:
      - application/json
      parameters:
      - description: roleId
        in: query
        name: roleId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcShow'
              type: object
      summary: 查看单个npc
      tags:
      - admin_npc
  /admin/npc/update:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.NpcUpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.NpcUpdateResp'
              type: object
      summary: 更新NPC
      tags:
      - admin_npc
  /admin/operator/add:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.AdminOperatorAddReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.AdminOperatorAddResp'
              type: object
      summary: 新增用户
      tags:
      - admin_operator
  /admin/operator/del:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.AdminOperatorDeleteReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.BaseDeleteResp'
              type: object
      summary: 删除用户
      tags:
      - admin_operator
  /admin/operator/list:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        minimum: 1
        name: page
        type: integer
      - description: 管理后台分页数量最大限制为100
        in: query
        maximum: 100
        minimum: 1
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.AdminOperatorListResp'
              type: object
      summary: 获取用户列表
      tags:
      - admin_operator
  /admin/operator/update:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.AdminOperatorUpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.AdminOperatorUpdateResp'
              type: object
      summary: 更新用户
      tags:
      - admin_operator
  /api/comment/add:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.CommentAddReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommentAddResp'
              type: object
      summary: 添加评论
      tags:
      - comment
  /api/comment/cancel_like:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.CommentLikeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommentLikeResp'
              type: object
      summary: 取消点赞评论
      tags:
      - comment
  /api/comment/delete:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.CommentDeleteReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommentDeleteResp'
              type: object
      summary: 删除评论
      tags:
      - comment
  /api/comment/like:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.CommentLikeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommentLikeResp'
              type: object
      summary: 点赞评论
      tags:
      - comment
  /api/comment/list:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: momentId
        required: true
        type: integer
      - in: query
        minimum: 1
        name: page
        type: integer
      - in: query
        maximum: 20
        minimum: 1
        name: pageSize
        type: integer
      - description: 玩家角色id
        in: query
        name: roleId
        required: true
        type: integer
      - description: 玩家授权skey
        in: query
        name: skey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommentListResp'
              type: object
      summary: 获取评论列表
      tags:
      - comment
  /api/follow/add:
    post:
      description: 关注用户
      parameters:
      - description: req
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.FollowReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.FollowResp'
              type: object
      summary: 关注用户
      tags:
      - follow
  /api/follow/delete:
    post:
      description: 取消关注用户
      parameters:
      - description: req
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.FollowReq'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.FollowResp'
              type: object
      summary: 取消关注用户
      tags:
      - follow
  /api/follow/fans_list:
    get:
      description: 获取粉丝列表
      parameters:
      - in: query
        minimum: 1
        name: page
        type: integer
      - in: query
        maximum: 20
        minimum: 1
        name: pageSize
        type: integer
      - description: 玩家角色id
        in: query
        name: roleId
        required: true
        type: integer
      - description: 玩家授权skey
        in: query
        name: skey
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.FanListResp'
              type: object
      summary: 获取粉丝列表
      tags:
      - follow
  /api/follow/follow_list:
    get:
      description: 获取关注列表
      parameters:
      - in: query
        minimum: 1
        name: page
        type: integer
      - in: query
        maximum: 20
        minimum: 1
        name: pageSize
        type: integer
      - description: 玩家角色id
        in: query
        name: roleId
        required: true
        type: integer
      - description: 玩家授权skey
        in: query
        name: skey
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.FollowListResp'
              type: object
      summary: 获取关注列表
      tags:
      - follow
  /api/moment/cancel_collect:
    post:
      consumes:
      - application/json
      parameters:
      - description: req
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.MomentCollectReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.MomentActionResp'
              type: object
      summary: 取消收藏动态
      tags:
      - moment_collect
  /api/moment/cancel_like:
    post:
      consumes:
      - application/json
      parameters:
      - description: req
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.MomentLikeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.MomentActionResp'
              type: object
      summary: 取消点赞动态
      tags:
      - moment_like
  /api/moment/collect:
    post:
      consumes:
      - application/json
      parameters:
      - description: req
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.MomentCollectReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.MomentActionResp'
              type: object
      summary: 收藏动态
      tags:
      - moment_collect
  /api/moment/collect_list:
    get:
      consumes:
      - application/json
      parameters:
      - description: 动态分类id  0-所有 1-默认 2-混厄 3-支线 4-氛围
        in: query
        maximum: 4
        minimum: 0
        name: categoryId
        required: true
        type: integer
      - in: query
        minimum: 1
        name: page
        type: integer
      - in: query
        maximum: 20
        minimum: 1
        name: pageSize
        type: integer
      - description: 玩家角色id
        in: query
        name: roleId
        required: true
        type: integer
      - description: 玩家授权skey
        in: query
        name: skey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.MomentCollectListResp'
              type: object
      summary: 获取动态收藏列表
      tags:
      - moment_collect
  /api/moment/detail:
    get:
      consumes:
      - application/json
      parameters:
      - description: 动态id
        in: query
        name: momentId
        type: integer
      - description: 玩家角色id
        in: query
        name: roleId
        required: true
        type: integer
      - description: 玩家授权skey
        in: query
        name: skey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.MomentShow'
              type: object
      summary: 动态-获取详情
      tags:
      - moment
  /api/moment/follow_list:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        minimum: 1
        name: page
        type: integer
      - in: query
        maximum: 20
        minimum: 1
        name: pageSize
        type: integer
      - description: 玩家角色id
        in: query
        name: roleId
        required: true
        type: integer
      - description: 玩家授权skey
        in: query
        name: skey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.MomentListResp'
              type: object
      summary: 动态-获取关注的动态列表
      tags:
      - moment
  /api/moment/like:
    post:
      consumes:
      - application/json
      parameters:
      - description: req
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.MomentLikeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.MomentActionResp'
              type: object
      summary: 点赞动态
      tags:
      - moment_like
  /api/moment/list_by_topic:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        minimum: 1
        name: page
        type: integer
      - in: query
        maximum: 20
        minimum: 1
        name: pageSize
        type: integer
      - description: 玩家角色id
        in: query
        name: roleId
        required: true
        type: integer
      - description: 玩家授权skey
        in: query
        name: skey
        required: true
        type: string
      - in: query
        name: topicId
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.MomentListResp'
              type: object
      summary: 动态-话题下动态列表
      tags:
      - moment
  /api/moment/recc_list:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        minimum: 1
        name: page
        type: integer
      - in: query
        maximum: 20
        minimum: 1
        name: pageSize
        type: integer
      - description: 玩家角色id
        in: query
        name: roleId
        required: true
        type: integer
      - description: 玩家授权skey
        in: query
        name: skey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.MomentListResp'
              type: object
      summary: 动态-获取推荐的动态列表
      tags:
      - moment
  /api/server/moment/add_by_npc:
    post:
      consumes:
      - application/json
      description: 新增npc发送给玩家的动态
      parameters:
      - description: req
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.MomentAddByNpcReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.MomentAddByNpcResp'
              type: object
      summary: 动态-新增npc发送给玩家的动态
      tags:
      - moment
  /api/server/moment/add_by_template:
    post:
      consumes:
      - application/json
      parameters:
      - description: req
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/schema.MomentAddByTemplateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.MomentAddByTemplateResp'
              type: object
      security: []
      summary: 动态-服务器通过模板新增动态
      tags:
      - moment
  /api/trend/list:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: areaId
        type: integer
      - in: query
        minimum: 1
        name: page
        type: integer
      - in: query
        maximum: 20
        minimum: 1
        name: pageSize
        type: integer
      - description: 玩家角色id
        in: query
        name: roleId
        required: true
        type: integer
      - description: 玩家授权skey
        in: query
        name: skey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.TrendListResp'
              type: object
      summary: 获取热门趋势话题
      tags:
      - trend
  /api/trend/random:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: areaId
        type: integer
      - in: query
        minimum: 1
        name: page
        type: integer
      - in: query
        maximum: 20
        minimum: 1
        name: pageSize
        type: integer
      - description: 玩家角色id
        in: query
        name: roleId
        required: true
        type: integer
      - description: 玩家授权skey
        in: query
        name: skey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.TrendListResp'
              type: object
      summary: 获取随机热门趋势话题
      tags:
      - trend
  /api/user/profile:
    get:
      consumes:
      - application/json
      parameters:
      - description: 玩家角色id
        in: query
        name: roleId
        required: true
        type: integer
      - description: 玩家授权skey
        in: query
        name: skey
        required: true
        type: string
      - description: 查看的角色id
        in: query
        name: viewRoleId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/api.RespBody'
            - properties:
                data:
                  $ref: '#/definitions/schema.UserProfileResp'
              type: object
      summary: 获取用户详情
      tags:
      - user
  /web/tuite_upload:
    get:
      responses: {}
      summary: Tuite上传页面
      tags:
      - web
securityDefinitions:
  api_key:
    description: Authentication key
    in: query
    name: skey
    type: apiKey
swagger: "2.0"
