package controller

import (
	"app/api"
	"app/schema"
	"app/service"

	"github.com/gin-gonic/gin"
)

type UserController struct {
	userService *service.UserService
}

func NewUserController(userService *service.UserService) *UserController {
	return &UserController{
		userService: userService,
	}
}

// GetUserProfile godoc
// @Summary 获取用户详情
// @Tags user
// @Accept  json
// @Produce  json
// @Param request query schema.UserProfileReq true "query"
// @success 200 {object} api.RespBody{data=schema.UserProfileResp}
// @Router /api/user/profile [get]
func (uc *UserController) GetUserProfile(ctx *gin.Context) {
	req := &schema.UserProfileReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := uc.userService.GetUserProfile(ctx, req)
	api.HandleResponse(ctx, resp, err)
}
