package controller

import (
	"app/api"
	"app/schema"
	"app/service"

	"github.com/gin-gonic/gin"
)

type AdminNpcMomentController struct {
	npcMomentService *service.NpcMomentService
	momentService    *service.MomentService
}

func NewAdminNpcMomentController(npcMomentService *service.NpcMomentService, momentService *service.MomentService) *AdminNpcMomentController {
	return &AdminNpcMomentController{
		npcMomentService: npcMomentService,
		momentService:    momentService,
	}
}

// AddNpc godoc
// @Summary 新增NPC动态
// @Tags admin_npc_moment
// @Accept  json
// @Produce  json
// @Param request body schema.NpcMomentAddReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.NpcMomentAddResp}
// @Router /admin/npc/moment/add [post]
func (nc *AdminNpcMomentController) AddNpcMoment(ctx *gin.Context) {
	req := &schema.NpcMomentAddReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.momentService.AddNpcMoment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// SetNptMomentTop godoc
// @Summary 设置NPC动态置顶
// @Tags admin_npc_moment
// @Accept  json
// @Produce  json
// @Param request body schema.NpcMomentTopReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.NpcMomentTopResp}
// @Router /admin/npc/moment/top [post]
func (nc *AdminNpcMomentController) SetNptMomentTop(ctx *gin.Context) {
	req := &schema.NpcMomentTopReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.npcMomentService.SetNpcMomentTop(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// SetNptMomentUnTop godoc
// @Summary 设置NPC动态取消置顶
// @Tags admin_npc_moment
// @Accept  json
// @Produce  json
// @Param request body schema.NpcMomentTopReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.NpcMomentTopResp}
// @Router /admin/npc/moment/untop [post]
func (nc *AdminNpcMomentController) SetNptMomentUnTop(ctx *gin.Context) {
	req := &schema.NpcMomentTopReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.npcMomentService.SetNpcMomentUnTop(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// GetNpcMoment godoc
// @Summary 查看单个npc动态
// @Tags admin_npc_moment
// @Accept  json
// @Produce  json
// @Param request query schema.NpcMomentShowReq true "Request query"
// @success 200 {object} api.RespBody{data=schema.NpcMomentShowResp}
// @Router /admin/npc/moment/show [get]
func (nc *AdminNpcMomentController) GetNpcMoment(ctx *gin.Context) {
	req := &schema.NpcMomentShowReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.npcMomentService.GetNpcMoment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// UpdateNpcMoment godoc
// @Summary 更新NPC动态
// @Tags admin_npc_moment
// @Accept  json
// @Produce  json
// @Param request body schema.NpcMomentUpdateReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.NpcMomentUpdateResp}
// @Router /admin/npc/moment/update [post]
func (nc *AdminNpcMomentController) UpdateNpcMoment(ctx *gin.Context) {
	req := &schema.NpcMomentUpdateReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.npcMomentService.UpdateNpcMoment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// GetNpcMomentList godoc
// @Summary 获取NPC动态列表
// @Tags admin_npc_moment
// @Accept  json
// @Produce  json
// @Param request query schema.NpcMomentListReq true "Request query"
// @success 200 {object} api.RespBody{data=schema.NpcMomentListResp}
// @Router /admin/npc/moment/list [get]
func (nc *AdminNpcMomentController) GetNpcMomentList(ctx *gin.Context) {
	req := &schema.NpcMomentListReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.npcMomentService.GetNpcMomentList(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// GetNpcSubMomentList godoc
// @Summary 获取NPC子动态列表
// @Tags admin_npc_moment
// @Accept  json
// @Produce  json
// @Param request query schema.NpcMomentListReq true "Request query"
// @success 200 {object} api.RespBody{data=schema.NpcMomentListResp}
// @Router /admin/npc/moment/sub_list [get]
func (nc *AdminNpcMomentController) GetNpcSubMomentList(ctx *gin.Context) {
	req := &schema.NpcSubMomentListReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.npcMomentService.GetNpcSubMomentList(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// DeleteNpcMoment godoc
// @Summary 删除NPC动态
// @Tags admin_npc_moment
// @Accept  json
// @Produce  json
// @Param request body schema.NpcMomentDeleteReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.NpcMomentDeleteResp}
// @Router /admin/npc/moment/delete [post]
func (nc *AdminNpcMomentController) DeleteNpcMoment(ctx *gin.Context) {
	req := &schema.NpcMomentDeleteReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.momentService.DeleteNpcMoment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// GetNpcMomentFilters godoc
// @Summary 获取NPC动态列表过滤器
// @Tags admin_npc_moment
// @Produce  json
// @Router /admin/npc/moment/filters [get]
// @Success 200 {object} schema.NpcMomentListFilterResp
func (nc *AdminNpcMomentController) GetNpcMomentFilters(ctx *gin.Context) {
	resp, err := nc.npcMomentService.GetNpcMomentFilters(ctx)
	api.HandleResponse(ctx, resp, err)
}
