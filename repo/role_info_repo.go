package repo

import (
	"app/api"
	"app/model"
	"app/util"
	"context"
	"strconv"

	"gorm.io/gorm"
)

type RoleInfoRepo struct {
	*BaseRepo[model.RoleInfoModel]
}

func NewRoleInfoRepo(db *gorm.DB) *RoleInfoRepo {
	return &RoleInfoRepo{
		BaseRepo: NewBaseRepo[model.RoleInfoModel](db),
	}
}

// get roleInfoList by roleIds
func (r *RoleInfoRepo) GetRoleInfoList(ctx context.Context, roleIds []int64, cols []string) ([]model.RoleInfoModel, error) {
	if len(roleIds) == 0 {
		return []model.RoleInfoModel{}, nil
	}
	var roleInfoList []model.RoleInfoModel
	err := r.Scope(ctx).Find(&roleInfoList, "RoleId in (?)", roleIds).Select(cols).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleIds", roleIds).Error("GetRoleInfoListFail")
		return roleInfoList, err
	}
	return roleInfoList, nil
}

// get roleInfoMap by roleIds
func (r *RoleInfoRepo) GetRoleInfoMap(ctx context.Context, roleIds []int64) (map[int64]model.RoleInfoModel, error) {
	if len(roleIds) == 0 {
		return map[int64]model.RoleInfoModel{}, nil
	}
	roleInfoMap := make(map[int64]model.RoleInfoModel)
	var roleInfoList []model.RoleInfoModel
	err := r.Scope(ctx).Find(&roleInfoList, "RoleId in (?)", roleIds).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleIds", roleIds).Error("GetRoleInfoListFail")
		return roleInfoMap, err
	}
	for _, roleInfo := range roleInfoList {
		roleInfoMap[roleInfo.RoleId] = roleInfo
	}
	return roleInfoMap, nil
}

func (r *RoleInfoRepo) FindByRoleId(ctx context.Context, roleId int64) (model.RoleInfoModel, error) {
	roleInfo := model.RoleInfoModel{}
	err := r.Scope(ctx).Where("RoleId = ?", roleId).First(&roleInfo).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).Error("FindRoleByIdFail")
		if err == gorm.ErrRecordNotFound {
			return roleInfo, api.GetBizErr(api.ErrBizRoleInfoNotFound)
		}
		return roleInfo, err
	}
	return roleInfo, nil
}

// save roleinfo by model.NpcModel
func (r *RoleInfoRepo) SaveNpcRoleInfo(ctx context.Context, npc model.NpcModel) (model.RoleInfoModel, error) {
	roleInfo := model.RoleInfoModel{
		RoleId:   npc.RoleId,
		RoleName: npc.Name,
		Avatar:   strconv.Itoa(npc.AvatarId),
	}
	err := r.Scope(ctx).Where("RoleId = ?", npc.RoleId).Save(&roleInfo).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("npc", npc).Error("SaveNpcRoleInfoFail")
		return roleInfo, err
	}
	return roleInfo, err
}

// 新增：根据 roleId createOrUpdate，支持事务和部分字段同步
func (r *RoleInfoRepo) CreateOrUpdateByRoleId(tx *gorm.DB, npc model.NpcModel) error {
	var info model.RoleInfoModel
	err := tx.Where("RoleId = ?", npc.RoleId).First(&info).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			info = model.RoleInfoModel{
				RoleId:   npc.RoleId,
				RoleName: npc.Name,
				Avatar:   strconv.Itoa(npc.AvatarId),
			}
			return tx.Create(&info).Error
		}
		return err
	}
	// 已存在则更新部分字段
	info.RoleName = npc.Name
	info.Avatar = strconv.Itoa(npc.AvatarId)
	return tx.Save(&info).Error
}
