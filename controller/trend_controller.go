package controller

import (
	"app/api"
	"app/schema"
	"app/service"

	"github.com/gin-gonic/gin"
)

type TrendController struct {
	topicService *service.TopicService
}

func NewTrendController(topicService *service.TopicService) *TrendController {
	return &TrendController{
		topicService: topicService,
	}
}

// GetTrendsList godoc
// @Summary 获取热门趋势话题
// @Tags trend
// @Accept  json
// @Produce  json
// @Param request query schema.TrendListReq true "query"
// @Success 200 {object} api.RespBody{data=schema.TrendListResp}
// @Router /api/trend/list [get]
func (tc *TrendController) GetTrendsList(ctx *gin.Context) {
	req := &schema.TrendListReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := tc.topicService.GetTrendsList(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// GetRandomTrendsList godoc
// @Summary 获取随机热门趋势话题
// @Tags trend
// @Accept  json
// @Produce  json
// @Param request query schema.TrendListReq true "query"
// @Success 200 {object} api.RespBody{data=schema.TrendListResp}
// @Router /api/trend/random [get]
func (tc *TrendController) GetRandomTrendsList(ctx *gin.Context) {
	req := &schema.TrendListReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := tc.topicService.GetRandomTrendsList(ctx, req)
	api.HandleResponse(ctx, resp, err)
}
