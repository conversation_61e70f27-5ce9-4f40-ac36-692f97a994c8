package controller

import (
	"app/api"
	"app/schema"
	"app/service"

	"github.com/gin-gonic/gin"
)

type AdminNpcCommentController struct {
	commentService *service.NpcCommentService
	envSdkService  *service.EnvSdkService
}

func NewAdminNpcCommentController(commentService *service.NpcCommentService, envSdkService *service.EnvSdkService) *AdminNpcCommentController {
	return &AdminNpcCommentController{
		commentService: commentService,
		envSdkService:  envSdkService,
	}
}

// AddNpcComment godoc
// @Summary 新增NPC评论
// @Tags admin_npc_comment
// @Accept  json
// @Produce  json
// @Param request body schema.NpcCommentAddReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.NpcCommentAddResp}
// @Router /admin/npc/comment/add [post]
func (nc *AdminNpcCommentController) AddNpcComment(ctx *gin.Context) {
	req := &schema.NpcCommentAddReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.commentService.AddNpcComment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// GetNpcCommentList godoc
// @Summary 获取NPC评论列表
// @Tags admin_npc_comment
// @Accept  json
// @Produce  json
// @Param request query schema.NpcCommentListReq true "Request query"
// @success 200 {object} api.RespBody{data=schema.NpcCommentListResp}
// @Router /admin/npc/comment/list [get]
func (nc *AdminNpcCommentController) GetNpcCommentList(ctx *gin.Context) {
	req := &schema.NpcCommentListReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.commentService.GetNpcCommentList(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// DeleteNpcComment godoc
// @Summary 删除NPC评论
// @Tags admin_npc_comment
// @Accept  json
// @Produce  json
// @Param request body schema.NpcCommentDeleteReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.BaseDeleteResp}
// @Router /admin/npc/comment/delete [post]
func (nc *AdminNpcCommentController) DeleteNpcComment(ctx *gin.Context) {
	req := &schema.NpcCommentDeleteReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.commentService.DeleteNpcComment(ctx, req.Id)
	api.HandleResponse(ctx, resp, err)
}

// UpdateNpcComment godoc
// @Summary 更新NPC评论数据
// @Tags admin_npc_comment
// @Accept  json
// @Produce  json
// @Param request body schema.NpcCommentUpdateReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.BaseUpdateResp}
// @Router /admin/npc/comment/update [post]
func (nc *AdminNpcCommentController) UpdateNpcComment(ctx *gin.Context) {
	req := &schema.NpcCommentUpdateReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.commentService.UpdateNpcComment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}
