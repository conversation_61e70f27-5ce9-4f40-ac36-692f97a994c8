# 接口GWT清单

## 101 XX接口

- 登录状态：已登录、未登录
- 影响场景2：

| Given[场景上下文]       | When[接口输入]   | Then[接口输出] | 自测状态 | 备注 |
|--------------------|--------------|------------|------|----|
| 用户在登录界面输入账号密码并点击登录 | 已存在且正确的账号密码  | 返回登录成功code | 已验证  | /  |
| 用户在登录界面输入账号密码并点击登录 | 已存在但不正确的账号密码 | 返回用户密码错误   | 未验证  | /  |

# 流程GWT清单

## XX流程

- 登录状态：已登录、未登录
- 影响场景2：

| Given[场景上下文]    | When[流程输入]  | Then[流程输出]    | 自测状态 | 备注 |
|-----------------|-------------|---------------|------|----|
| 某日用户登录失败超过100人次 | 指定检查时间范围为某日 | 触发日登录失败次数消息告警 | 未验证  | /  |