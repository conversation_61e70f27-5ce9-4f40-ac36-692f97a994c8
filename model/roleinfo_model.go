package model

// 用户头像
type RoleInfoModel struct {
	// 角色id
	RoleId                   int64  `gorm:"column:RoleId;primaryKey" json:"roleId"`
	RoleName                 string `gorm:"column:RoleName"`           // 玩家昵称
	Level                    int    `gorm:"column:Level;default:0"`    // 玩家等级
	ServerId                 uint   `gorm:"column:ServerId;default:0"` // 服务器Id
	Signature                string `gorm:"column:Signature"`          // 玩家签名
	HeadBoxId                uint   `gorm:"column:HeadBoxId"`          // 头像框Id
	HeadBoxExpire            int64  `gorm:"column:HeadBoxExpire"`      // 头像框过期时间
	Relation                 string `gorm:"column:Relation"`           // 玩家关系链
	AchievementSelect        string `gorm:"column:AchievementSelect"`  // 玩家成就
	Avatar                   string `gorm:"column:Avatar;NOT NULL"`
	AvatarAudit              int    `gorm:"column:AvatarAudit;default:0;NOT NULL"`
	CallbackUrl              string `gorm:"column:CallbackUrl;NOT NULL"`     // 拼接该回调url给特定用户发消息
	PreAvatar                string `gorm:"column:PreAvatar;NOT NULL"`       // 上一个审核通过的头像
	Sex                      int    `gorm:"column:Sex"`                      // 性别
	Birthday                 int64  `gorm:"column:Birthday"`                 // 生日
	Location                 string `gorm:"column:Location"`                 // 位置
	Background               uint   `gorm:"column:Background"`               // 背景Id
	FaceData                 string `gorm:"column:FaceData"`                 // 面部数据
	SpiritPose               string `gorm:"column:SpiritPose"`               // 灵的姿势
	PlayerPose               string `gorm:"column:PlayerPose"`               // 玩家姿势
	CreateTime               int64  `gorm:"column:CreateTime;NOT NULL"`      // 角色创建时间
	UpdateTime               int64  `gorm:"column:UpdateTime;NOT NULL"`      // 角色更新时间
	BanState                 string `gorm:"column:BanState"`                 // 封禁状态
	WorldLevel               int    `gorm:"column:WorldLevel;default:0"`     // 世界等级
	AchievementNum           uint   `gorm:"column:AchievementNum;default:0"` // 成就点
	SpiritNum                uint   `gorm:"column:SpiritNum;default:0"`      // 灵魂数
	Areas                    string `gorm:"column:Areas"`                    // 玩家解锁区域
	LevelUpdateTime          int64  `gorm:"column:LevelUpdateTime"`          // 等级更新时间
	AchievementNumUpdateTime int64  `gorm:"column:AchievementNumUpdateTime"` // 成就更新时间
	SpiritNumUpdateTime      int64  `gorm:"column:SpiritNumUpdateTime"`      // 灵魂更新时间
	SpiritPoseUpdateTime     int64  `gorm:"column:SpiritPoseUpdateTime"`     // 灵的姿势更新时间
}

func (m *RoleInfoModel) TableName() string {
	return "l50_roleinfo"
}
