variables:
  REPO: "hub.fuxi.netease.com/leihuo-ccc-l50/bizgo-l50-social-media-server"
  PROJECT_ID: 833
  PROJECT_ID_MA: 1357
  PROJECT_ID_CBT: 1356

stages:
  - build

build:
  stage: build
  tags:
    - ccc-gitlab-runner
  script:
    - echo "docker username $DOCKER_USER"
    - echo "$DOCKER_PASS" | docker login --username "$DOCKER_USER" --password-stdin hub.fuxi.netease.com
    - tag="${REPO}:${CI_COMMIT_REF_NAME}.${CI_PIPELINE_ID}.${CI_COMMIT_SHORT_SHA}"
    - DOCKER_BUILDKIT=1 docker image build  -t $tag .
    - docker image push $tag
    - docker rmi -f $tag
    - commit_msg=$(git log -1 --pretty=%B | head -c 100)
    - curl -X POST -d "receiver=2258086&msg=GO框架镜像打包成功%0Atag:${tag}%0A最终提交commit地址:${CI_PROJECT_URL}/-/commit/${CI_COMMIT_SHA}%0A提交信息：${commit_msg}" "http://qa.leihuo.netease.com:3316/popo_qatool"
    - curl -X POST -d "receiver=${GITLAB_USER_EMAIL}&project_id=${PROJECT_ID}&image=$tag&msg=${commit_msg}" "https://ccc-apipub.leihuo.netease.com/api/ci/pub"
    - curl -X POST -d "receiver=${GITLAB_USER_EMAIL}&project_id=${PROJECT_ID_MA}&image=$tag&msg=${commit_msg}" "https://ccc-apipub.leihuo.netease.com/api/ci/pub"
    - curl -X POST -d "receiver=${GITLAB_USER_EMAIL}&project_id=${PROJECT_ID_CBT}&image=$tag&msg=${commit_msg}" "https://ccc-apipub.leihuo.netease.com/api/ci/pub"
