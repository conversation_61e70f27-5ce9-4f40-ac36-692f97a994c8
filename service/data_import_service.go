package service

import (
	"app/repo"
	"app/service/dataimport"
	"context"

	"github.com/xuri/excelize/v2"
)

type DataImportService struct {
	importService *dataimport.Service
}

// NewDataImportService creates a new DataImportService that wraps the new modular import service
func NewDataImportService(npcRepo *repo.NpcRepo, momentRepo *repo.MomentRepo, topicRepo *repo.TopicRepo, commentRepo *repo.CommentRepo, momentTopicRepo *repo.MomentTopicRepo, roleInfoRepo *repo.RoleInfoRepo) *DataImportService {
	return &DataImportService{
		importService: dataimport.NewService(npcRepo, momentRepo, topicRepo, commentRepo, momentTopicRepo, roleInfoRepo),
	}
}

type ImportResult = dataimport.ImportResult

func (d *DataImportService) ImportFromExcel(ctx context.Context, file *excelize.File) (*ImportResult, error) {
	return d.importService.ImportFromExcel(ctx, file)
}
