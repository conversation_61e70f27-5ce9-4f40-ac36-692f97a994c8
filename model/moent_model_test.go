package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// test extract topic text list
func TestExtractTopicTextList(t *testing.T) {
	moment := &MomentModel{Text: "#测试#"}
	topicTextList := moment.GetTopicTextList()
	assert.Equal(t, topicTextList[0], "测试") // test
	assert.Equal(t, len(topicTextList), 1)

	text2 := "话题好的#测试#话题中间#测试2#"
	moment.Text = text2
	topicTextList2 := moment.GetTopicTextList()
	assert.Equal(t, topicTextList2[0], "测试")  // test
	assert.Equal(t, topicTextList2[1], "测试2") // test
	assert.Equal(t, len(topicTextList2), 2)

	moment.Text = "# 武夫 的挑战# "
	topicTextList3 := moment.GetTopicTextList()
	assert.Equal(t, topicTextList3[0], " 武夫 的挑战") // test
	assert.Equal(t, len(topicTextList3), 1)
}
