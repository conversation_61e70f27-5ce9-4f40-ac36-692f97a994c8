package model

import (
	"time"

	"gorm.io/gorm"
)

const (
	FollowStatusNormal int = 0
	FollowStatusDelete int = -1
)

type FollowModel struct {
	ID         int64 `gorm:"primary_key;auto_increment"`
	RoleId     int64 `gorm:"column:RoleId"`
	TargetId   int64 `gorm:"column:TargetId"`
	Status     int   `gorm:"column:Status"`
	IsFriend   int   `gorm:"column:IsFriend"`
	CreateTime int64 `gorm:"column:CreateTime;autoCreateTime:milli"`
	UpdateTime int64 `gorm:"column:UpdateTime;autoUpdateTime:milli"`
}

func (f *FollowModel) BeforeCreate(tx *gorm.DB) (err error) {
	t := time.Now().UnixMilli()
	tx.Statement.SetColumn("CreateTime", t)
	tx.Statement.SetColumn("UpdateTime", t)
	return
}

func (u *FollowModel) BeforeUpdate(tx *gorm.DB) (err error) {
	t := time.Now().UnixNano() / 1e6
	tx.Statement.SetColumn("UpdateTime", t)
	return nil
}

func (f *FollowModel) TableName() string {
	return "l50_follow"
}

func (f *FollowModel) IsUnFollow() bool {
	return f.Status == FollowStatusDelete
}
