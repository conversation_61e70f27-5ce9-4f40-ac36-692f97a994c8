package repo

import (
	"app/util"
	"context"
	"strconv"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/redis"
)

type TopicIdCache struct {
	*BaseCache
}

func NewTopicIdCache(redisClient *redis.Pools) *TopicIdCache {
	return &TopicIdCache{
		BaseCache: NewBaseCache(redisClient),
	}
}

// sadd to redis set topic id
func (t *TopicIdCache) AddTopicId(ctx context.Context, id int64) error {
	return t.redisClient.SAdd(ctx, t.Key("topic_ids"), id)
}

// sadd batch topic ids to redis set
func (t *TopicIdCache) AddTopicIds(ctx context.Context, ids []int64) error {
	members := []interface{}{}
	for _, id := range ids {
		members = append(members, id)
	}
	return t.redisClient.SAdd(ctx, t.Key("topic_ids"), members...)
}

// get random szie topic id from set
func (t *TopicIdCache) GetRandomTopicIds(ctx context.Context, size int64) ([]int64, error) {
	var ids []int64
	// go lib not support srandmembern, use pipeline as workround
	pipiline := t.redisClient.Pipeline()
	sliceCmd := pipiline.SRandMemberN(ctx, t.Key("topic_ids"), size)
	_, err := pipiline.Exec(ctx)
	if err != nil {
		util.LogWithContext(ctx).WithField("size", size).WithError(err).Error("GetRandomTopicIdsError")
		return nil, err
	}

	members, err := sliceCmd.Result()
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("GetRandomTopicIdsGetSliveCmdError")
		return nil, err
	}

	for _, idVal := range members {
		id, err := strconv.ParseInt(idVal, 10, 64)
		if err != nil {
			util.LogWithContext(ctx).WithError(err).Error("GetRandomTopicIdsParseIdValError")
			return nil, err
		}
		ids = append(ids, id)
	}
	return ids, err
}
