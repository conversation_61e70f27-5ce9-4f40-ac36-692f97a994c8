package dataimport

import (
	"app/assets"
	"app/model"
	"app/util"
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// NpcImporter handles NPC sheet import
type NpcImporter struct {
	importCtx *ImportContext
}

// NewNpcImporter creates a new NPC importer
func NewNpcImporter(importCtx *ImportContext) *NpcImporter {
	return &NpcImporter{
		importCtx: importCtx,
	}
}

// GetSheetName returns the sheet name for NPC import
func (n *NpcImporter) GetSheetName() string {
	return "NPC"
}

// ImportSheet imports NPC data from the NPC sheet
func (n *NpcImporter) ImportSheet(ctx context.Context, file *excelize.File, tx *gorm.DB, result *ImportResult) error {
	stepStartTime := time.Now()
	util.LogWithContext(ctx).Info("startProcessingNpcSheet")

	npcRows, err := file.GetRows(n.GetSheetName())
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("getNpcRowsError")
		return err
	}
	util.LogWithContext(ctx).WithField("total_rows", len(npcRows)).Info("npcSheetTotalRows")

	// Read valid NPC avatar ID list
	var npcAvatars []struct {
		ID  int64  `json:"id"`
		URL string `json:"url"`
	}
	if err := json.Unmarshal(assets.NpcAvatarsData, &npcAvatars); err != nil {
		util.LogWithContext(ctx).WithError(err).Error("unmarshalNpcAvatarsError")
		return err
	}

	errScope := "导入NPC失败："

	if len(npcRows) < 3 { // Excel row 3 is 0-indexed as 2. This is the header row.
		result.Errors = append(result.Errors, errScope+"NPC sheet has less than 3 rows, cannot find header row.")
		return nil
	}

	headerDefinitionRow := npcRows[2] // Actual header values are in the 3rd row (index 2)
	headerMap := make(map[string]int)
	for idx, headerName := range headerDefinitionRow {
		headerMap[strings.TrimSpace(headerName)] = idx
	}

	// Define expected headers based on user's image and assumptions for other fields
	expectedHeaders := map[string]string{
		"roleId":   "Id",
		"avatarId": "SImage", // As per image, under the first "NPC头像"
		"name":     "Name",
	}

	for i, row := range npcRows {
		if i < ImportStartSheetRow-1 {
			continue
		}

		// Check if reached data end marker
		if len(row) > 0 && strings.Contains(row[0], SheetDataEndMarker) {
			util.LogWithContext(ctx).WithField("row", i+1).Info("np；wheetReachedEndMarker")
			break
		}

		if i > 10000 {
			result.Errors = append(result.Errors, errScope+"扫描NPC数据超过10000行 (基于原始文件行号)")
			break
		}

		if err := n.processNpcRow(ctx, tx, result, row, headerMap, expectedHeaders, i+1, errScope); err != nil {
			continue
		}
	}

	result.Npcs.DurationMs = time.Since(stepStartTime).Milliseconds()
	util.LogWithContext(ctx).WithField("imported_count", result.Npcs.Count).WithField("duration_ms", result.Npcs.DurationMs).Info("npcSheetProcessingCompleted")
	return nil
}

// processNpcRow processes a single NPC row
func (n *NpcImporter) processNpcRow(ctx context.Context, tx *gorm.DB, result *ImportResult, row []string, headerMap map[string]int, expectedHeaders map[string]string, rowNum int, errScope string) error {
	roleIdStr := row[headerMap[expectedHeaders["roleId"]]]
	util.LogWithContext(ctx).WithField("row", rowNum).WithField("roleId", roleIdStr).Debug("startProcessingNpcRow")

	roleId, err := CheckId(roleIdStr)
	if err != nil {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("roleId", roleIdStr).WithError(err).Error("npcIdParsingFailed")
		result.Errors = append(result.Errors, errScope+"Invalid NPC ID at row "+strconv.Itoa(rowNum))
		return err
	}
	if roleId == -1 {
		util.LogWithContext(ctx).WithField("row", rowNum).Debug("npcRowIsEmpty")
		return nil
	}
	if roleId == 0 {
		util.LogWithContext(ctx).WithField("row", rowNum).Debug("npcRowSkippedWithTilde")
		return nil
	}

	// Get AvatarId
	var avatarId int64
	avatarIdHeaderName := expectedHeaders["avatarId"]
	if avatarIdCol, headerFound := headerMap[avatarIdHeaderName]; headerFound {
		if avatarIdCol < len(row) {
			avatarIdStr := strings.TrimSpace(row[avatarIdCol])
			if avatarIdStr != "" {
				parsedAvatarId, err := strconv.ParseInt(avatarIdStr, 10, 64)
				if err != nil {
					result.Errors = append(result.Errors, errScope+"无效的 NPC AvatarId '"+avatarIdStr+"' (RoleId: "+roleIdStr+") 在 Excel 行 "+strconv.Itoa(rowNum))
					return err
				}
				avatarId = parsedAvatarId
			}
		} else {
			result.Errors = append(result.Errors, errScope+"NPC AvatarId 列 (表头 '"+avatarIdHeaderName+"') 在 Excel 行 "+strconv.Itoa(rowNum)+" (RoleId: "+roleIdStr+") 数据缺失")
			return errors.New("avatar id column missing")
		}
	} else {
		result.Errors = append(result.Errors, errScope+"表头 '"+avatarIdHeaderName+"' 未找到，无法解析 AvatarId (RoleId: "+roleIdStr+") 在 Excel 行 "+strconv.Itoa(rowNum))
		return errors.New("avatar id header not found")
	}

	var name string
	nameHeaderName := expectedHeaders["name"]
	if nameCol, headerFound := headerMap[nameHeaderName]; headerFound {
		if nameCol < len(row) {
			name = strings.TrimSpace(row[nameCol])
			if name == "" {
				result.Errors = append(result.Errors, errScope+"NPC 名称不能为空 (RoleId: "+roleIdStr+") 在 Excel 行 "+strconv.Itoa(rowNum))
				return errors.New("npc name cannot be empty")
			}
		} else {
			result.Errors = append(result.Errors, errScope+"NPC 名称列 (表头 '"+nameHeaderName+"') 在 Excel 行 "+strconv.Itoa(rowNum)+" (RoleId: "+roleIdStr+") 数据缺失")
			return errors.New("name column missing")
		}
	} else {
		result.Errors = append(result.Errors, errScope+"表头 '"+nameHeaderName+"' 未找到，无法解析名称 (RoleId: "+roleIdStr+") 在 Excel 行 "+strconv.Itoa(rowNum))
		return errors.New("name header not found")
	}

	// Get AccountId (optional string)
	var accountId string
	accountIdHeaderName := expectedHeaders["accountId"]
	if accountIdCol, headerFound := headerMap[accountIdHeaderName]; headerFound {
		if accountIdCol < len(row) {
			accountId = strings.TrimSpace(row[accountIdCol])
		}
	}

	return n.createOrUpdateNpc(ctx, tx, result, roleId, avatarId, name, accountId, rowNum, errScope)
}

// createOrUpdateNpc creates or updates an NPC record using upsert
func (n *NpcImporter) createOrUpdateNpc(ctx context.Context, tx *gorm.DB, result *ImportResult, roleId, avatarId int64, name, accountId string, rowNum int, errScope string) error {
	util.LogWithContext(ctx).WithField("row", rowNum).WithField("roleId", roleId).WithField("name", name).WithField("avatarId", avatarId).Debug("preparingToSaveNpcData")

	npc := model.NpcModel{
		RoleId:    roleId,
		AvatarId:  int(avatarId),
		Name:      name,
		AccountId: accountId,
	}

	// Use upsert with OnConflict to handle duplicate role_id
	err := tx.Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "role_id"}},
		DoUpdates: clause.Assignments(map[string]interface{}{
			"avatar_id":  avatarId,
			"name":       name,
			"account_id": accountId,
			"deleted_at": 0,
		}),
	}).Create(&npc).Error

	if err != nil {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("roleId", roleId).WithError(err).Error("upsertNpcFailed")
		result.Errors = append(result.Errors, errScope+"Failed to upsert NPC at row "+strconv.Itoa(rowNum)+": "+err.Error())
		return err
	}

	util.LogWithContext(ctx).WithField("row", rowNum).WithField("roleId", roleId).WithField("name", name).Info("npcUpsertedSuccessfully")

	// Sync RoleInfo
	if n.importCtx.RoleInfoRepo != nil {
		if err := n.importCtx.RoleInfoRepo.CreateOrUpdateByRoleId(tx, npc); err != nil {
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("roleId", roleId).WithError(err).Error("createOrUpdateRoleInfoFailed")
			result.Errors = append(result.Errors, errScope+"Failed to create/update RoleInfo for NPC at row "+strconv.Itoa(rowNum)+": "+err.Error())
			return err
		}
	}

	result.Npcs.Count++
	return nil
}
