package controller

import (
	"app/api"
	"app/schema"
	"app/service"

	"github.com/gin-gonic/gin"
)

type AdminNpcController struct {
	npcService *service.NpcService
}

func NewAdminNpcController(npcService *service.NpcService) *AdminNpcController {
	return &AdminNpcController{
		npcService: npcService,
	}
}

// AddNpc godoc
// @Summary 新增NPC
// @Tags admin_npc
// @Accept  json
// @Produce  json
// @Param request body schema.NpcAddReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.NpcAddResp}
// @Router /admin/npc/add [post]
func (nc *AdminNpcController) AddNpc(ctx *gin.Context) {
	req := &schema.NpcAddReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.npcService.AddNpc(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// GetNpc godoc
// @Summary 查看单个npc
// @Tags admin_npc
// @Accept  json
// @Produce  json
// @Param  roleId query string true "roleId"
// @success 200 {object} api.RespBody{data=schema.NpcShow}
// @Router /admin/npc/show [get]
func (nc *AdminNpcController) GetNpc(ctx *gin.Context) {
	req := &schema.NpcShowReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.npcService.GetNpc(ctx, req.RoleId)
	api.HandleResponse(ctx, resp, err)
}

// UpdateNpc godoc
// @Summary 更新NPC
// @Tags admin_npc
// @Accept  json
// @Produce  json
// @Param request body schema.NpcUpdateReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.NpcUpdateResp}
// @Router /admin/npc/update [post]
func (nc *AdminNpcController) UpdateNpc(ctx *gin.Context) {
	req := &schema.NpcUpdateReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.npcService.UpdateNpc(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// GetNpcList godoc
// @Summary 获取NPC列表
// @Tags admin_npc
// @Accept  json
// @Produce  json
// @Param request query schema.NpcListReq true "Request query"
// @success 200 {object} api.RespBody{data=schema.NpcListResp}
// @Router /admin/npc/list [get]
func (nc *AdminNpcController) GetNpcList(ctx *gin.Context) {
	req := &schema.NpcListReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.npcService.GetNpcList(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// DeleteNpc godoc
// @Summary 删除NPC
// @Tags admin_npc
// @Accept  json
// @Produce  json
// @Param request body schema.NpcDeleteReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.NpcDeleteResp}
// @Router /admin/npc/delete [post]
func (nc *AdminNpcController) DeleteNpc(ctx *gin.Context) {
	req := &schema.NpcDeleteReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := nc.npcService.DeleteNpc(ctx, req.RoleId)
	api.HandleResponse(ctx, resp, err)
}
