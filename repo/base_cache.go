package repo

import (
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/redis"
)

type BaseCache struct {
	redisClient *redis.Pools
	KeyPrefix   string
}

func NewBaseCache(redisClient *redis.Pools) *BaseCache {
	return &BaseCache{
		redisClient: redisClient,
		KeyPrefix:   "l50_social_media:",
	}
}

// key func is key prefix + key
func (b *BaseCache) Key(key string) string {
	return b.KeyPrefix + key
}
