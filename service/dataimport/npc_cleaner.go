package dataimport

import (
	"app/model"
	"app/util"
	"context"
	"time"

	"gorm.io/gorm"
)

// NpcCleaner handles cleaning up NPC-related data
type Npc<PERSON>lean<PERSON> struct{}

// NewNpcCleaner creates a new NPC cleaner
func NewNpcCleaner() *Npc<PERSON>leaner {
	return &NpcCleaner{}
}

// CleanupNpcData cleans up existing NPC-related data before importing
func (n *NpcCleaner) CleanupNpcData(ctx context.Context, tx *gorm.DB, result *ImportResult, errScope string) error {
	stepStartTime := time.Now()
	util.LogWithContext(ctx).Info("startCleaningNpcData")
	
	// Clean up moments created by NPC roles (role_id between NPC_MIN_ID and NPC_MAX_ID)
	momentDeleteResult := tx.Where("role_id >= ? AND role_id <= ?", model.NPC_MIN_ID, model.NPC_MAX_ID).Delete(&model.MomentModel{})
	if momentDeleteResult.Error != nil {
		util.LogWithContext(ctx).WithError(momentDeleteResult.Error).Error("cleanupNpcMomentsFailed")
		result.Errors = append(result.Errors, errScope+"Failed to cleanup NPC moments: "+momentDeleteResult.Error.Error())
		return momentDeleteResult.Error
	}
	
	// Clean up moment-topic relations for NPC moments
	topicDeleteResult := tx.Where("moment_role_id >= ? AND moment_role_id <= ?", model.NPC_MIN_ID, model.NPC_MAX_ID).Delete(&model.MomentTopicModel{})
	if topicDeleteResult.Error != nil {
		util.LogWithContext(ctx).WithError(topicDeleteResult.Error).Error("cleanupNpcMomentTopicsFailed")
		result.Errors = append(result.Errors, errScope+"Failed to cleanup NPC moment topics: "+topicDeleteResult.Error.Error())
		return topicDeleteResult.Error
	}
	
	// Clean up comments created by NPC roles
	commentDeleteResult := tx.Where("role_id >= ? AND role_id <= ?", model.NPC_MIN_ID, model.NPC_MAX_ID).Delete(&model.CommentModel{})
	if commentDeleteResult.Error != nil {
		util.LogWithContext(ctx).WithError(commentDeleteResult.Error).Error("cleanupNpcCommentsFailed")
		result.Errors = append(result.Errors, errScope+"Failed to cleanup NPC comments: "+commentDeleteResult.Error.Error())
		return commentDeleteResult.Error
	}
	
	// Clean up NPC records themselves
	npcDeleteResult := tx.Where("role_id >= ? AND role_id <= ?", model.NPC_MIN_ID, model.NPC_MAX_ID).Delete(&model.NpcModel{})
	if npcDeleteResult.Error != nil {
		util.LogWithContext(ctx).WithError(npcDeleteResult.Error).Error("cleanupNpcRecordsFailed")
		result.Errors = append(result.Errors, errScope+"Failed to cleanup NPC records: "+npcDeleteResult.Error.Error())
		return npcDeleteResult.Error
	}
	
	// Calculate total deleted records and duration
	totalDeleted := momentDeleteResult.RowsAffected + topicDeleteResult.RowsAffected + commentDeleteResult.RowsAffected + npcDeleteResult.RowsAffected
	cleanupDuration := time.Since(stepStartTime).Milliseconds()
	
	// Update detailed cleanup statistics in result
	result.Cleanup.TotalCount = int(totalDeleted)
	result.Cleanup.MomentsDeleted = int(momentDeleteResult.RowsAffected)
	result.Cleanup.TopicsDeleted = int(topicDeleteResult.RowsAffected)
	result.Cleanup.CommentsDeleted = int(commentDeleteResult.RowsAffected)
	result.Cleanup.NpcsDeleted = int(npcDeleteResult.RowsAffected)
	result.Cleanup.DurationMs = cleanupDuration
	
	util.LogWithContext(ctx).WithField("moments_deleted", momentDeleteResult.RowsAffected).
		WithField("topics_deleted", topicDeleteResult.RowsAffected).
		WithField("comments_deleted", commentDeleteResult.RowsAffected).
		WithField("npcs_deleted", npcDeleteResult.RowsAffected).
		WithField("total_deleted", totalDeleted).
		WithField("duration_ms", cleanupDuration).
		Info("npcDataCleanupCompleted")
	
	return nil
}