package mapper

import (
	"app/model"
	"app/repo"
	"app/schema"
	"context"
	"strconv"
)

type RoleInfoMapper struct {
	avatarRepo *repo.AvatarRepo
}

func NewRoleInfoMapper(avatarRepo *repo.AvatarRepo) *RoleInfoMapper {
	return &RoleInfoMapper{
		avatarRepo: avatarRepo,
	}
}

func (m *RoleInfoMapper) ToTinyRoleInfo(ctx context.Context, roleInfo model.RoleInfoModel, npc model.NpcModel) schema.TinyRoleInfo {
	avatarId, _ := strconv.Atoi(roleInfo.Avatar)
	avatarUrl := m.avatarRepo.GetAvatarUrl(ctx, avatarId)
	return schema.TinyRoleInfo{
		RoleId:      roleInfo.RoleId,
		RoleName:    roleInfo.RoleName,
		AvatarId:    avatarId,
		AvatarUrl:   avatarUrl,
		IsNpc:       model.IsValidNpcId(roleInfo.RoleId),
		IsCertified: npc.IsVip,
		AccountId:   npc.AccountId,
	}
}
