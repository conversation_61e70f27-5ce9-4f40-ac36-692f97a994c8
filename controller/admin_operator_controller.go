package controller

import (
	"app/api"
	"app/middleware"
	"app/model"
	"app/schema"
	"app/service"
	"net/http"

	"github.com/gin-gonic/gin"
)

type AdminOperatorController struct {
	adminOperatorService *service.AdminOperatorService
}

func NewAdminOperatorController(adminOperatorService *service.AdminOperatorService) *AdminOperatorController {
	return &AdminOperatorController{
		adminOperatorService: adminOperatorService,
	}
}

// AddOperator godoc
// @Summary 新增用户
// @Tags admin_operator
// @Accept  json
// @Produce  json
// @Param request body schema.AdminOperatorAddReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.AdminOperatorAddResp}
// @Router /admin/operator/add [post]
func (c *AdminOperatorController) AddOperator(ctx *gin.Context) {
	req := &schema.AdminOperatorAddReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := c.adminOperatorService.AddOperator(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// DeleteOperator godoc
// @Summary 删除用户
// @Tags admin_operator
// @Accept  json
// @Produce  json
// @Param request body schema.AdminOperatorDeleteReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.BaseDeleteResp}
// @Router /admin/operator/del [post]
func (c *AdminOperatorController) DeleteOperator(ctx *gin.Context) {
	req := &schema.AdminOperatorDeleteReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	loginInfo := middleware.GetAdminLoginInfo(ctx)
	if loginInfo.CorpMail == req.OpenId {
		api.HandleResponse(ctx, nil, api.GetBizErr(api.ErrAdminOperatorDelSelf))
		return
	}
	resp, err := c.adminOperatorService.DeleteOperator(ctx, req.OpenId)
	api.HandleResponse(ctx, resp, err)
}

// UpdateOperator godoc
// @Summary 更新用户
// @Tags admin_operator
// @Accept  json
// @Produce  json
// @Param request body schema.AdminOperatorUpdateReq true "Request body"
// @success 200 {object} api.RespBody{data=schema.AdminOperatorUpdateResp}
// @Router /admin/operator/update [post]
func (c *AdminOperatorController) UpdateOperator(ctx *gin.Context) {
	req := &schema.AdminOperatorUpdateReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := c.adminOperatorService.UpdateOperator(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// GetOperatorList godoc
// @Summary 获取用户列表
// @Tags admin_operator
// @Accept  json
// @Produce  json
// @Param request query schema.AdminOperatorListReq true "Request query"
// @success 200 {object} api.RespBody{data=schema.AdminOperatorListResp}
// @Router /admin/operator/list [get]
func (c *AdminOperatorController) GetOperatorList(ctx *gin.Context) {
	req := &schema.AdminOperatorListReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := c.adminOperatorService.GetOperatorList(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

func (c *AdminOperatorController) CheckIsAdmin(ctx *gin.Context) {
	loginInfo := middleware.GetAdminLoginInfo(ctx)
	if len(loginInfo.CorpMail) == 0 {
		api.HandleResponse(ctx, nil, api.GetBizErr(api.ErrAdminOperatorNotExist))
		ctx.AbortWithStatus(http.StatusForbidden)
		return
	}
	isAdmin := c.adminOperatorService.CheckIsAdmin(ctx, loginInfo.CorpMail, model.AdminNpcScope)
	if isAdmin {
		ctx.Next()
	} else {
		api.HandleResponse(ctx, nil, api.GetBizErr(api.ErrAdminRoleTypeNeedAdmin))
		ctx.AbortWithStatus(http.StatusForbidden)
		return
	}
}

// GetLoginInfo godoc
// @Summary 获取登录用户信息
// @Tags admin_operator
// @Accept  json
// @Produce  json
// @success 200 {object} api.RespBody{data=schema.AdminAuthLoginInfoResp}
// @Router /admin/auth/login_info [get]
func (c *AdminOperatorController) GetLoginInfo(ctx *gin.Context) {
	loginInfo := middleware.GetAdminLoginInfo(ctx)
	resp, err := c.adminOperatorService.GetLoginInfo(ctx, loginInfo)
	if err != nil {
		api.HandleResponse(ctx, nil, err)
		return
	}
	api.HandleResponse(ctx, resp, nil)
}

func (c *AdminOperatorController) CheckWritePermission(ctx *gin.Context) {
	loginInfo := middleware.GetAdminLoginInfo(ctx)
	if len(loginInfo.CorpMail) == 0 {
		api.HandleResponse(ctx, nil, api.GetBizErr(api.ErrAdminOperatorNotExist))
		ctx.AbortWithStatus(http.StatusForbidden)
		return
	}
	isAdmin := c.adminOperatorService.CheckWritePermission(ctx, loginInfo.CorpMail, model.AdminNpcScope)
	if isAdmin {
		ctx.Next()
	} else {
		api.HandleResponse(ctx, nil, api.GetBizErr(api.ErrAdminRoleTypeNeedWritePermission))
		ctx.AbortWithStatus(http.StatusForbidden)
		return
	}
}
