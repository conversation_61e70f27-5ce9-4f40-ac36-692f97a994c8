POST  {{baseUrl}}/api/comment/delete
{
  "roleId": 80634011083,
  "commentId": 48,
  "skey": "{{skey}}"
}


POST  {{baseUrl}}/api/comment/cancel_like
{
  "roleId": 80634011083,
  "commentId": 47,
  "skey": "{{skey}}"
}

POST  {{baseUrl}}/api/comment/like
{
  "roleId": 101023225,
  "commentId": 59,
  "skey": "{{skey}}"
}

# get comeent list
GET {{baseUrl}}/api/comment/list
[QueryStringParams]
skey:{{skey}}
roleId:79716011247

# get comment detail
GET {{baseUrl}}/api/comment/list
[QueryStringParams]
skey:{{skey}}
roleId: 80634011083
momentId: 6


# add comment test sensitive workd
POST  {{baseUrl}}/api/comment/add
{
  "roleId": 80634011083,
  "commentId": 47,
  "text": "小熊维尼",
  "skey": "{{skey}}"
}

