package schema

type AdminOperatorAddReq struct {
	OpenId   string `json:"openId"`                         //openId
	FullName string `json:"fullName"`                       //全名
	RoleType int8   `json:"roleType" binding:"oneof=0 1 2"` //角色类型 0 普通运营  1 管理员  2 只读账号
}

type AdminOperatorDeleteReq struct {
	OpenId string `json:"openId"` //openId
}

type AdminOperatorDeleteResp struct {
	Id int64 `json:"id"` //id
}

type AdminOperatorUpdateReq struct {
	OpenId   string `json:"openId"`                         //openId
	FullName string `json:"fullName"`                       //全名
	RoleType int8   `json:"roleType" binding:"oneof=0 1 2"` //角色类型 0 普通运营  1 管理员 2 只读账号
}

type AdminOperatorUpdateResp struct {
	Id int64 `json:"id"` //id
}

type AdminOperatorListReq struct {
	AdminPagination
}

type AdminOperatorListResp struct {
	Total int64           `json:"total"` //总数
	List  []AdminOperator `json:"list"`  //列表
}

type AdminOperator struct {
	AdminOperatorAddReq
	Id         int64 `json:"id"`         //id
	CreateTime int64 `json:"createTime"` //创建时间
}

type AdminOperatorAddResp struct {
	Id         int64 `json:"id"`         //id
	CreateTime int64 `json:"createTime"` //创建时间
}

type AdminAuthLoginInfoResp struct {
	Id         int64  `json:"id"`
	OpenId     string `json:"openId"`
	RoleType   int8   `json:"roleType"`
	CreateTime int64  `json:"createTime"`
}
