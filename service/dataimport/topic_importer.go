package dataimport

import (
	"app/model"
	"app/util"
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// TopicImporter handles Topic sheet import
type TopicImporter struct {
	importCtx *ImportContext
}

// NewTopicImporter creates a new Topic importer
func NewTopicImporter(importCtx *ImportContext) *TopicImporter {
	return &TopicImporter{
		importCtx: importCtx,
	}
}

// GetSheetName returns the sheet name for Topic import
func (t *TopicImporter) GetSheetName() string {
	return "Topic"
}

// ImportSheet imports topic data from the Topic sheet
func (t *TopicImporter) ImportSheet(ctx context.Context, file *excelize.File, tx *gorm.DB, result *ImportResult) error {
	stepStartTime := time.Now()
	util.LogWithContext(ctx).Info("startProcessingTopicSheet")
	errScope := "导入Topic失败："

	topicRows, err := file.GetRows(t.GetSheetName())
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("getTopicRowsError")
		return err
	}
	util.LogWithContext(ctx).WithField("total_rows", len(topicRows)).Info("topicSheetTotalRows")

	if len(topicRows) < 3 {
		result.Errors = append(result.Errors, errScope+"Topic sheet has less than 3 rows, cannot find header row.")
		return nil
	}

	headerDefinitionRow := topicRows[2] // Actual header values are in the 3rd row (index 2)
	headerMap := make(map[string]int)
	for idx, headerName := range headerDefinitionRow {
		headerMap[strings.TrimSpace(headerName)] = idx
	}

	// Define expected headers based on user's image and assumptions for other fields
	expectedHeaders := map[string]string{
		"id":   "Id",
		"name": "TypeName",
	}

	for i, row := range topicRows {
		if i < ImportStartSheetRow-1 { // Skip rows before data (e.g., title, default, and actual header row)
			continue
		}

		// Check if reached data end marker
		if len(row) > 0 && strings.Contains(row[0], SheetDataEndMarker) {
			util.LogWithContext(ctx).WithField("row", i+1).Info("topicSheetReachedEndMarker")
			break
		}

		if err := t.processTopicRow(ctx, tx, result, row, headerMap, expectedHeaders, i+1, errScope); err != nil {
			continue
		}
	}

	result.Topics.DurationMs = time.Since(stepStartTime).Milliseconds()
	util.LogWithContext(ctx).WithField("imported_count", result.Topics.Count).WithField("duration_ms", result.Topics.DurationMs).Info("topicSheetProcessingCompleted")
	return nil
}

// processTopicRow processes a single Topic row
func (t *TopicImporter) processTopicRow(ctx context.Context, tx *gorm.DB, result *ImportResult, row []string, headerMap map[string]int, expectedHeaders map[string]string, rowNum int, errScope string) error {
	idStr := row[headerMap[expectedHeaders["id"]]]
	util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", idStr).Debug("startProcessingTopicRow")

	id, err := CheckId(idStr)
	if err != nil {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", idStr).WithError(err).Error("topicIdParsingFailed")
		result.Errors = append(result.Errors, errScope+"Invalid Topic ID at row "+strconv.Itoa(rowNum))
		return err
	}
	if id == -1 {
		util.LogWithContext(ctx).WithField("row", rowNum).Debug("topicRowIsEmpty")
		return nil
	}
	if id == 0 {
		util.LogWithContext(ctx).WithField("row", rowNum).Debug("topicRowSkippedWithTilde")
		return nil
	}

	name := row[headerMap[expectedHeaders["name"]]]
	if name == "" {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).Error("topicNameCannotBeEmpty")
		result.Errors = append(result.Errors, errScope+"Topic name cannot be empty at row "+strconv.Itoa(rowNum))
		return errors.New("topic name cannot be empty")
	}
	util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithField("name", name).Debug("preparingToSaveTopicData")

	return t.createOrUpdateTopic(ctx, tx, result, id, name, rowNum, errScope)
}

// createOrUpdateTopic creates or updates a Topic record
func (t *TopicImporter) createOrUpdateTopic(ctx context.Context, tx *gorm.DB, result *ImportResult, id int64, name string, rowNum int, errScope string) error {
	var topic model.TopicModel
	// Delete same name configuration (unique index constraint)
	_ = tx.Where("name = ?", name).First(&topic).Error
	if topic.ID > 0 {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("existing_topic_id", topic.ID).WithField("name", name).Debug("deletingDuplicateTopicByName")
		err := tx.Model(&model.TopicModel{}).Delete(&topic).Error
		if err != nil {
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithError(err).Error("deleteDuplicateTopicFailed")
			result.Errors = append(result.Errors, errScope+"Failed to delete duplicate topic at row "+strconv.Itoa(rowNum)+": "+err.Error())
			return err
		}
		topic = model.TopicModel{}
	}

	if err := tx.Where("id = ?", id).First(&topic).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			topic = model.TopicModel{
				BaseModel: model.BaseModel{
					ID: id,
				},
				Name: name,
			}
			if err := tx.Create(&topic).Error; err != nil {
				util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithError(err).Error("createTopicFailed")
				result.Errors = append(result.Errors, errScope+"Failed to create Topic at row "+strconv.Itoa(rowNum)+": "+err.Error())
				return err
			}
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithField("name", name).Info("topicCreatedSuccessfully")
		} else {
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithError(err).Error("topicDatabaseQueryError")
			result.Errors = append(result.Errors, errScope+"Database error for Topic at row "+strconv.Itoa(rowNum)+": "+err.Error())
			return err
		}
	} else {
		topic.Name = name
		if err := tx.Model(&model.TopicModel{}).Where("id = ?", id).Update("name", name).Error; err != nil {
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithError(err).Error("updateTopicFailed")
			result.Errors = append(result.Errors, errScope+"Failed to update Topic at row "+strconv.Itoa(rowNum)+": "+err.Error())
			return err
		}
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithField("name", name).Info("topicUpdatedSuccessfully")
	}

	result.Topics.Count++
	return nil
}
