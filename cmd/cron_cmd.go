package cmd

import (
	"fmt"
	cron2 "github.com/robfig/cron/v3"
	"github.com/spf13/cobra"
	"sync"
)

func init() {
	root.AddCommand(cron)
}

var c *cron2.Cron
var cronOnce sync.Once

var cron = &cobra.Command{
	Use:   "cron",
	Short: "Start Cron Server",
	Long:  "Start Cron Server",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()
		err := CronRun(cmd, args)
		if err != nil {
			fmt.Println(err)
		}
	},
}

func AddCronJob(spec string, cmd cron2.Job) (cron2.EntryID, error) {
	cronOnce.Do(func() {
		c = cron2.New(cron2.WithSeconds())
	})
	return c.AddJob(spec, cmd)
}

func CronRun(cmd *cobra.Command, args []string) error {
	c.Start()
	listenStop(func() error {
		ctx := c.Stop()
		<-ctx.Done()
		return nil
	})
	return nil
}
