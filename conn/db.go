package conn

import (
	"app/config"
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var dbMap = make(map[string]*gorm.DB)

func GetDB(kl ...string) *gorm.DB {
	key := DB1
	if len(kl) > 0 {
		key = kl[0]
	}

	db, exists := dbMap[key]
	if !exists {
		fmt.Println(fmt.Errorf("database %s not connected", key))
		return nil
	}
	return db
}

const DB1 = "db1"

var db1Once = sync.Once{}

func ConnectDB1(conf *config.MySQLConfig) {
	db1Once.Do(func() {
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", conf.Username, conf.Password, conf.Host, conf.Port, conf.DBName)

		logLevel := logger.Silent
		parameterizedQueries := true
		if config.C.TestCfg.DBDebug {
			logLevel = logger.Info
			parameterizedQueries = false
		}
		dbLogger := logger.New(
			log.New(os.Stdout, "\n", log.LstdFlags), // io writer
			logger.Config{
				SlowThreshold:             time.Second,          // Slow SQL threshold
				LogLevel:                  logLevel,             // Log level
				IgnoreRecordNotFoundError: false,                // Ignore ErrRecordNotFound error for logger
				ParameterizedQueries:      parameterizedQueries, // Don't include params in the SQL log
				Colorful:                  true,                 // Disable color
			},
		)

		db, err := gorm.Open(mysql.Open(dsn+"?charset=utf8mb4&parseTime=True&loc=Local"), &gorm.Config{
			Logger: dbLogger,
		})
		if err != nil {
			log.Fatal(dsn, err)
		}
		sqlDB, err := db.DB()
		if err != nil {
			log.Fatal(dsn, err)
		}
		sqlDB.SetMaxIdleConns(conf.MaxIdle)
		sqlDB.SetMaxOpenConns(conf.MaxOpen)
		sqlDB.SetConnMaxLifetime(time.Duration(conf.ConnMaxLifetime) * time.Minute)
		fmt.Printf("database %s conn success ! %v\n", DB1, sqlDB.Ping())
		dbMap[DB1] = db
	})
}
