package controller

import (
	"app/api"
	"app/config"
	"app/schema"
	"app/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

type MomentController struct {
	momentService *service.MomentService
}

func NewMomentController(momentService *service.MomentService) *MomentController {
	return &MomentController{
		momentService: momentService,
	}
}

// GetReccMomentList godoc
// @Summary 动态-获取推荐的动态列表
// @Tags moment
// @Accept  json
// @Produce  json
// @Param request query schema.ReccMomentListReq true "query"
// @success 200 {object} api.RespBody{data=schema.MomentListResp}
// @Router /api/moment/recc_list [get]
func (mc *MomentController) GetReccMomentList(ctx *gin.Context) {
	req := &schema.ReccMomentListReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := mc.momentService.GetReccMomentList(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// GetMomentListByTopic godoc
// @Summary 动态-话题下动态列表
// @Tags moment
// @Accept  json
// @Produce  json
// @Param request query schema.MomentListByTopicReq true "query"
// @success 200 {object} api.RespBody{data=schema.MomentListResp}
// @Router /api/moment/list_by_topic [get]
func (mc *MomentController) GetMomentListByTopic(ctx *gin.Context) {
	req := &schema.MomentListByTopicReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := mc.momentService.GetMomentListByTopic(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// GetFollowMomentList godoc
// @Summary 动态-获取关注的动态列表
// @Tags moment
// @Accept  json
// @Produce  json
// @Param request query schema.FollowMomentListReq true "query"
// @success 200 {object} api.RespBody{data=schema.MomentListResp}
// @Router /api/moment/follow_list [get]
func (mc *MomentController) GetFollowMomentList(ctx *gin.Context) {
	req := &schema.FollowMomentListReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := mc.momentService.GetFollowMomentList(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// GetMomentDetail godoc
// @Summary 动态-获取详情
// @Tags moment
// @Accept  json
// @Produce  json
// @Param request query schema.MomentDetailReq true "query"
// @success 200 {object} api.RespBody{data=schema.MomentShow}
// @Router /api/moment/detail [get]
func (mc *MomentController) GetMomentDetail(ctx *gin.Context) {
	req := &schema.MomentDetailReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := mc.momentService.GetMomentDetail(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// GetMomentCollectList godoc
// @Summary 获取动态收藏列表
// @Tags moment_collect
// @Accept  json
// @Produce  json
// @Param request query schema.MomentCollectListReq true "req"
// @success 200 {object} api.RespBody{data=schema.MomentCollectListResp}
// @Router /api/moment/collect_list [get]
func (mc *MomentController) GetMomentCollectList(ctx *gin.Context) {
	req := &schema.MomentCollectListReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := mc.momentService.GetMomentCollectList(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// CollectMoment godoc
// @Summary 收藏动态
// @Tags moment_collect
// @Accept  json
// @Produce  json
// @Param request body schema.MomentCollectReq true "req"
// @success 200 {object} api.RespBody{data=schema.MomentActionResp}
// @Router /api/moment/collect [post]
func (mc *MomentController) CollectMoment(ctx *gin.Context) {
	req := &schema.MomentCollectReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := mc.momentService.CollectMoment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// CancelCollectMoment godoc
// @Summary 取消收藏动态
// @Tags moment_collect
// @Accept  json
// @Produce  json
// @Param request body schema.MomentCollectReq true "req"
// @success 200 {object} api.RespBody{data=schema.MomentActionResp}
// @Router /api/moment/cancel_collect [post]
func (mc *MomentController) CancelCollectMoment(ctx *gin.Context) {
	req := &schema.MomentCollectReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := mc.momentService.CancelCollectMoment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// LikeMoment godoc
// @Summary 点赞动态
// @Tags moment_like
// @Accept  json
// @Produce  json
// @Param request body schema.MomentLikeReq true "req"
// @success 200 {object} api.RespBody{data=schema.MomentActionResp}
// @Router /api/moment/like [post]
func (mc *MomentController) LikeMoment(ctx *gin.Context) {
	req := &schema.MomentLikeReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := mc.momentService.LikeMoment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// CancelLikeMoment godoc
// @Summary 取消点赞动态
// @Tags moment_like
// @Accept  json
// @Produce  json
// @Param request body schema.MomentLikeReq true "req"
// @success 200 {object} api.RespBody{data=schema.MomentActionResp}
// @Router /api/moment/cancel_like [post]
func (mc *MomentController) CancelLikeMoment(ctx *gin.Context) {
	req := &schema.MomentLikeReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := mc.momentService.CancelLikeMoment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// AddMomentByTemplate godoc
// @Summary 动态-服务器通过模板新增动态
// @Tags moment
// @Accept  json
// @Produce  json
// @Param request body schema.MomentAddByTemplateReq true "req"
// @success 200 {object} api.RespBody{data=schema.MomentAddByTemplateResp}
// @Router /api/server/moment/add_by_template [post]
// @Security
func (mc *MomentController) AddMomentByTemplate(ctx *gin.Context) {
	req := &schema.MomentAddByTemplateReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	signBody := map[string]string{
		"roleId":     strconv.FormatInt(req.RoleId, 10),
		"templateId": strconv.FormatInt(req.TemplateId, 10),
		"time":       strconv.FormatInt(req.Time, 10),
	}
	isPass := service.CheckAuthToken(ctx, config.C.Auth.AuthTokenSalt, signBody, req.Token)
	if !isPass {
		api.HandleResponse(ctx, nil, api.GetBizErr(api.ErrAuthTokenInvalid))
		return
	}

	resp, err := mc.momentService.AddMomentByTemplate(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// AddMomentByNpc godoc
// @Summary  动态-新增npc发送给玩家的动态
// @Description 新增npc发送给玩家的动态
// @Tags moment
// @Accept  json
// @Produce  json
// @Param request body schema.MomentAddByNpcReq true "req"
// @success 200 {object} api.RespBody{data=schema.MomentAddByNpcResp}
// @Router /api/server/moment/add_by_npc [post]
func (mc *MomentController) AddMomentByNpc(ctx *gin.Context) {
	req := &schema.MomentAddByNpcReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	signBody := map[string]string{
		"npcId":      strconv.FormatInt(req.NpcId, 10),
		"roleId":     strconv.FormatInt(req.RoleId, 10),
		"text":       req.Text,
		"createTime": strconv.FormatInt(req.CreateTime, 10),
	}
	isPass := service.CheckAuthToken(ctx, config.C.Auth.AuthTokenSalt, signBody, req.Token)
	if !isPass {
		api.HandleResponse(ctx, nil, api.GetBizErr(api.ErrAuthTokenInvalid))
		return
	}

	resp, err := mc.momentService.AddMomentByNpc(ctx, req)
	api.HandleResponse(ctx, resp, err)
}
