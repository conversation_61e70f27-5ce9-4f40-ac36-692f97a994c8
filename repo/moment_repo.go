package repo

import (
	"app/api"
	"app/model"
	"app/schema"
	"app/util"
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
)

// need export for localazation, game table check force id must greate then 0
var categoryList = []schema.MomentCategory{
	{Id: 1, Name: "默认"},
	{Id: 2, Name: "混厄"},
	{Id: 3, Name: "支线"},
	{Id: 4, Name: "氛围"},
}

type MomentRepo struct {
	*BaseRepo[model.MomentModel]
}

const (
	// 最大子动态数量
	MOMENT_MAX_SUB_MOMENT_COUNT = 100
)

func NewMomentRepo(db *gorm.DB) *MomentRepo {
	return &MomentRepo{
		BaseRepo: NewBaseRepo[model.MomentModel](db),
	}
}

func (r *MomentRepo) GetCategoryList() ([]schema.MomentCategory, error) {
	var list []model.MomentCategoryModel
	// todo cache
	r.DB.Model(&model.MomentCategoryModel{}).Find(&list)
	res := make([]schema.MomentCategory, 0, len(list))
	for _, v := range list {
		res = append(res, schema.MomentCategory{
			Id:   int(v.ID),
			Name: v.Name,
		})
	}
	return res, nil
}

func (r *MomentRepo) IsValidCategoryId(categoryId int) bool {
	list, err := r.GetCategoryList()
	if err != nil {
		util.LogWithContext(context.Background()).WithError(err).Error("IsValidCategoryIdFail")
		return false
	}
	for _, v := range list {
		if v.Id == categoryId {
			return true
		}
	}
	return false
}

func (r *MomentRepo) PlayerViewScope(ctx context.Context, roleId int64) *gorm.DB {
	query := r.Scope(ctx).
		// 能看到自己的动态和npc的非任务动态
		Where("(role_id = ? OR role_id < ? ) AND visible = 1", roleId, model.NPC_MAX_ID).
		Where("public_time < ?", time.Now().UnixMilli())
	return query
}

func (r *MomentRepo) DeleteByRoleId(ctx context.Context, roleId int64) (int64, error) {
	return r.DeleteByQuery(ctx, "role_id = ?", roleId)
}

func (r *MomentRepo) FindMomentById(ctx context.Context, momentId int64) (model.MomentModel, error) {
	var m model.MomentModel
	m, err := r.FindOne(ctx, "id = ?", momentId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return m, api.GetBizErr(api.ErrBizMomentNotFound)
		} else {
			util.LogWithContext(ctx).WithError(err).WithField("momentId", momentId).Error("FindMomentByIdFail")
		}
	}
	return m, err
}

// increment like count
func (r *MomentRepo) IncrementLikeCount(ctx context.Context, moment model.MomentModel) (int64, error) {
	c := moment.IncrementLikeCount()
	err := r.DB.Model(&c).Updates(map[string]any{"like_count": c.LikeCount, "real_like_count": c.RealLikeCount}).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("moment", moment).Error("IncrementMomentLikeCountFail")
		return 0, err
	}
	return c.GetLikeCount(), nil
}

// increment comment count
func (r *MomentRepo) IncrementCommentCount(ctx context.Context, moment model.MomentModel) (int64, error) {
	c := moment.IncrementCommentCount()
	err := r.DB.Model(&c).Updates(map[string]any{"comment_count": c.CommentCount}).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("moment", moment).Error("IncrementMomentCommentCountFail")
		return 0, err
	}
	return c.CommentCount, nil
}

// decrement comment count
func (r *MomentRepo) DecrementCommentCount(ctx context.Context, moment model.MomentModel) (int64, error) {
	c := moment.DecrementCommentCount()
	err := r.DB.Model(&c).Updates(map[string]any{"comment_count": c.CommentCount}).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("moment", moment).Error("DecrementMomentCommentCountFail")
		return 0, err
	}
	return c.CommentCount, nil
}

// decrement like count
func (r *MomentRepo) DecrementLikeCount(ctx context.Context, moment model.MomentModel) (int64, error) {
	m := moment.DecrementLikeCount()
	err := r.DB.Model(&m).Updates(map[string]any{"like_count": m.LikeCount, "real_like_count": m.RealLikeCount}).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("moment", moment).Error("DecrementMomentLikeCountFail")
		return 0, err
	}
	return m.GetLikeCount(), nil
}

// count moments by follow role ids
func (r *MomentRepo) GetFollowRoleIdMomentsCount(ctx context.Context, roleId int64, followRoleIds []int64) (int64, error) {
	if len(followRoleIds) == 0 {
		return 0, nil
	}
	query := r.Scope(ctx).
		Where("public_time < ?", time.Now().UnixMilli()).
		Where("role_id in (?)", followRoleIds)

	cnt, err := r.CountByQuery(ctx, query)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).Error("GetFollowRoleIdMomentsCountFail")
		return 0, err
	}
	return cnt, nil
}

func (r *MomentRepo) CreateByTemplate(ctx context.Context, roleId int64, template model.MomentModel) (*model.MomentModel, error) {
	newMoment := template
	newMoment.ID = 0
	newMoment.RoleId = roleId
	newMoment.ShowRoleId = template.RoleId
	newMoment.TemplateId = template.ID
	newMoment.IsTemplate = false
	// 评论需要重置，计算评论数的时候，回取出模板的评论数
	newMoment.CommentCount = 0
	// 玩家创建的moment默认可见
	newMoment.Visible = 1
	newMoment.PublicTime = time.Now().UnixMilli()
	err := r.Scope(ctx).Create(&newMoment).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("newMoment", newMoment).WithField("template", template).Error("CreateMomentByTemplateFail")
		return nil, err
	}
	return &newMoment, nil
}

// is valid parent moment id
func (r *MomentRepo) IsValidParentMomentId(momentId int64) error {
	var moment model.MomentModel
	err := r.DB.Model(&moment).Where("id = ?", momentId).First(&moment).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return api.GetBizErr(api.ErrBizMomentNotFound)
		}
		util.LogWithContext(context.Background()).WithError(err).Error("IsValidMomentIdFail")
		return err
	}
	if moment.ParentId > 0 {
		return api.GetBizErr(api.ErrBizParentIdNotRoot)
	}
	return nil
}

func (r *MomentRepo) GetSubListByMomentIds(ctx context.Context, momentIds []int64) ([]model.MomentModel, error) {
	var momentList []model.MomentModel
	err := r.Scope(ctx).Where("parent_id in (?)", momentIds).Find(&momentList).Error
	if err != nil {
		return nil, err
	}
	return momentList, nil
}

// delete by parent id
func (r *MomentRepo) DeleteByParentId(ctx context.Context, parentId int64) (int64, error) {
	return r.DeleteByQuery(ctx, "parent_id = ?", parentId)
}

// find by template id and role id
func (r *MomentRepo) FindIdByTemplateIdAndRoleId(ctx context.Context, templateId int64, roleId int64) (int64, error) {
	var id int64
	err := r.Scope(ctx).Where("template_id = ? AND role_id = ?", templateId, roleId).Pluck("id", &id).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("templateId", templateId).WithField("roleId", roleId).Error("FindIdByTemplateIdAndRoleIdFail")
		return 0, err
	}
	return id, nil
}
