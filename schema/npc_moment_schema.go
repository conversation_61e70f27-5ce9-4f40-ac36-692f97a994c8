package schema

type NpcMomentAddReq struct {
	// 发表动态的npc的RoleId
	RoleId int64 `json:"roleId" binding:"required"`
	// 发表动态的内容
	Text string `json:"text" binding:"required"`
	// 发表动态的图片列表
	ImgList []string `json:"imgList"`
	// 发表动态的视频列表
	VideoList []string `json:"videoList"`
	// 发表动态的视频封面列表
	VideoCoverList []string `json:"videoCoverList"`
	// 发表动态的基础点赞数
	BaseLikeCount int64 `json:"baseLikeCount"`
	// 发表动态的浏览量
	ViewCount int64 `json:"viewCount"`
	// 动态关联的任务id
	TaskId int `json:"taskId"`
	// 动态关联的地区id
	AreaId int `json:"areaId"`
	// 动态是否置顶
	IsTop bool `json:"isTop"`
	// 发表时间
	PublicTime int64 `json:"publicTime"`
	// 动态所属类别id
	CategoryId int `json:"categoryId"`
	// 是否是动态模板
	IsTemplate bool `json:"isTemplate"`
	// 父动态id
	ParentId int64 `json:"parentId,omitempty"`
}

type NpcMomentAddResp struct {
	BaseAddResp
}

type NpcMomentUpdateReq struct {
	// 动态id
	Id     int64  `json:"id" binding:"required"`
	RoleId *int64 `json:"roleId,omitempty"`
	// 动态文本
	Text *string `json:"text,omitempty"`
	// 动态图片
	ImgList *[]string `json:"imgList,omitempty"`
	// 动态视频列表
	VideoList *[]string `json:"videoList,omitempty"`
	// 动态视频封面列表
	VideoCoverList *[]string `json:"videoCoverList"`
	// 动态点赞数
	BaseLikeCount *int64 `json:"baseLikeCount,omitempty"`
	// 动态浏览量
	ViewCount *int64 `json:"viewCount,omitempty"`
	// 动态关联的任务id
	TaskId *int `json:"taskId,omitempty"`
	// 动态关联的地区id
	AreaId *int `json:"areaId,omitempty"`
	// 动态是否置顶
	IsTop *bool `json:"isTop,omitempty"`
	// 发表时间
	PublicTime int64 `json:"publicTime,omitempty"`
	// 动态所属类别id
	CategoryId *int `json:"categoryId"`
	// 是否是模板动态
	IsTemplate *bool `json:"isTemplate"`
	// 父动态id
	ParentId *int64 `json:"parentId"`
}

type NpcMomentShowReq struct {
	// 动态id
	Id int64 `json:"id" form:"id" binding:"required"`
}

type NpcMomentUpdateResp struct {
	BaseUpdateResp
}

type NpcMomentListResp struct {
	List  []NpcMomentShowResp `json:"list"`
	Total int64               `json:"total"`
}

type NpcMomentShowResp struct {
	NpcMomentAddReq
	NpcMomentAddResp
	// 置顶时间
	TopTime int64 `json:"toptime"`
	// 发表动态所属的npc信息
	RoleInfo *TinyRoleInfo `json:"roleInfo"`
	// 动态评论数
	CommentCount int64 `json:"commentCount"`
	// 真实点赞数
	RealLikeCount int64 `json:"realLikeCount"`
	// 合计点赞数
	LikeCount int64 `json:"likeCount"`
	// 子动态列表
	SubList []NpcMomentShowResp `json:"subList"`
}

type NpcMomentListReq struct {
	AdminPagination
	// 动态所属npcId
	RoleId *int64 `json:"roleId" form:"roleId"`
	// 是否有任务
	HasTask *bool `json:"hasTask" form:"hasTask"`
	// 动态所属类别id
	CategoryId *int `json:"categoryId" form:"categoryId"`
	// 动态所属地区id
	AreaId      *int  `json:"areaId" form:"areaId"`
	OnlyShowNpc *bool `json:"onlyShowNpc" form:"onlyShowNpc"`
}

type NpcSubMomentListReq struct {
	//父动态id
	MomentId int64 `json:"momentId" form:"momentId" binding:"required"`
}

type NpcSubMomentListResp struct {
	List []NpcMomentShowResp `json:"list"`
}

type NpcMomentDeleteReq struct {
	// 动态id
	Id int64 `json:"id" form:"id" binding:"required"`
}

type NpcMomentTopReq struct {
	// 动态id
	Id int64 `json:"id" form:"id" binding:"required"`
}

type NpcMomentTopResp = BaseUpdateResp

type NpcMomentDeleteResp struct {
	BaseDeleteResp
	DelCollectCount int64 `json:"delCollectCount"`
}

type NpcmomentRoleInfoMap = map[int64]*TinyRoleInfo

type MomentCategory struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

type NpcMomentListFilterResp struct {
	Categories []MomentCategory `json:"categories"`
}
