-- Active: 1720162447357@@apps-hp.danlu.netease.com@43228@l50
-- npc user table
CREATE TABLE `l50_npc` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT ,
  `role_id` bigint(20) NOT NULL COMMENT 'npc角色id',
  `name` varchar(255) NOT NULL COMMENT 'npc名称',
  `avatar_id` bigint(20) NOT NULL COMMENT 'npc头像id',
  `signature` varchar(255) NOT NULL COMMENT 'npc签名',
  `base_following_count` bigint(20) UNSIGNED NOT NULL COMMENT '基础关注数',
  `base_follower_count` bigint(20) UNSIGNED NOT NULL COMMENT '基础粉丝数',
  `ctime` bigint(20) UNSIGNED NOT NULL COMMENT '创建时间',
  `utime` bigint(20) UNSIGNED NOT NULL COMMENT '更新时间',
  `deleted_at` bigint(20) UNSIGNED NOT NULL COMMENT '删除时间',
  <PERSON>IMAR<PERSON> KEY (`id`),
  UNIQUE KEY `role_id` (`role_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = 'npc用户表';

ALTER TABLE l50_npc
ADD COLUMN is_player_npc tinyint(4) DEFAULT '0' COMMENT '是否是代号玩家npc';

-- moment table
CREATE TABLE `l50_moment` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `text` varchar(255) NOT NULL COMMENT 'moment内容',
  `img_list` varchar(255) NOT NULL COMMENT 'moment图片',
  `img_audit_list` varchar(255) NOT NULL COMMENT 'moment图片审核',
  `base_like_count` bigint(20) unsigned NOT NULL COMMENT '基础点赞数',
  `like_count` bigint(20) unsigned NOT NULL COMMENT '点赞数',
  `comment_count` bigint(20) unsigned NOT NULL COMMENT '评论数',
  `view_count` bigint(20) unsigned NOT NULL COMMENT '浏览量',
  `task_id` bigint(20) unsigned NOT NULL COMMENT '任务id',
  `area_id` bigint(20) unsigned NOT NULL COMMENT '地区id',
  `top_time` bigint(20) unsigned NOT NULL COMMENT '置顶时间, 最大的时间排在前面',
  `ctime` bigint(20) unsigned NOT NULL COMMENT '创建时间',
  `utime` bigint(20) unsigned NOT NULL COMMENT '更新时间',
  `deleted_at` bigint(20) unsigned NOT NULL COMMENT '删除时间',
  `public_time` bigint(20) unsigned NOT NULL COMMENT '发表时间',
  `template_id` bigint(20) DEFAULT 0 COMMENT '创建使用的模板npc_id',
  `category_id` tinyint(3) unsigned NOT NULL COMMENT '动态类型1 => 混厄 2 => 支线 3 => 氛围',
  `real_like_count` bigint(20) unsigned NOT NULL COMMENT '真实点赞数',
  `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '版本',
  `video_list` varchar(255) NOT NULL COMMENT 'moment视频列表',
  `video_audit_list` varchar(255) NOT NULL COMMENT 'moment视频审核列表',
  `visible` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否可见, 默认可见',
  `is_template` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否是模板动态',
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100000 DEFAULT CHARSET=utf8mb4 COMMENT='moment表'

-- add parent_id to moment table
ALTER TABLE l50_moment ADD COLUMN parent_id bigint(20) DEFAULT 0 COMMENT '父动态id';
ALTER TABLE l50_moment ADD INDEX idx_parent_id (parent_id);

ALTER TABLE l50_moment
ADD COLUMN template_id bigint(20) DEFAULT 0 COMMENT '创建使用的模板npc_id';

ALTER TABLE l50_moment
  ADD COLUMN show_role_id bigint(20) DEFAULT 0 COMMENT '展示使用的roleId';


-- fix moment show_role_id for template moment
UPDATE l50_moment as m1 inner join l50_moment as m2 on m1.template_id = m2.id and m1.template_id > 0 set m1.show_role_id = m2.role_id;

-- comment table
CREATE TABLE `l50_comment` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `moment_id` bigint(20) UNSIGNED NOT NULL COMMENT 'moment id',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `moment_role_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户id',
  `base_like_count` bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '基础点赞数',
  `like_count` bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户点赞数',
  `text` varchar(255) NOT NULL COMMENT '评论内容',
  `ctime` bigint(20) UNSIGNED NOT NULL COMMENT '创建时间',
  `utime` bigint(20) UNSIGNED NOT NULL COMMENT '更新时间',
  `deleted_at` bigint(20) UNSIGNED NOT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `moment_id` (`moment_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = 'comment表';

-- l50 roleinfo table
CREATE TABLE `l50_roleinfo` (
  `RoleId` bigint(20) NOT NULL,
  `RoleName` varchar(20) DEFAULT NULL COMMENT '玩家昵称',
  `Level` smallint(5) DEFAULT '0' COMMENT '玩家等级',
  `ServerId` mediumint(7) unsigned DEFAULT '0' COMMENT '服务器Id',
  `Signature` varchar(100) DEFAULT '' COMMENT '玩家签名',
  `HeadBoxId` int(10) unsigned DEFAULT NULL COMMENT '头像框Id',
  `HeadBoxExpire` bigint(20) DEFAULT NULL COMMENT '头像框过期时间',
  `Relation` varchar(200) DEFAULT NULL COMMENT '玩家关系链',
  `AchievementSelect` varchar(200) DEFAULT NULL COMMENT '玩家成就',
  `Avatar` varchar(255) NOT NULL,
  `AvatarAudit` tinyint(4) NOT NULL DEFAULT '0',
  `CallbackUrl` varchar(127) NOT NULL COMMENT '拼接该回调url给特定用户发消息',
  `PreAvatar` varchar(255) NOT NULL DEFAULT '' COMMENT '上一个审核通过的头像',
  `Sex` tinyint(4) DEFAULT NULL COMMENT '性别',
  `Birthday` bigint(20) DEFAULT NULL COMMENT '生日',
  `Location` varchar(200) DEFAULT '' COMMENT '位置',
  `Background` int(10) unsigned DEFAULT NULL COMMENT '背景Id',
  `FaceData` varchar(200) DEFAULT NULL COMMENT '面部数据',
  `SpiritPose` varchar(512) DEFAULT NULL COMMENT '灵的姿势',
  `PlayerPose` varchar(200) DEFAULT NULL COMMENT '玩家姿势',
  `CreateTime` bigint(20) NOT NULL COMMENT '角色创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '角色更新时间',
  `BanState` varchar(255) DEFAULT NULL COMMENT '封禁状态',
  `WorldLevel` smallint(5) DEFAULT '0' COMMENT '世界等级',
  `AchievementNum` int(10) unsigned DEFAULT 0 COMMENT '成就点',
  `SpiritNum` int(10) unsigned DEFAULT '0' COMMENT '灵魂数',
  `Areas` varchar(1000) DEFAULT NULL COMMENT '玩家解锁区域',
  `LevelUpdateTime` bigint(20) DEFAULT NULL COMMENT '等级更新时间',
  `AchievementNumUpdateTime` bigint(20) DEFAULT NULL COMMENT '成就更新时间',
  `SpiritNumUpdateTime` bigint(20) DEFAULT NULL COMMENT '灵魂更新时间',
  `SpiritPoseUpdateTime` bigint(20) DEFAULT NULL COMMENT '灵的姿势更新时间',
  PRIMARY KEY (`RoleId`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '用户头像';

-- l50 moment like table
CREATE TABLE `l50_moment_like` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `moment_id` bigint(20) UNSIGNED NOT NULL COMMENT 'moment id',
  `moment_role_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户id',
  `ctime` bigint(20) UNSIGNED NOT NULL COMMENT '创建时间',
  `utime` bigint(20) UNSIGNED NOT NULL COMMENT '更新时间',
  `deleted_at` bigint(20) UNSIGNED NOT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  UNIQUE KEY `moment_like` (`moment_id`, `role_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = 'moment点赞表';

-- l50 moment collect TABLE
CREATE TABLE `l50_moment_collect` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `moment_id` bigint(20) UNSIGNED NOT NULL COMMENT 'moment id',
  `moment_role_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户id',
  `ctime` bigint(20) UNSIGNED NOT NULL COMMENT '创建时间',
  `utime` bigint(20) UNSIGNED NOT NULL COMMENT '更新时间',
  `deleted_at` bigint(20) UNSIGNED NOT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  UNIQUE KEY `moment_collect` (`moment_id`, `role_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = 'moment收藏表';

-- l50 comment like table
CREATE TABLE `l50_comment_like` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `comment_id` bigint(20) UNSIGNED NOT NULL COMMENT 'comment id',
  `comment_role_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户id',
  `ctime` bigint(20) UNSIGNED NOT NULL COMMENT '创建时间',
  `utime` bigint(20) UNSIGNED NOT NULL COMMENT '更新时间',
  `deleted_at` bigint(20) UNSIGNED NOT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  UNIQUE KEY `comment_like` (`comment_id`, `role_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = 'comment点赞表';


-- l50 topic table
CREATE TABLE `l50_topic` (
     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
     `name` varchar(14) NOT NULL COMMENT '话题名称',
     `ctime` bigint(20) unsigned NOT NULL COMMENT '创建时间',
     `utime` bigint(20) unsigned NOT NULL COMMENT '更新时间',
     `deleted_at` bigint(20) unsigned NOT NULL COMMENT '删除时间',
     `area_id` int(11) DEFAULT NULL COMMENT '区域id',
     PRIMARY KEY (`id`) USING BTREE,
     UNIQUE KEY `name` (`name`) USING BTREE,
     UNIQUE KEY `area_name` (`area_id`,`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=400 DEFAULT CHARSET=utf8mb4 COMMENT='话题表';

-- l50 moment topic TABLE
CREATE TABLE `l50_moment_topic` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `moment_role_id` bigint(20) UNSIGNED NOT NULL COMMENT '动态的roleId',
  `moment_visible` tinyint(4) NOT NULL DEFAULT '1' COMMENT '动态是否可见',
  `moment_id` bigint(20) UNSIGNED NOT NULL COMMENT 'moment id',
  `topic_id` bigint(20) UNSIGNED NOT NULL COMMENT '话题id',
  `ctime` bigint(20) UNSIGNED NOT NULL COMMENT '创建时间',
  `utime` bigint(20) UNSIGNED NOT NULL COMMENT '更新时间',
  `deleted_at` bigint(20) UNSIGNED NOT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `topic_id` (`topic_id`),
  KEY `moment_id` (`moment_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '话题关联moment表';


update l50_moment_topic as t inner join l50_moment as m  on t.moment_id = m.id set t.moment_role_id= m.role_id;
update l50_moment_topic as t inner join l50_moment as m  on t.moment_id = m.id set t.moment_visible = m.visible;

-- l50 openrole admin
CREATE TABLE `l50_openid_role` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `open_id` varchar(50) NOT NULL COMMENT 'corp邮箱',
    `full_name` varchar(127) NOT NULL COMMENT '姓名',
    `scope` tinyint(4) DEFAULT '0' COMMENT '权限后台范围 1 => NPC后台',
    `role_type` tinyint(4) DEFAULT '0' COMMENT '评论状态：0 => 普通运营 1 => 管理员 2 => 超管',
    `ctime` bigint(20) NOT NULL COMMENT '创建时间',
    `utime` bigint(20) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_role` (`open_id`, `scope`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT = 'openId管理后台角色权限表';


-- npc user table
CREATE TABLE `l50_moment_category` (
   `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT ,
   `name` varchar(255) NOT NULL COMMENT '分类名',
   `ctime` bigint(20) UNSIGNED NOT NULL COMMENT '创建时间',
   `utime` bigint(20) UNSIGNED NOT NULL COMMENT '更新时间',
   `deleted_at` bigint(20) UNSIGNED NOT NULL COMMENT '删除时间',
   PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = 'moment分类表';