package repo

import (
	"app/api"
	"app/model"
	"app/util"
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
)

type FollowRepo struct {
	*BaseRepo[model.FollowModel]
}

func NewFollowRepo(db *gorm.DB) *FollowRepo {
	return &FollowRepo{
		BaseRepo: NewBaseRepo[model.FollowModel](db),
	}
}

func (f *FollowRepo) FindByRoleId(ctx context.Context, roleId int64) ([]model.FollowModel, error) {
	followList := []model.FollowModel{}
	err := f.Scope(ctx).Where("Status = 0").Where("RoleId = ?", roleId).Find(&followList).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).Error("FindFollowListFail")
		return followList, err
	}
	return followList, nil
}

func (f *FollowRepo) FindByTargetId(ctx context.Context, targetId int64) ([]model.FollowModel, error) {
	followList := []model.FollowModel{}
	err := f.<PERSON>ope(ctx).Where("Status = 0 ").Where("TargetId = ?", targetId).Find(&followList).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("targetId", targetId).Error("FindFollowListFail")
		return followList, err
	}
	return followList, nil
}

// follow
func (f *FollowRepo) Follow(ctx context.Context, roleId int64, targetId int64) (*model.FollowModel, error) {
	curFollow, err := f.FindOneByQuery(ctx, f.Scope(ctx).Where("RoleId = ? and TargetId = ?", roleId, targetId))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		} else {
			return nil, err
		}
	}
	if curFollow.ID > 0 && curFollow.Status == 0 {
		return nil, api.GetBizErr(api.ErrBizFollowed)
	}
	curFollow.Status = 0
	curFollow.RoleId = roleId
	curFollow.TargetId = targetId
	err = f.DB.WithContext(ctx).Save(&curFollow).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("follow", curFollow).Error("FollowFail")
		return nil, err
	}
	return &curFollow, nil
}

func (f *FollowRepo) Unfollow(ctx context.Context, roleId int64, targetId int64) (model.FollowModel, error) {
	curFollow, err := f.FindOneByQuery(ctx, f.Scope(ctx).Where("RoleId = ? and TargetId = ?", roleId, targetId))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = api.GetBizErr(api.ErrBizNotFollowed)
		} else {
			return curFollow, err
		}
	}
	if curFollow.ID == 0 || curFollow.IsUnFollow() {
		return curFollow, api.GetBizErr(api.ErrBizNotFollowed)
	}
	err = f.DB.WithContext(ctx).Model(&curFollow).Where("ID", curFollow.ID).Updates(map[string]interface{}{
		"Status":     model.FollowStatusDelete,
		"UpdateTime": time.Now().UnixMilli(),
	}).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("follow", curFollow).Error("UnfollowFail")
		return curFollow, err
	}
	return curFollow, nil
}

func (f *FollowRepo) GetIsFollowMap(ctx context.Context, roleId int64, targetIds []int64) (map[int64]bool, error) {
	followList := []model.FollowModel{}
	isFollowMap := make(map[int64]bool)
	if len(targetIds) == 0 {
		return isFollowMap, nil
	}
	err := f.Scope(ctx).Where("RoleId = ?", roleId).Where("TargetId in ?", targetIds).Where("Status = 0").Select("TargetId").Find(&followList).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).Error("GetIsFollowMapFail")
		return nil, err
	}
	for _, follow := range followList {
		isFollowMap[follow.TargetId] = true
	}
	return isFollowMap, nil
}

func (f *FollowRepo) IsFollow(ctx context.Context, roleId int64, targetId int64) (bool, error) {
	follow, err := f.FindOneByQuery(ctx, f.Scope(ctx).Where("Status = 0").Where("RoleId = ? and TargetId = ?", roleId, targetId))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		} else {
			return false, err
		}
	}
	return follow.Status == model.FollowStatusNormal, nil
}

// get follow count for roleId
func (f *FollowRepo) GetFollowCount(ctx context.Context, roleId int64) (int64, error) {
	followCount := int64(0)
	err := f.Scope(ctx).Where("RoleId = ?", roleId).Where("Status = 0").Count(&followCount).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).Error("GetFollowCountFail")
		return followCount, err
	}
	return followCount, nil
}

// get fans count for roleId
func (f *FollowRepo) GetFanCount(ctx context.Context, roleId int64) (int64, error) {
	fanCount := int64(0)
	err := f.Scope(ctx).Where("TargetId = ?", roleId).Where("Status = 0").Count(&fanCount).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).Error("GetFanCountFail")
		return fanCount, err
	}
	return fanCount, nil
}
