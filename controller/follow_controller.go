// Copyright 2019 The Moolim Company. All rights reserved.
// Use of this source code is governed by a MIT style
// license that can be found in the LICENSE file.

package controller

import (
	"app/api"
	"app/schema"
	"app/service"

	"github.com/gin-gonic/gin"
)

type FollowController struct {
	followService *service.FollowService
}

func NewFollowController(followService *service.FollowService) *FollowController {
	return &FollowController{
		followService: followService,
	}
}

// @Summary 获取关注列表
// @Description 获取关注列表
// @Tags follow
// @Param request query schema.FollowListReq true "query"
// @success 200 {object} api.RespBody{data=schema.FollowListResp}
// @Router /api/follow/follow_list [get]
func (c *FollowController) GetFollowList(ctx *gin.Context) {
	req := &schema.FollowListReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := c.followService.GetFollowList(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// @Summary 获取粉丝列表
// @Description 获取粉丝列表
// @Tags follow
// @Param request query schema.FanListReq true "query"
// @success 200 {object} api.RespBody{data=schema.FanListResp}
// @Router /api/follow/fans_list [get]
func (c *FollowController) GetFanList(ctx *gin.Context) {
	req := &schema.FanListReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := c.followService.GetFanList(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// @Summary 关注用户
// @Description 关注用户
// @Tags follow
// @Param request body schema.FollowReq true "req"
//
// @success 200 {object} api.RespBody{data=schema.FollowResp}
// @Router /api/follow/add [post]
func (c *FollowController) Follow(ctx *gin.Context) {
	req := &schema.FollowReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := c.followService.Follow(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// @Summary 取消关注用户
// @Description 取消关注用户
// @Tags follow
// @Param request body schema.FollowReq true "req"
// @success 200 {object} api.RespBody{data=schema.FollowResp}
// @Router /api/follow/delete [post]
func (c *FollowController) Unfollow(ctx *gin.Context) {
	req := &schema.FollowReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := c.followService.Unfollow(ctx, req)
	api.HandleResponse(ctx, resp, err)
}
