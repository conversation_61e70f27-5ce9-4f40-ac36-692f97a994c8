package config

// BizConfig 应用业务配置
type BizConfig struct {
	CrossDomainOrigin []string // 默认跨域中间件跨域Domain列表，需先route中打开跨域中间件方能生效
	StartTime         string   // 默认时间控制中间件开始时间设定，对使用默认时间控制中间件的接口生效
	FinishTime        string   // 默认时间控制中间件结束时间设定，对使用默认时间控制中间件的接口生效

	// 在这里定制业务配置，也可以独立定义结构并加到Conf中
	NpcMaxSize          int  // npc最大数量
	MomentMaxImageCount int  // 每个moment最大图片数量
	MomentMaxVideoCount int  // 每个moment最大视频数量
	EnableUploadUI      bool // 是否开启上传UI页面

	IPWhitList []string
}
