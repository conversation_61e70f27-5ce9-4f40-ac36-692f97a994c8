package router

import (
	"app/api"
	"app/config"
	"app/conn"
	"app/middleware"
	"app/model"
	"app/repo"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	// swagger embed files
	docs "app/docs"
)

func RegisterRoute(router *gin.Engine) {
	// 绑定 pprof 到 Gin 路由
	pprof.Register(router)
	//go func() {
	//	log.Println("Starting pprof on :6060")
	//	log.Println(http.ListenAndServe("localhost:6060", nil)) // 启动 pprof 服务器
	//}()
	c, err := InitAppController(conn.GetDB(), conn.GetRedis(), config.C.EnvSdk)
	if err != nil {
		panic(err)
	}
	router.GET("/docs/scalar", api.RenderScalar())

	// ------------------注册业务逻辑接口---------------------
	apiPrefix := "/l50/social_media"
	r := router.Group(apiPrefix)

	docs.SwaggerInfo.Host = config.C.Swagger.Host
	docs.SwaggerInfo.BasePath = config.C.App.BasePath
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	{
		// 健康检查
		r.GET("/health", api.Health)
	}

	// for web ui
	{
		webUi := router.Group(apiPrefix + "/web")
		webUi.GET("/tuite_upload", c.WebController.TuiteUpload)
	}

	// for game api
	{
		gac := router.Group(apiPrefix + "/api")
		gac.POST("/common/import", middleware.CheckGMIP, c.AdminCommonController.Import)
		gac.Use(middleware.UserLoginCheck(&config.C.Auth))
		gac.GET("/moment/recc_list", c.MomentController.GetReccMomentList)
		gac.GET("/moment/follow_list", c.MomentController.GetFollowMomentList)
		gac.GET("/moment/detail", c.MomentController.GetMomentDetail)
		gac.GET("/moment/list_by_topic", c.MomentController.GetMomentListByTopic)

		gac.POST("/moment/like", c.MomentController.LikeMoment)
		gac.POST("/moment/cancel_like", c.MomentController.CancelLikeMoment)

		gac.POST("/moment/collect", c.MomentController.CollectMoment)
		gac.POST("/moment/cancel_collect", c.MomentController.CancelCollectMoment)
		gac.GET("/moment/collect_list", c.MomentController.GetMomentCollectList)

		gac.GET("/comment/list", c.CommentController.GetCommentList)
		gac.POST("/comment/add", c.CommentController.AddComment)
		gac.POST("/comment/delete", c.CommentController.DeleteComment)
		gac.POST("/comment/like", c.CommentController.LikeComment)
		gac.POST("/comment/cancel_like", c.CommentController.CancelLikeComment)

		gac.GET("/user/profile", c.UserController.GetUserProfile)

		// follow api
		gac.POST("/follow/add", c.FollowController.Follow)
		gac.POST("/follow/delete", c.FollowController.Unfollow)
		gac.GET("/follow/follow_list", c.FollowController.GetFollowList)
		gac.GET("/follow/fans_list", c.FollowController.GetFanList)

		// trends api
		gac.GET("/trend/list", c.TopicController.GetTrendsList)
		gac.GET("/trend/random", c.TopicController.GetRandomTrendsList)

		gas := router.Group(apiPrefix + "/api/server")
		gas.POST("/moment/add_by_template", c.MomentController.AddMomentByTemplate)
		gas.POST("/moment/add_by_npc", c.MomentController.AddMomentByNpc)
	}

	admin := router.Group(apiPrefix + "/admin")
	{
		openIdRepo := repo.NewOpenIdRoleRepo(conn.GetDB())
		admin.Use(middleware.AdminAuth, middleware.CheckAuthForScope(model.AdminNpcScope, openIdRepo))

		checkWritePermission := c.AdminOperatorController.CheckWritePermission

		// admin assets api
		admin.GET("/assets/avatars/list", c.AdminAssetController.ListAvatars)
		admin.GET("/common/get_fp_token", c.AdminCommonController.GetFPToken)
		admin.GET("/common/export_to_excel", c.AdminCommonController.ExportToExcel)

		admin.POST("/npc/add", checkWritePermission, c.AdminNpcController.AddNpc)
		admin.POST("/npc/update", checkWritePermission, c.AdminNpcController.UpdateNpc)
		admin.POST("/npc/delete", checkWritePermission, c.AdminNpcController.DeleteNpc)
		admin.GET("/npc/show", c.AdminNpcController.GetNpc)
		admin.GET("/npc/list", c.AdminNpcController.GetNpcList)

		admin.POST("/npc/moment/add", checkWritePermission, c.AdminNpcMomentController.AddNpcMoment)
		admin.POST("/npc/moment/top", checkWritePermission, c.AdminNpcMomentController.SetNptMomentTop)
		admin.POST("/npc/moment/untop", checkWritePermission, c.AdminNpcMomentController.SetNptMomentUnTop)
		admin.GET("/npc/moment/show", c.AdminNpcMomentController.GetNpcMoment)
		admin.GET("/npc/moment/filters", c.AdminNpcMomentController.GetNpcMomentFilters)
		admin.GET("/npc/moment/list", c.AdminNpcMomentController.GetNpcMomentList)
		admin.GET("/npc/moment/sub_list", c.AdminNpcMomentController.GetNpcSubMomentList)
		admin.POST("/npc/moment/update", checkWritePermission, c.AdminNpcMomentController.UpdateNpcMoment)
		admin.POST("/npc/moment/delete", checkWritePermission, c.AdminNpcMomentController.DeleteNpcMoment)

		admin.GET("/npc/comment/list", c.AdminNpcCommentController.GetNpcCommentList)
		admin.POST("/npc/comment/add", checkWritePermission, c.AdminNpcCommentController.AddNpcComment)
		admin.POST("/npc/comment/delete", checkWritePermission, c.AdminNpcCommentController.DeleteNpcComment)
		admin.POST("/npc/comment/update", checkWritePermission, c.AdminNpcCommentController.UpdateNpcComment)

		checkIsAdmin := c.AdminOperatorController.CheckIsAdmin
		admin.POST("/operator/add", checkIsAdmin, c.AdminOperatorController.AddOperator)
		admin.POST("/operator/del", checkIsAdmin, c.AdminOperatorController.DeleteOperator)
		admin.GET("/operator/list", c.AdminOperatorController.GetOperatorList)
		admin.POST("/operator/update", checkIsAdmin, c.AdminOperatorController.UpdateOperator)
		admin.GET("/auth/login_info", c.AdminOperatorController.GetLoginInfo)
	}
}
