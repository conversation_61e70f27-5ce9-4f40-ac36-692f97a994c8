# 整体流程

- 主线1：登录后进行什么操作获得什么结果
- 主线2：登录后进行什么操作获得什么结果

# 行为动作陈列

- 打开主页
    - 点击登录
    - 点击退出
- 系统定时转账

# GWT清单

## A模块/页面

- 登录状态：已登录、未登录
- 影响场景2：

| Given[场景上下文]   | When[行为操作]         | Then[期望结果]          | 实现方   |
|----------------|--------------------|---------------------|-------|
| 用户已成功注册        | 在登录界面输入正确账号密码并点击登录 | 提示登录成功，3秒后跳转至个人中心页面 | 前端+后端 |
| 用户在订单列表中找到目标订单 | 在滑出菜单中选择删除该订单      | 额外弹出删除二次确认弹窗        | 前端    |