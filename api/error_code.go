package api

import (
	"app/config"
	"strconv"
	"sync"

	stdErrors "errors"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/errors"
)

type Code int64

const OK Code = 0 // OK 成功

// 系统错误码
const (
	ErrUnknownError           Code = 10000 // UnknownError 未知错误
	ErrThirdServiceError      Code = 10001 // ThirdServiceError 三方服务错误
	ErrDatabaseError          Code = 10002 // DatabaseError 数据库错误
	ErrRedisError             Code = 10003 // RedisError Redis错误
	ErrMiddlewareServiceError Code = 10004 // MiddlewareServiceError 中间件服务错误
)

// 业务通用错误码
const (
	ErrNotLoggedIn        Code = 20000 // NotLoggedIn 用户未登录
	ErrPermissionDenied   Code = 20001 // PermissionDenied 用户无权限
	ErrParameterInvalid   Code = 20002 // ParameterInvalid 参数非法
	ErrDataParseError     Code = 20003 // DataParseError 数据解析异常
	ErrDataNotFound       Code = 20004 // DataNotFound 数据不存在
	ErrDataConflict       Code = 20005 // DataConflict 数据冲突
	ErrBizNotStarted      Code = 20006 // BizNotStarted 活动未开始
	ErrBizFinished        Code = 20007 // BizFinished 活动已结束
	ErrServiceMaintenance Code = 20008 // ServiceMaintenance 系统维护中
	ErrTooFrequently      Code = 20009 // TooFrequently 操作过于频繁/未获得锁
	ErrContentSensitive   Code = 20010 // ContentSensitive 内容包含敏感信息
	ErrLoginExpired       Code = 20011 // LoginExpired 登录过期
)

// 业务定制错误码，请从30000开始
const (
	ErrBizNpcNotFound                    Code = 30000 // Npc未找到
	ErrBizNpcCreated                     Code = 30001 // Npc已存在
	ErrBizInvalidMomentCategory          Code = 30002 // 动态所属类别非法
	ErrBizMomentCollected                Code = 30003 // 动态已收藏
	ErrBizMomentNotCollected             Code = 30004 // 动态未收藏
	ErrBizMomentLiked                    Code = 30005 // 动态已点赞
	ErrBizMomentNotLiked                 Code = 30006 // 动态未点赞
	ErrBizMomentNotFound                 Code = 30007 // 动态未找到
	ErrBizCommentNotFound                Code = 30008 // 评论未找到
	ErrBizCommentNotLiked                Code = 30009 // 评论未点赞
	ErrBizCommentLiked                   Code = 30010 // 评论已点赞
	ErrBizRoleInfoNotFound               Code = 30011 // 角色信息未找到
	ErrBizViewCountLessThanLikeCount     Code = 30012 // 点赞数不能小于浏览数
	ErrBizFollowed                       Code = 30013 // 您已经已关注该用户
	ErrBizNotFollowed                    Code = 30014 // 您还未关注该用户
	ErrBizTopicNotExist                  Code = 30015 // 话题不存在
	ErrBizNpcIdInvalid                   Code = 30016 // Npc RoleId必须小于6位数字
	ErrBizAvatarIdInvalid                Code = 30017 // 头像id不合法
	ErrBizInvalidImageUrl                Code = 30018 // 图片url不合法
	ErrBizInvalidVideoUrl                Code = 30019 // 视频url不合法
	ErrBizImageVideoBothUnSupported      Code = 30020 // 动态不支持同时上传图片和视频
	ErrBizImageListMaxCountExceeded      Code = 30021 // 图片数量超过最大限制
	ErrBizVideoListMaxCountExceeded      Code = 30022 // 视频数量超过最大限制
	ErrBizInvalidVideoCoverUrl           Code = 30023 // 视频封面url不合法
	ErrBizVideoCoverListMaxCountExceeded Code = 30024 // 视频封面数量超过最大限制
	ErrBizPlayerCommentDisallowModify    Code = 30025 // 玩家评论不允许修改
	ErrRoleInfoNotFound                  Code = 30026 // 角色信息未找到
	ErrNpcMomentTemplateNotFound         Code = 30027 // Npc动态模板未找到
	ErrNpcMomentTemplateNpcNotFound      Code = 30028 // Npc动态模板对应的npc未找到
	ErrNpcMomentTemplateNoTask           Code = 30029 // Npc动态模板未配置任务
	ErrBizTimeInvalid                    Code = 30030 // 时间戳无效
	ErrBizParentIdNotRoot                Code = 30031 // 父动态id必须为根动态
	ErrAdminOperatorExist                Code = 30032 // 管理后台用户已存在
	ErrAdminOperatorNotExist             Code = 30033 // 管理后台用户不存在
	ErrAdminRoleTypeNeedAdmin            Code = 30034 // 管理后台用户类型需要为管理员
	ErrAdminOperatorDelSelf              Code = 30035 // 管理后台用户不能删除自己
	ErrAdminRoleTypeNeedWritePermission  Code = 30036 // 管理后台用户类型需要为修改权限
	ErrAdminNeedAddPermission            Code = 30037 // 管理后台用户需要添加权限
	ErrAdminImportFailed                 Code = 30038 // 管理后台用户需要添加权限
	ErrServerToken                       Code = 30039 // ErrServerToken 鉴权失败

	// 这里要兼容之前服务的错误code
	ErrAuthTokenInvalid Code = -1 // AuthTokenInvalid 授权Token无效
	ErrAuthInvalidSkey  Code = -3 // AuthInvalidSkey 授权信息无效
	ErrAuthIdMisMatch   Code = -4 // AuthLoginFail 登录失败
	ErrAuthNoSession    Code = -5 // AuthIdMisMatch 角色id不匹配
	ErrAuthSkeyExpires  Code = -6 // AuthNoSession 未登录
)

var (
	httpCodeMsgMap map[Code]string
	once           sync.Once
)

// GetMessage 获取Code对应Message
func GetMessage(code Code) string {
	once.Do(initHttpCodeMsgMap)
	msg, ok := httpCodeMsgMap[code]
	if ok {
		return msg
	}
	return "未定义错误"
}

func initHttpCodeMsgMap() {
	httpCodeMsgMap = make(map[Code]string)
	for cs, msg := range config.C.HttpCode {
		c, err := strconv.ParseInt(cs, 10, 64)
		if nil != err {
			continue
		}
		httpCodeMsgMap[Code(c)] = msg
	}
}

func GetBizErr(code Code) *errors.Err {
	return &errors.Err{ErrCode: int64(code), ErrMsg: GetMessage(code)}
}

func IsBizErr(err error, code Code) bool {
	var targetErr *errors.Err
	if stdErrors.As(err, &targetErr) {
		return targetErr.ErrCode == int64(code)
	}
	return false
}
