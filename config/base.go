package config

import (
	"app/schema"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/z/popo"
)

var C = &Conf{
	App: AppConfig{
		HttpListen: ":4001",
	},
	Gin: GinConfig{
		MaxMultipartMemory: 8,
		Pprof:              0,
		Log: GinLogConfig{
			LogPath:        "./logs/",
			AccessFileName: "access.log",
			ErrorFileName:  "error.log",
			ReserveDays:    30,
			ReserveCount:   300,
			MaxSize:        1024,
		},
	},
	Log: LogConfig{
		LogPath:      "./logs/",
		FileName:     "app.log",
		ReserveDays:  30,
		ReserveCount: 300,
		MaxSize:      1024,
		Level:        elog.LvINFO,
	},
	Mysql: MySQLConfig{
		Host:            "127.0.0.1",
		Port:            "3306",
		MaxOpen:         500,
		MaxIdle:         200,
		ConnMaxLifetime: 5,
	},
	Swagger: SwaggerConfig{
		Host: "localhost:4001",
	},
	Redis: RedisConfig{
		Addr: "127.0.0.1:6379",
	},
	HttpClient: HttpClientConfig{
		Timeout:             5000,
		MaxIdleConnsPerHost: 200,
		MaxConnsPerHost:     500,
		IdleConnTimeout:     5,
	},
	Popo: popo.Config{
		Timeout: 5000,
	},
	Biz: BizConfig{
		NpcMaxSize: 200,
	},
	Auth: AuthConfig{
		SkeySecret:      "",
		EnableCheatSkey: false,
		CheatSkey:       "CHEAT_SKEY_ONLY_FOR_TEST",
		AuthTokenSalt:   "",
	},
}

// Conf 项目完整配置
type Conf struct {
	App        AppConfig
	Gin        GinConfig
	Swagger    SwaggerConfig
	Log        LogConfig
	Mysql      MySQLConfig
	Redis      RedisConfig
	HttpClient HttpClientConfig
	Popo       popo.Config
	Biz        BizConfig
	HttpCode   HttpCodeConfig
	TestCfg    TestConfig
	Filepicker FilepickerCfg
	CorpAuth   CorpAuthCfg
	EnvSdk     EnvSdkCfg
	Auth       AuthConfig
}

// TestConfig 测试配置
type TestConfig struct {
	DBDebug        bool
	SkipTokenCheck bool
	SkipIpCheck    bool
}

// AppConfig 应用配置
type AppConfig struct {
	AppName    string // 应用名称，同时影响日志的基础app字段属性
	HttpListen string // Http Server监听地址，默认:9992
	TcpListen  string // Tcp Server监听地址，默认:9993
	RpcListen  string // Rpc Server监听地址（暂时无用）
	Language   string // 应用语言（已废弃），错误消息请通过HttpCodeConfig实现
	BasePath   string // 接口基础路径
}

// GinConfig Gin框架相关开发配置
type GinConfig struct {
	MaxMultipartMemory int // 具体见gin.Engine.MaxMultipartMemory，单位：MB，默认8
	Pprof              int // Gin pprof开关，0-关；1-打开，默认0
	Log                GinLogConfig
}

// GinLogConfig Gin日志配置
type GinLogConfig struct {
	LogPath        string // Gin日志路径，默认./logs
	AccessFileName string // Gin access日志文件名，默认access.log
	ErrorFileName  string // Gin error日志文件名，默认error.log
	ReserveDays    int    // 日志保留天数，默认30
	ReserveCount   int    // 日志保留数量，默认300
	MaxSize        int    // 触发日志切割大小，单位：MB，默认1024
	Schema         string // 伏羲采集指定Schema，根据项目自行修改
}

// LogConfig 应用日志配置
type LogConfig struct {
	LogPath      string     // 日志路径，默认./logs
	FileName     string     // 日志文件，默认app.log
	ReserveDays  int        // 日志保留天数，默认30
	ReserveCount int        // 日志保留数量，默认300
	MaxSize      int        // 触发日志切割大小，单位：MB，默认1024
	Level        elog.Level // 日志等级，默认(elog.LvINFO)4
	GlobalFields struct {
		Schema string // 伏羲采集指定Schema，根据项目自行修改
	}
}

// HttpCodeConfig HTTP业务状态码信息配置列表
type HttpCodeConfig map[string]string

type SwaggerConfig struct {
	Host    string
	DocUrl  string
	Servers []schema.SwaggerServer
}

type FilepickerCfg struct {
	Project   string
	SecretKey string
	DevMode   bool
	Region    string
	Review    string
	Policy    *struct {
		FsizeLimit [2]int
		MimeLimit  []string
	}
	TokenExpire int64
}

type CorpAuthCfg struct {
	SkipAuth bool
	Secret   string
}

type EnvSdkCfg struct {
	// Envsdk 服务地址
	Host string
	// Envsdk 服务超时时间
	Timeout int
}

type AuthConfig struct {
	// 梦岛登陆的skey生成的密钥
	SkeySecret string
	// 是否开启作弊的skey，用于测试环境
	EnableCheatSkey bool
	// 具体的作弊skey
	CheatSkey string
	// 服务器校验合法请求计算token用的salt
	AuthTokenSalt string
}
