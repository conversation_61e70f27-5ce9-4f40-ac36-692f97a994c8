package schema

type TrendListReq struct {
	BaseAuthReq
	AreaId int64 `form:"areaId" json:"areaId"`
	Pagination
}

type TrendInfo struct {
	Id     int64  `json:"id"`
	Hot    int64  `json:"hot"`
	Name   string `json:"name"`
	AreaId int    `json:"areaId"`
}

type TrendListResp struct {
	List  []TrendInfo `json:"list"`
	Total int64       `json:"total"`
}

type TrendRandomListResp struct {
	List []TrendInfo `json:"list"`
}

type TopicHot struct {
	TopicId int64 `json:"topicId"`
	Hot     int64 `json:"hot"`
}

type TopicItem struct {
	Id     int64  `json:"id"`
	Name   string `json:"name"`
	AreaId int    `json:"areaId"`
}
