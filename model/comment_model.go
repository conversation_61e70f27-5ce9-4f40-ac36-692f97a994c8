package model

// comment表
type CommentModel struct {
	BaseCoreVersionModel
	MomentId      int64  `gorm:"column:moment_id;NOT NULL"`       // moment id
	RoleId        int64  `gorm:"column:role_id;NOT NULL"`         // 角色id
	MomentRoleId  int64  `gorm:"column:moment_role_id;NOT NULL"`  // 用户id
	Text          string `gorm:"column:text;NOT NULL"`            // 评论内容
	BaseLikeCount int64  `gorm:"column:base_like_count;NOT NULL"` // 基础点赞数 LikeCount     int64  `gorm:"column:like_count;NOT NULL"`      // 点赞数 = base_like_count + real_like_count
	RealLikeCount int64  `gorm:"column:real_like_count;NOT NULL"` // 实际点赞数
	LikeCount     int64  `gorm:"column:like_count;NOT NULL"`      // 点赞数 = base_like_count + real_like_count
}

func (m *CommentModel) TableName() string {
	return "l50_comment"
}

func (m *CommentModel) GetLikeCount() int64 {
	return m.BaseLikeCount + m.RealLikeCount
}

// increment like count
func (m *CommentModel) IncrementLikeCount() *CommentModel {
	m.RealLikeCount++
	m.LikeCount = m.BaseLikeCount + m.RealLikeCount
	return m
}

// decrement like count
func (m *CommentModel) DecrementLikeCount() *CommentModel {
	m.RealLikeCount = max(m.RealLikeCount-1, 0)
	m.LikeCount = max(m.BaseLikeCount+m.RealLikeCount, 0)
	return m
}

// 业务是配置的评论时间，需要保证动态时间一定大于评论时间
func (c *CommentModel) GetCommentTime(momentCtime int64) int64 {
	if c.Ctime > momentCtime {
		return c.Ctime
	}
	return momentCtime
}
