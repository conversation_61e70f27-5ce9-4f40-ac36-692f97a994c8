//go:build wireinject

package router

import (
	"app/config"
	"app/controller"
	"app/repo"
	"app/service"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/redis"
	"github.com/google/wire"
	"gorm.io/gorm"
)

var controllers = wire.NewSet(
	controller.NewAdminNpcController,
	controller.NewAdminNpcMomentController,
	controller.NewAdminNpcCommentController,
	controller.NewAdminCommonController,
	controller.NewAssetController,
	controller.NewCommentController,
	controller.NewFollowController,
	controller.NewMomentController,
	controller.NewUserController,
	controller.NewTrendController,
	controller.NewAdminOperatorController,
	controller.NewWebController,
)

var repos = wire.NewSet(
	repo.NewAvatarRepo,
	repo.NewCommentLikeRepo,
	repo.NewCommentRepo,
	repo.NewFollowRepo,
	repo.NewMomentCollectRepo,
	repo.NewMomentLikeRepo,
	repo.NewMomentTopicRepo,
	repo.NewMomentRepo,
	repo.NewNpcRepo,
	repo.NewRoleInfoRepo,
	repo.NewTopicRepo,
	repo.NewTopicIdCache,
	repo.NewOpenIdRoleRepo,
)

var services = wire.NewSet(
	service.NewAssetsService,
	service.NewCommentService,
	service.NewFollowService,
	service.NewMomentService,
	service.NewNpcCommentService,
	service.NewNpcMomentService,
	service.NewNpcService,
	service.NewTopicService,
	service.NewUserService,
	service.NewEnvSdkService,
	service.NewDataExportService,
	service.NewDataImportService,
	service.NewAdminOperatorService,
)

func InitAppController(db *gorm.DB, redis *redis.Pools, envSdkCfg config.EnvSdkCfg) (*AppController, error) {
	panic(wire.Build(repos, services, controllers, NewAppController))
}
