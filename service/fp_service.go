package service

import (
	config "app/config"

	filepicker_tools "ccc-gitlab.leihuo.netease.com/pkgo/filepicker-tools"
)

func GetFPClient() *filepicker_tools.FPClient {
	conf := config.C
	var policy *filepicker_tools.PolicyInitOptions
	if conf.Filepicker.Policy != nil {
		policy = &filepicker_tools.PolicyInitOptions{
			FsizeLimit: conf.Filepicker.Policy.FsizeLimit,
			MimeLimit:  conf.Filepicker.Policy.MimeLimit,
		}
	}
	fpClient := filepicker_tools.NewFPClient(filepicker_tools.InitOption{
		Project:     conf.Filepicker.Project,
		SecretKey:   conf.Filepicker.SecretKey,
		DevMode:     conf.Filepicker.DevMode,
		Region:      filepicker_tools.Region(conf.Filepicker.Region),
		Policy:      policy,
		TokenExpire: &conf.Filepicker.TokenExpire,
	})
	return fpClient
}
