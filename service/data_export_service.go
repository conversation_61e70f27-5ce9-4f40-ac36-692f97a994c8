package service

import (
	"app/assets"
	"app/model"
	"app/repo"
	"app/util"
	"context"
	"strconv"
	"strings"

	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

const (
	BATCH_SIZE      = 100
	START_SHEET_ROW = 8
	START_SHEET_COL = "A"
)

type DataExportService struct {
	npcRepo         *repo.NpcRepo
	momentRepo      *repo.MomentRepo
	topicRepo       *repo.TopicRepo
	commentRepo     *repo.CommentRepo
	momentTopicRepo *repo.MomentTopicRepo
	roleInfoRepo    *repo.RoleInfoRepo
}

func NewDataExportService(npcRepo *repo.NpcRepo, momentRepo *repo.MomentRepo, topicRepo *repo.TopicRepo, commentRepo *repo.CommentRepo, momentTopicRepo *repo.MomentTopicRepo, roleInfoRepo *repo.RoleInfoRepo) *DataExportService {
	return &DataExportService{
		npcRepo:         npcRepo,
		momentRepo:      momentRepo,
		topicRepo:       topicRepo,
		commentRepo:     commentRepo,
		momentTopicRepo: momentTopicRepo,
		roleInfoRepo:    roleInfoRepo,
	}
}

func (d *DataExportService) ExportToExcel(ctx context.Context) (*excelize.File, error) {
	tempalteName := "excel/Tuite_Template.xlsx"
	templateFile, err := assets.ExcelAssets.Open(tempalteName)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("templateFile", tempalteName).Error("ExportToExcelOpenTemplateFileError")
		return nil, err
	}
	excelFile, err := excelize.OpenReader(templateFile)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("templateFile", tempalteName).Error("ExceleizeOpenTemplateFileError")
		return nil, err
	}

	// export npc list
	var npcList []model.NpcModel
	err = d.npcRepo.DB.FindInBatches(&npcList, BATCH_SIZE, func(tx *gorm.DB, batch int) error {
		for idx, npc := range npcList {
			startCell := START_SHEET_COL + strconv.Itoa(idx+START_SHEET_ROW)
			excelFile.SetSheetRow("NPC", startCell, &[]interface{}{npc.RoleId, npc.AvatarId, npc.Name, npc.AccountId, npc.BaseFollowingCount, npc.BaseFollowerCount})
		}
		return nil
	}).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("ExportNpcListExcelFail")
		return nil, err
	}

	npcIds := lo.Map(npcList, func(npc model.NpcModel, _ int) int64 {
		return npc.RoleId
	})

	var momentList []model.MomentModel
	commentIdx := 0
	err = d.momentRepo.DB.Where("role_id in ?", npcIds).FindInBatches(&momentList, BATCH_SIZE, func(tx *gorm.DB, batch int) error {
		for idx, moment := range momentList {
			// export comment list
			var commentList []model.CommentModel
			err = d.commentRepo.DB.Where("role_id in (?) AND moment_id = ?", npcIds, moment.ID).FindInBatches(&commentList, BATCH_SIZE, func(tx *gorm.DB, batch int) error {
				for _, comment := range commentList {
					startCell := START_SHEET_COL + strconv.Itoa(commentIdx+START_SHEET_ROW)
					excelFile.SetSheetRow("Comment", startCell, &[]interface{}{comment.ID, comment.RoleId, comment.Text})
					commentIdx++
				}
				return nil
			}).Error
			if err != nil {
				util.LogWithContext(ctx).WithField("moment_id", moment.ID).WithError(err).Error("ExportMomentCommentListExcelFail")
				return err
			}
			startCell := START_SHEET_COL + strconv.Itoa(idx+START_SHEET_ROW)
			commentIdsStr := strings.Join(lo.Map(commentList, func(comment model.CommentModel, _ int) string {
				return strconv.FormatInt(comment.ID, 10)
			}), ";")
			// export moment row
			excelFile.SetSheetRow("Tuite", startCell,
				&[]interface{}{moment.ID, moment.RoleId, moment.Text, commentIdsStr, moment.CategoryId, moment.TaskId, moment.ParentId, moment.IsTemplate, moment.BaseLikeCount, moment.ViewCount})
		}
		return nil
	}).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("ExportMomentListExcelFail")
		return nil, err
	}

	// epoxrt category list
	categoryList, err := d.momentRepo.GetCategoryList()
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("ExportCategoryListExcelFail")
		return nil, err
	}
	for idx, category := range categoryList {
		startCell := START_SHEET_COL + strconv.Itoa(idx+START_SHEET_ROW)
		excelFile.SetSheetRow("Type", startCell, &[]interface{}{category.Id, category.Name})
	}

	// export topic list
	var topicList []model.TopicModel
	err = d.topicRepo.DB.FindInBatches(&topicList, BATCH_SIZE, func(tx *gorm.DB, batch int) error {
		for idx, topic := range topicList {
			startCell := START_SHEET_COL + strconv.Itoa(idx+START_SHEET_ROW)
			excelFile.SetSheetRow("Topic", startCell, &[]interface{}{topic.ID, topic.Name})
		}
		return nil
	}).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("ExportTopicListExcelFail")
		return nil, err
	}

	return excelFile, nil
}
