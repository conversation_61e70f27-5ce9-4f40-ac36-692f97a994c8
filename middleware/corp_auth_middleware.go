package middleware

import (
	"app/api"
	"app/config"
	"app/model"
	"app/repo"
	"net/http"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/login"
	"github.com/gin-gonic/gin"
)

var loginInfoKey = "loginInfo"

func AdminAuth(ctx *gin.Context) {
	if config.C.CorpAuth.SkipAuth {
		ctx.Set("loginInfo", login.CorpInfo{
			CorpMail: "<EMAIL>",
			Nickname: "skip_login",
			FullName: "skip_login",
		})
		ctx.Next()
		return
	}

	corpInfo, err := login.ParseCorpFromCookie(ctx.Request, config.C.CorpAuth.Secret)
	if err != nil {
		elog.WithContext(ctx).With<PERSON>ield("err", err.Error()).Error("ParseCorpFromCookie failed")
		ctx.AbortWithStatus(http.StatusUnauthorized)
	}
	ctx.Set(loginInfoKey, corpInfo)
	ctx.Next()
}

func CheckAuthForScope(scope model.AdminScope, repo *repo.OpenIdRoleRepo) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var corpInfo login.CorpInfo
		corpInfoRaw, ok := ctx.Get(loginInfoKey)
		if !ok {
			ctx.AbortWithStatus(http.StatusUnauthorized)
			return
		}
		corpInfo, ok = corpInfoRaw.(login.CorpInfo)
		if !ok {
			ctx.AbortWithStatus(http.StatusUnauthorized)
			return
		}
		hasPermission, err := repo.CheckOpenIdPermission(ctx, corpInfo.CorpMail, scope, false)
		if err != nil {
			elog.WithContext(ctx).WithField("err", err.Error()).Error("CheckOpenIdPermission failed")
			ctx.AbortWithStatus(http.StatusInternalServerError)
			return
		}
		if !hasPermission {
			api.HandleResponse(ctx, nil, api.GetBizErr(api.ErrAdminNeedAddPermission))
			ctx.AbortWithStatus(http.StatusOK)
			return
		}
		ctx.Next()
	}
}

func GetAdminLoginInfo(ctx *gin.Context) login.CorpInfo {
	loginInfoRaw, ok := ctx.Get(loginInfoKey)
	if !ok {
		return login.CorpInfo{}
	}
	loginInfo, ok := loginInfoRaw.(login.CorpInfo)
	if !ok {
		return login.CorpInfo{}
	}
	return loginInfo
}
