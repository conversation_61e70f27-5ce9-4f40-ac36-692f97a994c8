package repo

import (
	"app/api"
	"app/model"
	"context"
	"errors"

	"gorm.io/gorm"
)

type MomentLikeRepo struct {
	*BaseRepo[model.MomentLikeModel]
}

func NewMomentLikeRepo(db *gorm.DB) *MomentLikeRepo {
	return &MomentLikeRepo{
		BaseRepo: NewBaseRepo[model.MomentLikeModel](db),
	}
}

// add like
func (r *MomentLikeRepo) AddLike(ctx context.Context, roleId int64, moment model.MomentModel) (*model.MomentLikeModel, error) {
	curLike, err := r.FindOneByQuery(ctx, r.Scope(ctx).Unscoped().Where("role_id = ? and moment_id = ?", roleId, moment.ID))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		} else {
			return nil, err
		}
	}
	if curLike.ID > 0 && curLike.DeletedAt == 0 {
		return nil, api.GetBizErr(api.ErrBizMomentLiked)
	}
	addLike := &model.MomentLikeModel{
		RoleId:       roleId,
		MomentId:     moment.ID,
		MomentRoleId: moment.RoleId,
	}
	addLike.ID = curLike.ID
	err = r.DB.WithContext(ctx).Save(&addLike).Error
	if err != nil {
		return nil, err
	}
	return addLike, nil
}

// isLike
func (r *MomentLikeRepo) IsLike(ctx context.Context, roleId int64, momentId int64) (bool, error) {
	var entity model.MomentLikeModel
	err := r.DB.WithContext(ctx).Where("role_id = ? and moment_id= ?", roleId, momentId).First(&entity).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, err
	}
	return entity.ID > 0, nil
}

// delete like
func (r *MomentLikeRepo) DeleteLike(ctx context.Context, roleId int64, momentId int64) (int64, error) {
	return r.DeleteByQuery(ctx, "role_id = ? and moment_id = ?", roleId, momentId)
}

// get isLikeMap by roleId and momentIds
func (r *MomentLikeRepo) GetIsLikeMap(ctx context.Context, roleId int64, momentIds []int64) (map[int64]bool, error) {
	var entityList []model.MomentLikeModel
	isLikeMap := make(map[int64]bool)
	if len(momentIds) == 0 {
		return isLikeMap, nil
	}
	query := r.Scope(ctx).Where("role_id = ?", roleId).Where("moment_id IN (?)", momentIds).Select("moment_id")
	err := query.Find(&entityList).Error
	if err != nil {
		return nil, err
	}
	for _, v := range entityList {
		isLikeMap[v.MomentId] = true
	}
	return isLikeMap, nil
}
