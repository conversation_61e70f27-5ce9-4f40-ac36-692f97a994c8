package schema

type FollowReq = struct {
	BaseAuthReq
	// 关注者角色id
	TargetId int64 `json:"targetId" binding:"required" form:"targetId"`
}

type UnFollowReq = FollowReq

type FollowResp = BaseActionResp

type FollowInfo struct {
	Id         int64        `json:"id"`
	TargetId   int64        `json:"targetId"`
	FollowTime int64        `json:"followTime"`
	RoleInfo   TinyRoleInfo `json:"roleInfo"`
}

type FollowListReq struct {
	BaseAuthReq
	Pagination
}

type FanListReq = FollowListReq

type FollowListResp struct {
	// 关注列表
	List  []FollowInfo `json:"list"`
	Total int64        `json:"total"`
}

type FanListResp struct {
	// 粉丝列表
	List  []FollowInfo `json:"list"`
	Total int64        `json:"total"`
}
