package controller

import (
	"app/api"
	"app/service"

	"github.com/gin-gonic/gin"
)

type AssertController struct {
	assetsService *service.AssetsService
}

func NewAssetController(asserService *service.AssetsService) *AssertController {
	return &AssertController{
		assetsService: asserService,
	}
}

// ListAvatars godoc
// @Summary 获取头像列表
// @Tags admin_asserts
// @Accept  json
// @Produce  json
// @success 200 {object} schema.AvatarItem
// @Router /admin/assets/avatars/list [get]
func (ac *AssertController) ListAvatars(ctx *gin.Context) {
	resp, err := ac.assetsService.ListAvatars(ctx)
	api.HandleResponse(ctx, resp, err)
}
