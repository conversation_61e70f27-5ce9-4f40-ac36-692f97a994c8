package cmd

import (
	"app/config"
	"app/conn"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"github.com/BurntSushi/toml"
	"github.com/spf13/cobra"
)

// cfgFile 全局配置文件地址
var cfgFile string

var root = &cobra.Command{
	Use:     "app",
	Short:   "App Root Command",
	Long:    "App Root Command",
	Example: "",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()
		serverStart()
	},
}

func init() {
	root.PersistentFlags().StringVar(&cfgFile, "config", "", "config file(default is config/config.toml)")
}

func Execute() {
	if err := root.Execute(); nil != err {
		fmt.Fprintln(os.Stderr, "执行命令错误", err)
		os.Exit(1)
	}
}

func bootstrap() {
	// 加载并解析配置
	if "" == cfgFile {
		appEnv := os.Getenv("APP_ENV")
		if len(appEnv) > 0 {
			cfgFile = fmt.Sprintf("conf/config.%s.toml", appEnv)
		} else {
			cfgFile = "conf/config.toml"
		}
	}

	if _, err := toml.DecodeFile(cfgFile, config.C); err != nil {
		fmt.Fprintln(os.Stderr, "Failed to decode config file", err)
		os.Exit(1)
	}
	fmt.Println("start with config file : ", cfgFile)

	// 初始化资源
	InitLog(config.C)
	InitDB(config.C)
	InitRedis(config.C)
	InitHttpClient(config.C)
}

// InitLog 初始化应用的日志资源
func InitLog(conf *config.Conf) {
	log := elog.New()
	log.SetLogPath(conf.Log.LogPath)
	log.SetFileName(conf.Log.FileName)
	log.SetReserveDays(conf.Log.ReserveDays)
	log.SetReserveCount(conf.Log.ReserveCount)
	log.SetMaxSize(conf.Log.MaxSize)
	log.SetLevel(conf.Log.Level)
	log.SetGlobalFields(elog.Fields{
		"schema": conf.Log.GlobalFields.Schema,
		"app":    conf.App.AppName,
		//"body":   elog.Fields{},
	})
	log.Start()
	elog.SetStd(log)
}

// InitDB 初始化应用的DB资源
func InitDB(conf *config.Conf) {
	conn.ConnectDB1(&conf.Mysql)
}

// InitRedis 初始化应用的Redis资源
func InitRedis(conf *config.Conf) {
	conn.ConnectRedis1(&conf.Redis)
}

// InitHttpClient 初始化应用的Http Client资源
func InitHttpClient(conf *config.Conf) {
	conn.InitHttpClient1(&conf.HttpClient)
}

func listenStop(handler func() error) {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	err := handler()
	if nil != err {
		fmt.Fprintln(os.Stderr, "处理结束错误", err)
		os.Exit(1)
	}
}
