package cmd

import (
	"app/conn"
	"app/repo"
	"app/service"
	"app/util"
	"context"

	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(sync_npc_info)
}

var sync_npc_info = &cobra.Command{
	Use:   "sync_npc_info",
	Short: "sync npc info",
	Long:  "sync npc info",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()
		db := conn.GetDB()
		npcRepo := repo.NewNpcRepo(db)
		roleInfoRepo := repo.NewRoleInfoRepo(db)
		npcSyncService := service.NewNpcSyncService(npcRepo, roleInfoRepo)
		ctx := context.Background()
		err := npcSyncService.SyncNpcInfo(ctx)
		if err != nil {
			util.LogWithContext(ctx).WithError(err).Error("RunSyncNpcInfoFail")
		}
	},
}
