package service

import (
	"app/api"
	"app/config"
	"app/conn"
	"app/mapper"
	"app/model"
	"app/repo"
	"app/schema"
	"app/util"
	"context"
	"errors"
	"strings"
	"time"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
)

type MomentService struct {
	momentRepo        *repo.MomentRepo
	momentCollectRepo *repo.MomentCollectRepo
	momentLikeRepo    *repo.MomentLikeRepo
	momentTopicRepo   *repo.MomentTopicRepo
	followRepo        *repo.FollowRepo
	roleInfoRepo      *repo.RoleInfoRepo
	avatarRepo        *repo.AvatarRepo
	npcRepo           *repo.NpcRepo
	topicRepo         *repo.TopicRepo
	topicIdCache      *repo.TopicIdCache
	userService       *UserService
	topicService      *TopicService
}

func NewMomentService(
	momentRepo *repo.MomentRepo,
	momentCollectRepo *repo.MomentCollectRepo,
	momentLikeRepo *repo.MomentLikeRepo,
	roleInfoRepo *repo.RoleInfoRepo,
	followRepo *repo.FollowRepo,
	avatarRepo *repo.AvatarRepo,
	npcRepo *repo.NpcRepo,
	topicRepo *repo.TopicRepo,
	momentTopicRepo *repo.MomentTopicRepo,
	topicIdCache *repo.TopicIdCache,
	userService *UserService,
	topicService *TopicService,
) *MomentService {
	return &MomentService{
		momentRepo:        momentRepo,
		momentCollectRepo: momentCollectRepo,
		momentLikeRepo:    momentLikeRepo,
		momentTopicRepo:   momentTopicRepo,
		roleInfoRepo:      roleInfoRepo,
		followRepo:        followRepo,
		avatarRepo:        avatarRepo,
		npcRepo:           npcRepo,
		topicRepo:         topicRepo,
		topicIdCache:      topicIdCache,
		userService:       userService,
		topicService:      topicService,
	}
}

func (m *MomentService) GetReccMomentList(ctx context.Context, req *schema.ReccMomentListReq) (schema.MomentListResp, error) {
	resp := schema.MomentListResp{List: []schema.MomentShow{}}
	curRoleId := req.RoleId
	baseQuery := func() *gorm.DB {
		return m.momentRepo.PlayerViewScope(ctx, curRoleId)
	}
	listQuery := baseQuery().Order("top_time desc").Order("public_time desc")
	momentList, err := m.momentRepo.FindWithPagination(ctx, listQuery, req.Pagination)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("req", req).Error("GetReccMomentListFail")
		return resp, err
	}
	showList, err := m.FillMomentShowList(ctx, momentList, curRoleId, false)
	if err != nil {
		return resp, err
	}
	total, err := m.momentRepo.CountByQuery(ctx, baseQuery())
	if err != nil {
		return resp, err
	}
	resp = schema.MomentListResp{
		List:  showList,
		Total: total,
	}
	return resp, nil
}

// fill moments to momentShowList
func (m *MomentService) FillMomentShowList(ctx context.Context, momentList []model.MomentModel, curRoleId int64, isAllFollow bool) ([]schema.MomentShow, error) {
	resp := []schema.MomentShow{}
	momentIds := lo.Map(momentList, func(v model.MomentModel, _ int) int64 { return v.ID })
	roleIds := lo.Map(momentList, func(v model.MomentModel, _ int) int64 { return v.GetShowRoleId() })

	errGroup, groupCtx := errgroup.WithContext(ctx)

	var roleInfoList []schema.TinyRoleInfo
	var isCollectMap map[int64]bool
	var isLikeMap map[int64]bool
	var isFollowMap map[int64]bool

	errGroup.Go(func() error {
		var err error
		roleInfoList, err = m.userService.GetUserInfoList(groupCtx, roleIds)
		return err
	})

	// get isCollectMap
	errGroup.Go(func() error {
		var err error
		isCollectMap, err = m.momentCollectRepo.GetIsCollectMap(groupCtx, curRoleId, momentIds)
		return err
	})

	// get isLikeMap
	errGroup.Go(func() error {
		var err error
		isLikeMap, err = m.momentLikeRepo.GetIsLikeMap(groupCtx, curRoleId, momentIds)
		return err
	})

	// get isFollowMap from momentList
	errGroup.Go(func() error {
		var err error
		isFollowMap = make(map[int64]bool)
		if isAllFollow {
			for _, moment := range momentList {
				isFollowMap[moment.GetShowRoleId()] = true
			}
		} else {
			isFollowMap, err = m.followRepo.GetIsFollowMap(groupCtx, curRoleId, roleIds)
		}
		return err
	})

	groupErr := errGroup.Wait()

	if groupErr != nil {
		util.LogWithContext(groupCtx).WithField("curRoleId", curRoleId).WithError(groupErr).Error("FillMomentShowListFail")
		return resp, groupErr
	}

	mapper := mapper.NewMomentMapper(m.avatarRepo, m.momentRepo)
	showList, err := mapper.ToMomentShowList(ctx, momentList, roleInfoList, isCollectMap, isLikeMap, isFollowMap)
	return showList, err
}

func (m *MomentService) GetFollowMomentList(ctx context.Context, req *schema.FollowMomentListReq) (schema.MomentListResp, error) {
	curRoleId := req.RoleId
	resp := schema.MomentListResp{List: []schema.MomentShow{}}
	followList, err := m.followRepo.FindByRoleId(ctx, curRoleId)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", curRoleId).Error("GetUserFollowListFail")
		return resp, err
	}
	followRoleIds := lo.Map(followList, func(v model.FollowModel, _ int) int64 { return v.TargetId })
	if len(followRoleIds) == 0 {
		return resp, nil
	}
	baseQuery := func() *gorm.DB {
		query := m.momentRepo.Scope(ctx).
			Where("public_time < ? AND visible = 1", time.Now().UnixMilli()).
			Where("(role_id = ? and show_role_id in (?)) OR role_id in (?)", curRoleId, followRoleIds, followRoleIds)
		return query
	}

	listQuery := baseQuery().Order("ctime desc")
	moments, err := m.momentRepo.FindWithPagination(ctx, listQuery, req.Pagination)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("req", req).Error("GetFollowMomentListFail")
		return resp, err
	}
	showList, err := m.FillMomentShowList(ctx, moments, curRoleId, true)
	if err != nil {
		return resp, err
	}
	total, err := m.momentRepo.CountByQuery(ctx, baseQuery())
	if err != nil {
		return resp, err
	}
	resp = schema.MomentListResp{
		List:  showList,
		Total: total,
	}
	return resp, nil
}

func (m *MomentService) GetMomentDetail(ctx context.Context, req *schema.MomentDetailReq) (*schema.MomentShow, error) {
	curRoleId := req.RoleId
	resp := &schema.MomentShow{}
	momentId := req.MomentId
	var err error

	moment, err := m.momentRepo.FindMomentById(ctx, momentId)
	if err != nil {
		return resp, err
	}
	if moment.IsTemplate {
		myTemplateMomentId, err := m.momentRepo.FindIdByTemplateIdAndRoleId(ctx, moment.ID, curRoleId)
		if err != nil {
			return resp, err
		}
		moment, err = m.momentRepo.FindMomentById(ctx, myTemplateMomentId)
		if err != nil {
			return resp, err
		}
	}
	mm := mapper.NewMomentMapper(m.avatarRepo, m.momentRepo)
	tinyRoleInfo, err := m.userService.GetUserInfoByRoleId(ctx, moment.GetShowRoleId())
	if err != nil {
		return resp, err
	}
	roleInfo := schema.CoreRoleInfo{
		TinyRoleInfo: tinyRoleInfo,
		IsFollow:     false,
	}
	roleInfo.RoleId = moment.GetShowRoleId()
	isLike, err := m.momentLikeRepo.IsLike(ctx, curRoleId, moment.ID)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("req", req).Error("GetMomentDetailIsLikeFail")
		return resp, err
	}
	isCollect, err := m.momentCollectRepo.IsCollect(ctx, curRoleId, moment.ID)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("req", req).Error("GetMomentDetailIsCollectFail")
		return resp, err
	}

	isFollow, err := m.followRepo.IsFollow(ctx, curRoleId, moment.GetShowRoleId())
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("req", req).Error("GetMomentDetailIsFollowFail")
		return resp, err
	}
	roleInfo.IsFollow = isFollow

	tplMoment := model.MomentModel{}
	if moment.TemplateId > 0 {
		tplMoment, err = m.momentRepo.FindMomentById(ctx, moment.TemplateId)
		if err != nil {
			util.LogWithContext(ctx).WithError(err).WithField("moment", moment).Error("FindTplMomentFail")
			return resp, err
		}
	}

	momentShow, err := mm.ToMomentShow(moment, roleInfo, isLike, isCollect, tplMoment)
	if err != nil {
		return resp, err
	}
	resp = &momentShow
	return resp, nil
}

func (m *MomentService) GetMomentCollectList(ctx context.Context, req *schema.MomentCollectListReq) (schema.MomentCollectListResp, error) {
	curRoleId := req.RoleId
	resp := schema.MomentCollectListResp{List: []schema.MomentShow{}}
	categoryId := *req.CategoryId
	momentIds, err := m.momentCollectRepo.ListCollectMomentIds(ctx, req.RoleId, categoryId, req.Pagination)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("req", req).Error("GetMomentCollectListFail")
		return resp, err
	}
	momentList, err := m.momentRepo.FindByIds(ctx, momentIds)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("req", req).Error("GetMomentByIdsFail")
		return resp, err
	}
	mapper := mapper.NewMomentMapper(m.avatarRepo, m.momentRepo)

	roleIds := lo.Map(momentList, func(v model.MomentModel, _ int) int64 { return v.GetShowRoleId() })
	userInfoList, err := m.userService.GetUserInfoList(ctx, roleIds)
	if err != nil {
		return resp, err
	}

	isCollectMap := make(map[int64]bool)
	for _, moment := range momentList {
		isCollectMap[moment.ID] = true
	}

	// get isLikeMap from momentList
	isLikeMap, err := m.momentLikeRepo.GetIsLikeMap(ctx, curRoleId, momentIds)
	if err != nil {
		return resp, err
	}

	// get isFollowMap from momentList
	isFollowMap, err := m.followRepo.GetIsFollowMap(ctx, curRoleId, roleIds)
	if err != nil {
		return resp, err
	}

	showList, err := mapper.ToMomentShowList(ctx, momentList, userInfoList, isCollectMap, isLikeMap, isFollowMap)
	if err != nil {
		return resp, err
	}
	total, err := m.momentCollectRepo.GetMomentCollectListCount(ctx, req.RoleId, categoryId)
	if err != nil {
		return resp, err
	}
	resp = schema.MomentCollectListResp{
		List:  showList,
		Total: total,
	}
	return resp, nil
}

func (m *MomentService) CollectMoment(ctx context.Context, req *schema.MomentCollectReq) (*schema.MomentActionResp, error) {
	resp := &schema.MomentActionResp{}
	moment, err := m.momentRepo.FindMomentById(ctx, req.MomentId)
	resp.LikeCount = moment.GetLikeCount()
	if err != nil {
		return resp, err
	}
	mc, err := m.momentCollectRepo.AddCollect(ctx, req.RoleId, moment)
	if err != nil {
		if api.IsBizErr(err, api.ErrBizMomentCollected) {
			util.LogWithContext(ctx).WithError(err).WithFields(elog.Fields{"roleId": req.RoleId, "momentId": req.MomentId}).Warn("ErrBizMomentCollected")
			resp.IsOk = true
			return resp, nil
		}
		return resp, err
	}
	resp.IsOk = mc.ID > 0
	return resp, nil
}

func (m *MomentService) CancelCollectMoment(ctx context.Context, req *schema.MomentCollectReq) (*schema.MomentActionResp, error) {
	resp := &schema.MomentActionResp{}
	delCnt, err := m.momentCollectRepo.DeleteCollect(ctx, req.RoleId, req.MomentId)
	if err != nil {
		return resp, err
	}
	if delCnt == 0 {
		util.LogWithContext(ctx).WithError(err).WithFields(elog.Fields{"roleId": req.RoleId, "momentId": req.MomentId}).Warn("ErrBizMomentNotCollected")
		resp.IsOk = true
		return resp, nil
	}
	resp.IsOk = delCnt > 0
	return resp, nil
}

func (m *MomentService) LikeMoment(ctx context.Context, req *schema.MomentLikeReq) (*schema.MomentActionResp, error) {
	resp := &schema.MomentActionResp{}
	moment, err := m.momentRepo.FindMomentById(ctx, req.MomentId)
	if err != nil {
		return resp, err
	}
	ml, err := m.momentLikeRepo.AddLike(ctx, req.RoleId, moment)
	if err != nil {
		if api.IsBizErr(err, api.ErrBizMomentLiked) {
			util.LogWithContext(ctx).WithError(err).WithFields(elog.Fields{"momentId": req.MomentId, "roleId": req.RoleId}).Warn("ErrBizMomentLiked")
			resp.IsOk = true
			resp.LikeCount = moment.GetLikeCount()
			return resp, nil
		}
		return resp, err
	}

	likeCount, err := m.momentRepo.IncrementLikeCount(ctx, moment)
	util.LogWithContext(ctx).WithError(err).WithFields(elog.Fields{
		"roleId":       req.RoleId,
		"momentId":     moment.ID,
		"momentRoleId": moment.RoleId,
		"likeCount":    likeCount,
	}).Info("UserLikeMoment")
	if err != nil {
		return resp, err
	}
	resp.IsOk = ml.ID > 0
	resp.LikeCount = likeCount
	return resp, nil
}

func (m *MomentService) CancelLikeMoment(ctx context.Context, req *schema.MomentLikeReq) (*schema.MomentActionResp, error) {
	resp := &schema.MomentActionResp{}
	moment, err := m.momentRepo.FindMomentById(ctx, req.MomentId)
	if err != nil {
		return resp, err
	}
	delCnt, err := m.momentLikeRepo.DeleteLike(ctx, req.RoleId, moment.ID)
	if err != nil {
		return resp, err
	}
	if delCnt == 0 {
		util.LogWithContext(ctx).WithError(err).WithFields(elog.Fields{
			"roleId":       req.RoleId,
			"momentId":     moment.ID,
			"momentRoleId": moment.RoleId,
		}).Info("ErrBizMomentNotLiked")
		resp.LikeCount = moment.GetLikeCount()
		resp.IsOk = true
		return resp, nil
	}

	likeCount, err := m.momentRepo.DecrementLikeCount(ctx, moment)
	util.LogWithContext(ctx).WithError(err).WithFields(elog.Fields{
		"roleId":       req.RoleId,
		"momentId":     moment.ID,
		"momentRoleId": moment.RoleId,
		"likeCount":    likeCount,
	}).Info("UserCancelLikeMoment")
	if err != nil {
		return resp, err
	}
	resp.IsOk = delCnt > 0
	resp.LikeCount = likeCount
	return resp, nil
}

func (s *MomentService) AddNpcMoment(ctx context.Context, momentAdd *schema.NpcMomentAddReq) (*schema.NpcMomentAddResp, error) {
	if momentAdd.ViewCount < momentAdd.BaseLikeCount {
		return nil, api.GetBizErr(api.ErrBizViewCountLessThanLikeCount)
	}
	if !s.momentRepo.IsValidCategoryId(momentAdd.CategoryId) {
		return nil, api.GetBizErr(api.ErrBizInvalidMomentCategory)
	}

	if len(momentAdd.ImgList) > 0 && len(momentAdd.VideoList) > 0 {
		return nil, api.GetBizErr(api.ErrBizImageVideoBothUnSupported)
	}

	if len(momentAdd.ImgList) > 0 {
		if len(momentAdd.ImgList) > config.C.Biz.MomentMaxImageCount {
			return nil, api.GetBizErr(api.ErrBizImageListMaxCountExceeded)
		}
		for _, img := range momentAdd.ImgList {
			if !util.IsValidUrl(img) {
				return nil, api.GetBizErr(api.ErrBizInvalidImageUrl)
			}
		}
	}

	if len(momentAdd.VideoList) > 0 {
		if len(momentAdd.VideoList) > config.C.Biz.MomentMaxVideoCount {
			return nil, api.GetBizErr(api.ErrBizVideoListMaxCountExceeded)
		}
		if len(momentAdd.VideoCoverList) > config.C.Biz.MomentMaxVideoCount {
			return nil, api.GetBizErr(api.ErrBizVideoCoverListMaxCountExceeded)
		}
		for _, video := range momentAdd.VideoList {
			if !util.IsValidUrl(video) {
				return nil, api.GetBizErr(api.ErrBizInvalidVideoUrl)
			}
		}
		for _, videoCover := range momentAdd.VideoCoverList {
			if !util.IsValidUrl(videoCover) {
				return nil, api.GetBizErr(api.ErrBizInvalidVideoCoverUrl)
			}
		}
	}

	if momentAdd.ParentId > 0 {
		if err := s.momentRepo.IsValidParentMomentId(momentAdd.ParentId); err != nil {
			return nil, err
		}
	}

	npcInfo, err := s.npcRepo.FindByRoleId(ctx, momentAdd.RoleId)
	if err != nil {
		return nil, err
	}

	publicTime := time.Now().UnixMilli()
	if momentAdd.PublicTime != 0 {
		publicTime = momentAdd.PublicTime
	}
	var topTime int64 = 0
	if momentAdd.IsTop {
		topTime = time.Now().UnixMilli()
	}

	imgList := util.StrArr2JsonArr(momentAdd.ImgList)
	videoList := util.StrArr2JsonArr(momentAdd.VideoList)
	videoCoverList := util.StrArr2JsonArr(momentAdd.VideoCoverList)

	moment := model.MomentModel{
		RoleId:              npcInfo.RoleId,
		Text:                momentAdd.Text,
		ImgList:             imgList,
		ImgAuditList:        "",
		VideoList:           videoList,
		VideoAuditList:      "",
		VideoCoverList:      videoCoverList,
		VideoCoverAuditList: "",
		CategoryId:          momentAdd.CategoryId,
		BaseLikeCount:       momentAdd.BaseLikeCount,
		ViewCount:           momentAdd.ViewCount,
		TaskId:              momentAdd.TaskId,
		AreaId:              momentAdd.AreaId,
		TopTime:             topTime,
		PublicTime:          publicTime,
		Visible:             1,
		IsTemplate:          momentAdd.IsTemplate,
		ParentId:            momentAdd.ParentId,
	}
	//  如果该动态被设置为当作模板动态，则不可见
	if momentAdd.IsTemplate {
		moment.Visible = 0
	}
	var topicList []model.TopicModel
	topicTextList := moment.GetTopicTextList()
	if len(topicTextList) > 0 {
		// iterate topicTextList and init topic
		for _, topicText := range topicTextList {
			err := s.topicRepo.EnsureTopicCreated(ctx, topicText, moment.AreaId)
			if err != nil {
				return nil, err
			}
		}

		// get topic and id list find by textList
		topicList, err = s.topicRepo.FindByTextList(ctx, topicTextList, moment.AreaId)
		if err != nil {
			return nil, err
		}
		text := s.ReplaceTopicInText(moment.Text, topicList)
		moment.Text = text
	}

	err = conn.GetDB().Create(&moment).Error
	if err != nil {
		return nil, err
	}

	if len(topicTextList) > 0 {
		// save topic and moment relation
		topicIds := lo.Map(topicList, func(v model.TopicModel, _ int) int64 { return v.ID })
		err = s.momentTopicRepo.SaveMomentTopicsRelation(ctx, moment, topicIds)
		if err != nil {
			return nil, err
		}
	}

	resp := &schema.NpcMomentAddResp{
		BaseAddResp: schema.BaseAddResp{
			Id:         moment.ID,
			CreateTime: moment.Ctime,
			UpdateTime: moment.Utime,
		},
	}
	return resp, nil
}

// moment text topic is surrounded by #topic#, if moment find topic text, replace it with <a socialHotId=id>#topic#</a>
func (s *MomentService) ReplaceTopicInText(text string, topicList []model.TopicModel) string {
	mapper := mapper.NewTopicMapper()
	for _, topic := range topicList {
		text = strings.ReplaceAll(text, "#"+topic.Name+"#", mapper.ToTopicLink(topic.ID, topic.Name))
	}
	return text
}

func (s *MomentService) GetMomentListByTopic(ctx context.Context, req *schema.MomentListByTopicReq) (schema.MomentListResp, error) {
	curRoleId := req.RoleId
	resp := schema.MomentListResp{List: []schema.MomentShow{}}
	// first check if topic exist
	topic, err := s.topicRepo.FindByID(ctx, req.TopicId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return resp, api.GetBizErr(api.ErrBizTopicNotExist)
		}
		util.LogWithContext(ctx).WithError(err).WithField("topicId", req.TopicId).Error("FindTopicByIdFail")
		return resp, err
	}
	topicId := topic.ID

	baseQuery := func() *gorm.DB {
		return s.momentTopicRepo.PlayerTopicScope(ctx, curRoleId, topicId)
	}

	listQuery := baseQuery().Order("moment_id desc")
	momentTopicList, err := s.momentTopicRepo.FindWithPagination(ctx, listQuery, req.Pagination)
	if err != nil {
		return resp, err
	}
	momentIds := lo.Map(momentTopicList, func(v model.MomentTopicModel, _ int) int64 { return v.MomentId })
	momentList, err := s.momentRepo.FindByIds(ctx, momentIds)
	if err != nil {
		return resp, err
	}
	showList, err := s.FillMomentShowList(ctx, momentList, curRoleId, false)
	if err != nil {
		return resp, err
	}

	total, _ := s.momentTopicRepo.CountByQuery(ctx, baseQuery())

	resp = schema.MomentListResp{
		List:  showList,
		Total: total,
	}
	return resp, nil
}

func (s *MomentService) CheckNpcMomentExist(ctx context.Context, id int64) (*model.MomentModel, error) {
	moment, err := s.momentRepo.FindByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, api.GetBizErr(api.ErrBizMomentNotFound)
		}
		return nil, err
	}
	return moment, nil
}

func (s *MomentService) DeleteNpcMoment(ctx context.Context, req *schema.NpcMomentDeleteReq) (*schema.NpcMomentDeleteResp, error) {
	resp := &schema.NpcMomentDeleteResp{}
	moment, err := s.CheckNpcMomentExist(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	err = conn.GetDB().Delete(moment).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("momentId", req.Id).Error("DelNpcMomentFail")
		return nil, err
	}
	util.LogWithContext(ctx).WithError(err).WithField("moment", moment).Error("DelNpcMomentOk")

	delByParentCnt, err := s.momentRepo.DeleteByParentId(ctx, moment.ID)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("momentId", req.Id).WithField("delParentCnt", delByParentCnt).Error("DelNpcMomentByParentFail")
		return nil, err
	}
	util.LogWithContext(ctx).WithError(err).WithField("moment", moment).Error("DElNpcMomentByParentOk")

	var delCollectCnt int64

	topicIds, err := s.momentTopicRepo.GetTopicIdsByMomentId(ctx, moment.ID)
	if err != nil {
		return nil, err
	}
	if len(topicIds) > 0 {
		err = s.momentTopicRepo.DeleteByMomentId(ctx, moment.ID)
		if err != nil {
			util.LogWithContext(ctx).WithError(err).WithField("momentId", moment.ID).WithField("delCnt", delCollectCnt).Error("DelMomentTopicFail")
		}
		s.topicService.CheckTopicReferenceAndClean(ctx, topicIds, moment.ID)
	}

	resp.Id = moment.ID
	resp.DeletedAt = int64(moment.DeletedAt)
	resp.DelCollectCount = delCollectCnt

	return resp, nil
}

func (s *MomentService) AddMomentByTemplate(ctx context.Context, req *schema.MomentAddByTemplateReq) (*schema.MomentAddByTemplateResp, error) {
	resp := &schema.MomentAddByTemplateResp{Id: 0, TemplateId: req.TemplateId, FirstCreate: true}

	// 检查templateId合法性
	template, err := s.momentRepo.FindByID(ctx, req.TemplateId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, api.GetBizErr(api.ErrNpcMomentTemplateNotFound)
		}
		util.LogWithContext(ctx).WithError(err).WithField("templateId", req.TemplateId).Error("FindMomentIdByIdFail")
		return nil, err
	}
	if template == nil {
		util.LogWithContext(ctx).WithField("templateId", req.TemplateId).Error("FindMomentIdByIdReturnNil")
		return nil, api.GetBizErr(api.ErrNpcMomentTemplateNotFound)
	}
	if !template.IsNpcCreated() {
		util.LogWithContext(ctx).WithField("templateId", req.TemplateId).Error("TemplateIdIsNotNpcCreated")
		return nil, api.GetBizErr(api.ErrNpcMomentTemplateNotFound)
	}
	npcId := template.RoleId
	_, err = s.npcRepo.FindOne(ctx, model.NpcModel{RoleId: npcId})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, api.GetBizErr(api.ErrNpcMomentTemplateNpcNotFound)
		}
		util.LogWithContext(ctx).WithError(err).WithField("npcId", npcId).Error("FindTemplateNpcFail")
		return nil, err
	}

	curMoment := model.MomentModel{}
	err = s.momentRepo.Scope(ctx).Where(model.MomentModel{TemplateId: req.TemplateId, RoleId: req.RoleId}).First(&curMoment).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			newMomentId, err := s.CopyFromTemplate(ctx, req.RoleId, *template)
			if err != nil {
				return nil, err
			}
			resp.Id = newMomentId
			return resp, nil
		}
		util.LogWithContext(ctx).WithError(err).WithField("templateId", req.TemplateId).Error("FindMomentIdByIdFail")
		return nil, err
	}
	resp.Id = curMoment.ID
	resp.FirstCreate = false
	return resp, nil
}

func (s *MomentService) CopyFromTemplate(ctx context.Context, roleId int64, template model.MomentModel) (int64, error) {
	newMoment, err := s.momentRepo.CreateByTemplate(ctx, roleId, template)
	if err != nil {
		return 0, err
	}
	cnt, err := s.momentTopicRepo.CopyRelationByMomentId(ctx, roleId, template.ID, newMoment.ID)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("cnt", cnt).Error("CopyRelationByMomentIdError")
		return 0, err
	}
	return newMoment.ID, nil
}

func (s *MomentService) CheckImgListValid(imgList []string) error {
	if len(imgList) > 0 {
		if len(imgList) > config.C.Biz.MomentMaxImageCount {
			return api.GetBizErr(api.ErrBizImageListMaxCountExceeded)
		}
		for _, img := range imgList {
			if !util.IsValidUrl(img) {
				return api.GetBizErr(api.ErrBizInvalidImageUrl)
			}
		}
	}
	return nil
}

func (s *MomentService) AddMomentByNpc(ctx context.Context, momentAdd *schema.MomentAddByNpcReq) (schema.MomentAddByNpcResp, error) {
	resp := schema.MomentAddByNpcResp{Id: 0}
	err := s.CheckImgListValid(momentAdd.ImgList)
	if err != nil {
		return resp, err
	}
	if !s.momentRepo.IsValidCategoryId(momentAdd.CategoryId) {
		return resp, api.GetBizErr(api.ErrBizInvalidMomentCategory)
	}

	// check is npcId valid
	_, err = s.npcRepo.FindOne(ctx, model.NpcModel{RoleId: momentAdd.NpcId})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return resp, api.GetBizErr(api.ErrBizNpcNotFound)
		} else {
			return resp, err
		}
	}

	// check is roleId valid
	_, err = s.roleInfoRepo.FindByID(ctx, momentAdd.RoleId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return resp, api.GetBizErr(api.ErrBizRoleInfoNotFound)
		} else {
			return resp, err
		}
	}

	imgList := util.StrArr2JsonArr(momentAdd.ImgList)

	moment := model.MomentModel{
		RoleId:       momentAdd.RoleId,
		ShowRoleId:   momentAdd.NpcId,
		Text:         momentAdd.Text,
		ImgList:      imgList,
		ImgAuditList: "",
		CategoryId:   momentAdd.CategoryId,
		PublicTime:   momentAdd.CreateTime,
		Visible:      1,
	}
	_, err = s.momentRepo.Create(ctx, &moment)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("moment", moment).Error("AddMomentByNpcFail")
		return resp, err
	}
	util.LogWithContext(ctx).WithField("moment", moment).Info("AddMomentByNpcOk")
	resp.Id = moment.ID
	return resp, nil
}
