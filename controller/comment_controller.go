package controller

import (
	"app/api"
	"app/schema"
	"app/service"

	"github.com/gin-gonic/gin"
)

type CommentController struct {
	commentService *service.CommentService
	envSdkService  *service.EnvSdkService
}

func NewCommentController(commentService *service.CommentService, envSdkService *service.EnvSdkService) *CommentController {
	return &CommentController{
		commentService: commentService,
		envSdkService:  envSdkService,
	}
}

// GetCommentList godoc
// @Summary 获取评论列表
// @Tags comment
// @Accept  json
// @Produce  json
// @Param request query schema.CommentListReq true "query"
// @success 200 {object} api.RespBody{data=schema.CommentListResp}
// @Router /api/comment/list [get]
func (c *CommentController) GetCommentList(ctx *gin.Context) {
	req := &schema.CommentListReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := c.commentService.GetCommentList(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// LikeComment godoc
// @Summary 点赞评论
// @Tags comment
// @Accept  json
// @Produce  json
// @Param request body schema.CommentLikeReq true "body"
// @success 200 {object} api.RespBody{data=schema.CommentLikeResp}
// @Router /api/comment/like [post]
func (c *CommentController) LikeComment(ctx *gin.Context) {
	req := &schema.CommentLikeReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := c.commentService.LikeComment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// CancelLikeComment godoc
// @Summary 取消点赞评论
// @Tags comment
// @Accept  json
// @Produce  json
// @Param request body schema.CommentLikeReq true "body"
// @success 200 {object} api.RespBody{data=schema.CommentLikeResp}
// @Router /api/comment/cancel_like [post]
func (c *CommentController) CancelLikeComment(ctx *gin.Context) {
	req := &schema.CommentLikeReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := c.commentService.CancelLikeComment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// AddComment godoc
// @Summary 添加评论
// @Tags comment
// @Accept  json
// @Produce  json
// @Param request body schema.CommentAddReq true "body"
// @success 200 {object} api.RespBody{data=schema.CommentAddResp}
// @Router /api/comment/add [post]
func (c *CommentController) AddComment(ctx *gin.Context) {
	req := &schema.CommentAddReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	isPass := c.envSdkService.ReviewWords(ctx, req.Text)
	if !isPass {
		api.HandleResponse(ctx, nil, api.GetBizErr(api.ErrContentSensitive))
		return
	}
	resp, err := c.commentService.AddComment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}

// DeleteComment godoc
// @Summary 删除评论
// @Tags comment
// @Accept  json
// @Produce  json
// @Param request body schema.CommentDeleteReq true "body"
// @success 200 {object} api.RespBody{data=schema.CommentDeleteResp}
// @Router /api/comment/delete [post]
func (c *CommentController) DeleteComment(ctx *gin.Context) {
	req := &schema.CommentDeleteReq{}
	if api.BindAndCheck(ctx, req) {
		return
	}
	resp, err := c.commentService.DeleteComment(ctx, req)
	api.HandleResponse(ctx, resp, err)
}
