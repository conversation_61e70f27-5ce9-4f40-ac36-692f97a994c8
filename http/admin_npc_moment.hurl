#npc moment add
POST {{baseUrl}}/admin/npc/moment/add
{
  "areaId": 10,
  "baseLikeCount": 10,
  "imgList": [],
  "videoList": ["https://storage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"],
  "videoCoverList": ["https://storage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg"],
  "isTop": true,
  "roleId": 100008,
  "taskId": 0,
  "text": "add moment text",
  "viewCount": 100
}

# npc moment show
GET {{baseUrl}}/admin/npc/moment/show
[QueryStringParams]
id:2

# npc moment list filter by roleId
GET {{baseUrl}}/admin/npc/moment/list
[QueryStringParams]
page:1
pageSize:10
# roleId:100001


# npc moment list
GET {{baseUrl}}/admin/npc/moment/list
[QueryStringParams]
page:1
pageSize:2
areaId:2222

# npc moment list filter by roleId
GET {{baseUrl}}/admin/npc/moment/list
[QueryStringParams]
roleId:100001
page:1
pageSize:2
hasTask:false

# npc moment update
POST {{baseUrl}}/admin/npc/moment/update
{
  "id": 47,
  "areaId": 0,
  "imgList": [],
  "isTop": true,
  "roleId": 100001,
  "taskId": 0,
  "text": "测试下话题修改2"
}

# npc moment partial update
POST {{baseUrl}}/admin/npc/moment/update
{
  "id": 2,
  "text": "动态正好3",
  "viewCount": 10
}

# npc moment delete
POST {{baseUrl}}/admin/npc/moment/delete
{
  "id": 45
}


# npc comment add
POST {{baseUrl}}/admin/npc/comment/add
{
  "momentId": 1,
  "roleId": 20240612,
  "text": "战争贩子安插了小丑。我还是轻敌了",
  "baseLikeCount": 10
}

# npc comment delete
POST {{baseUrl}}/admin/npc/comment/delete
{
  "id": 9
}


# npc comment update
POST {{baseUrl}}/admin/npc/comment/update
{
  "id": 3,
  "baseLikeCount": 11
}
