package service

import (
	"app/repo"
	"app/schema"
	"context"
)

type FollowService struct {
	followRepo *repo.FollowRepo
}

func NewFollowService(followRepo *repo.FollowRepo) *FollowService {
	return &FollowService{followRepo: followRepo}
}

func (s *FollowService) GetFollowList(ctx context.Context, req *schema.FollowListReq) (schema.FollowListResp, error) {
	resp := schema.FollowListResp{List: []schema.FollowInfo{}}
	return resp, nil
}

func (s *FollowService) GetFanList(ctx context.Context, req *schema.FanListReq) (schema.FanListResp, error) {
	resp := schema.FanListResp{List: []schema.FollowInfo{}}
	return resp, nil
}

func (s *FollowService) Follow(ctx context.Context, req *schema.FollowReq) (schema.FollowResp, error) {
	resp := schema.FollowResp{}
	relation, err := s.followRepo.Follow(ctx, req.RoleId, req.TargetId)
	if err != nil {
		return resp, err
	}
	resp.IsOk = relation.ID > 0
	return resp, nil
}

func (s *FollowService) Unfollow(ctx context.Context, req *schema.FollowReq) (schema.FollowResp, error) {
	resp := schema.FollowResp{}
	relation, err := s.followRepo.Unfollow(ctx, req.RoleId, req.TargetId)
	if err != nil {
		return resp, err
	}
	resp.IsOk = relation.ID > 0
	return resp, nil
}
