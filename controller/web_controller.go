package controller

import (
	"app/assets"
	"app/config"
	"net/http"

	"github.com/gin-gonic/gin"
)

type WebController struct {
}

func NewWebController() *WebController {
	return &WebController{}
}

// TuiteUpload godoc
// @Summary Tuite上传页面
// @Tags web
// @Router /web/tuite_upload [get]
func (wc *WebController) TuiteUpload(ctx *gin.Context) {
	// 检查是否启用了上传UI
	if !config.C.Biz.EnableUploadUI {
		ctx.AbortWithStatus(http.StatusNotFound)
		return
	}

	// 读取HTML文件
	htmlContent, err := assets.StaticAssets.ReadFile("static/tuite_upload.html")
	if err != nil {
		ctx.AbortWithStatus(http.StatusInternalServerError)
		return
	}

	// 返回HTML页面
	ctx.Header("Content-Type", "text/html; charset=utf-8")
	ctx.String(http.StatusOK, string(htmlContent))
}
