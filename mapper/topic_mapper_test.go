package mapper

import (
	"reflect"
	"testing"
)

func TestRemoveSocialHotTag(t *testing.T) {
	topicLink := "<a socialHot=1>#topic#</a>"
	expect := "#topic#"
	result := NewTopicMapper().RemoveSocialHotTag(topicLink)
	if result != expect {
		t.<PERSON><PERSON>("expect %s, but got %s", expect, result)
	}
}

func TestTopicMapper_ExtractTopicIds(t *testing.T) {
	mapper := NewTopicMapper()

	tests := []struct {
		name     string
		text     string
		expected []int
	}{
		{
			name:     "single topic",
			text:     `<a socialHot=123>#golang#</a>`,
			expected: []int{123},
		},
		{
			name:     "multiple topics",
			text:     `<a socialHot=123>#golang#</a> some text <a socialHot=456>#programming#</a>`,
			expected: []int{123, 456},
		},
		{
			name:     "no topics",
			text:     `this is plain text with no topics`,
			expected: []int{},
		},
		{
			name:     "empty text",
			text:     "",
			expected: []int{},
		},
		{
			name:     "mixed content",
			text:     `Hello <a socialHot=789>#test#</a> world! Check out <a socialHot=101>#news#</a> today.`,
			expected: []int{789, 101},
		},
		{
			name:     "invalid format",
			text:     `<a socialHot=abc>#invalid#</a>`,
			expected: []int{},
		},
		{
			name:     "with spaces",
			text:     `<a  socialHot=999>#spaced#</a>`,
			expected: []int{999},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := mapper.ExtractTopicIds(tt.text)
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("ExtractTopicIds() = %v, expected %v", result, tt.expected)
			}
		})
	}
}
