package repo

import (
	"app/model"
	"app/schema"
	"app/util"
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type TopicRepo struct {
	*BaseRepo[model.TopicModel]
}

func NewTopicRepo(db *gorm.DB) *TopicRepo {
	return &TopicRepo{
		BaseRepo: NewBaseRepo[model.TopicModel](db),
	}
}

func (r *TopicRepo) GetTopicTrendList(ctx context.Context) ([]model.TopicModel, error) {
	var trendList []model.TopicModel
	return trendList, nil
}

func (r *TopicRepo) FindByTextList(ctx context.Context, textList []string, areaId int) ([]model.TopicModel, error) {
	topicList, err := r.Find(ctx, "name IN (?) AND area_id = ?", textList, areaId)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("textList", textList).Error("FindByTextListError")
		return nil, err
	}
	return topicList, nil
}

// increment hot by topicIds with hotValue
func (r *TopicRepo) IncrementHotByTopicIds(ctx context.Context, topicIds []int64, hotValue int64) error {
	if len(topicIds) == 0 {
		return nil
	}
	err := r.DB.Model(&model.TopicModel{}).Where("id IN (?)", topicIds).Update("hot", gorm.Expr("hot + ?", hotValue)).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("topicIds", topicIds).WithField("hotValue", hotValue).Error("IncrementHotByTopicIdsError")
		return err
	}
	return err
}

// ensure topic text and areaId is exist
func (r *TopicRepo) EnsureTopicCreated(ctx context.Context, text string, areaId int) error {
	err := r.DB.Clauses(clause.OnConflict{
		DoUpdates: clause.Assignments(map[string]any{"deleted_at": 0, "area_id": areaId, "name": text}),
	}).Create(&model.TopicModel{
		Name:   text,
		AreaId: areaId,
	}).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("text", text).Error("EnsureTopicTextAndAreaIdError")
		return err
	}
	return err
}

// count all topic
func (r *TopicRepo) CountAll(ctx context.Context) (int64, error) {
	var count int64
	err := r.DB.Model(&model.TopicModel{}).Count(&count).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("CountAllError")
		return 0, err
	}
	return count, nil
}

// get max topic id
func (r *TopicRepo) GetMaxTopicId(ctx context.Context) (int64, error) {
	var maxId int64
	err := r.Scope(ctx).Select("MAX(id)").Scan(&maxId).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("GetMaxTopicIdError")
		return 0, err
	}
	return maxId, nil
}

func (r *TopicRepo) GetTopicMapByIds(ctx context.Context, ids []int64) (map[int64]schema.TopicItem, error) {
	var topics []schema.TopicItem
	err := r.Scope(ctx).Where("id in ?", ids).Select("id,name,area_id").Scan(&topics).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("ids", ids).Error("GetByTopicIdsError")
		return nil, err
	}
	resp := make(map[int64]schema.TopicItem)
	for _, topic := range topics {
		resp[topic.Id] = topic
	}
	return resp, nil
}
