package service

import (
	"app/config"
	"app/util"
	"context"
	"crypto/md5"
	"encoding/hex"
	"sort"
	"strings"
)

func CheckAuthToken(ctx context.Context, secretKey string, params map[string]string, inputToken string) bool {
	logger := util.LogWithContext(ctx).WithField("params", params)
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	var values []string
	for _, k := range keys {
		values = append(values, params[k])
	}
	signStr := strings.Join(values, "")

	expectedToken := calculateToken(signStr, secretKey)

	if expectedToken != inputToken {
		if config.C.TestCfg.SkipTokenCheck {
			logger.WithField("signStr", signStr).WithField("keys", keys).WithField("expectToken", expectedToken).WithField("inputToken", inputToken).Warn("TokenMismatchSkip")
			return true
		} else {
			logger.With<PERSON>ield("signStr", signStr).With<PERSON>ield("keys", keys).With<PERSON>ield("expectToken", expectedToken).With<PERSON>ield("inputToken", inputToken).Warn("TokenMismatch")
			return false
		}
	}

	return true
}

func calculateToken(signStr string, secretKey string) string {
	h := md5.New()
	h.Write([]byte(signStr + secretKey))
	return hex.EncodeToString(h.Sum(nil))
}
