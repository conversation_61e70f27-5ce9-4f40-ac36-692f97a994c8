package service

import (
	"app/api"
	"app/mapper"
	"app/model"
	"app/repo"
	"app/schema"
	"context"
	"strconv"

	"golang.org/x/sync/errgroup"
)

type UserService struct {
	roleInfoRepo *repo.RoleInfoRepo
	npcRepo      *repo.NpcRepo
	avatarRepo   *repo.AvatarRepo
	followRepo   *repo.FollowRepo
}

func NewUserService(roleInfoRepo *repo.RoleInfoRepo, npcRepo *repo.NpcRepo, avatarRepo *repo.AvatarRepo, followRepo *repo.FollowRepo) *UserService {
	return &UserService{
		roleInfoRepo: roleInfoRepo,
		npcRepo:      npcRepo,
		avatarRepo:   avatarRepo,
		followRepo:   followRepo,
	}
}

func (u *UserService) GetUserProfile(ctx context.Context, req *schema.UserProfileReq) (schema.UserProfileResp, error) {
	resp := schema.UserProfileResp{}
	roleInfo, err := u.roleInfoRepo.FindByRoleId(ctx, req.ViewRoleId)
	if err != nil {
		return resp, err
	}
	mapper := mapper.NewRoleInfoMapper(u.avatarRepo)
	var baseFollowingCount int64
	var baseFollowerCount int64
	var npc model.NpcModel
	if model.IsValidNpcId(req.ViewRoleId) {
		npc, err = u.npcRepo.FindByRoleId(ctx, req.ViewRoleId)
		if err != nil {
			return resp, err
		}
		baseFollowingCount = npc.BaseFollowingCount
		baseFollowerCount = npc.BaseFollowerCount
		resp.IsNpc = true
		resp.IsCertified = npc.IsVip
	}
	coreInfo := mapper.ToTinyRoleInfo(ctx, roleInfo, npc)
	resp.RoleId = coreInfo.RoleId
	resp.RoleName = coreInfo.RoleName
	resp.AvatarId = coreInfo.AvatarId
	resp.AvatarUrl = coreInfo.AvatarUrl
	resp.AccountId = coreInfo.AccountId

	// 获取关注数
	followCount, err := u.followRepo.GetFollowCount(ctx, resp.RoleId)
	if err != nil {
		return resp, err
	}

	// 获取粉丝数
	fanCount, err := u.followRepo.GetFanCount(ctx, resp.RoleId)
	if err != nil {
		return resp, err
	}
	resp.FollowingCount = followCount + baseFollowingCount
	resp.FollowerCount = fanCount + baseFollowerCount

	return resp, nil
}

func (u *UserService) GetUserInfoList(ctx context.Context, roleIds []int64) ([]schema.TinyRoleInfo, error) {
	var resp []schema.TinyRoleInfo
	if len(roleIds) == 0 {
		return resp, nil
	}

	errGroup, groupCtx := errgroup.WithContext(ctx)
	var roleInfos []model.RoleInfoModel
	var npcInfoMap map[int64]model.NpcModel

	errGroup.Go(func() error {
		var err error
		roleInfos, err = u.roleInfoRepo.GetRoleInfoList(groupCtx, roleIds, []string{"RoleId", "RoleName", "Avatar"})
		return err
	})

	errGroup.Go(func() error {
		var err error
		var npcIds []int64
		for _, roleInfo := range roleInfos {
			if model.IsValidNpcId(roleInfo.RoleId) {
				npcIds = append(npcIds, roleInfo.RoleId)
			}
		}
		npcInfoMap, err = u.npcRepo.GetNpcListMap(groupCtx, npcIds)
		return err
	})

	err := errGroup.Wait()
	if err != nil {
		return resp, err
	}

	for _, roleInfo := range roleInfos {
		npc, isOk := npcInfoMap[roleInfo.RoleId]
		avatarId, _ := strconv.Atoi(roleInfo.Avatar)
		avatarUrl := u.avatarRepo.GetAvatarUrl(ctx, avatarId)
		coreInfo := schema.TinyRoleInfo{
			RoleId:      roleInfo.RoleId,
			RoleName:    roleInfo.RoleName,
			AvatarId:    avatarId,
			AvatarUrl:   avatarUrl,
			IsNpc:       false,
			IsCertified: false,
			AccountId:   "",
		}
		if isOk {
			coreInfo.IsNpc = true
			coreInfo.IsCertified = npc.IsVip
			coreInfo.AccountId = npc.AccountId
		}
		resp = append(resp, coreInfo)
	}
	return resp, nil
}

// get user info by roleId
func (u *UserService) GetUserInfoByRoleId(ctx context.Context, roleId int64) (schema.TinyRoleInfo, error) {
	var roleInfo schema.TinyRoleInfo
	userInfoList, err := u.GetUserInfoList(ctx, []int64{roleId})
	if err != nil {
		return roleInfo, err
	}
	if len(userInfoList) == 0 {
		return roleInfo, api.GetBizErr(api.ErrRoleInfoNotFound)
	}
	return userInfoList[0], nil
}
