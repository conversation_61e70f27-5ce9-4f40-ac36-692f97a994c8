package service

import (
	"app/config"
	"app/schema"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
)

type EncodeAuthInfo struct {
	IdType string `json:"idType"`
	Time   int64  `json:"time"`
	IdVal  int64  `json:"idVal"`
}

const (
	AuthIdRoleType = "role"
	// jwt HS256 header
	JWT_SHA256_HEADER = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9"
)

type EncodeData = [3]interface{}

type AuthService struct {
	conf *config.AuthConfig
}

// 使用 HMAC-SHA256 计算签名
func hmacSha256(data []byte, key []byte) string {
	mac := hmac.New(sha256.New, key)
	mac.Write(data)
	return base64.RawURLEncoding.EncodeToString(mac.Sum(nil))
}

func NewAuthService(conf *config.AuthConfig) *AuthService {
	return &AuthService{conf: conf}
}

func (s *AuthService) DecodeSkey(ctx context.Context, skey string, roleId int64) (*EncodeAuthInfo, error) {
	if s.conf.EnableCheatSkey && skey == s.conf.CheatSkey {
		elog.Warn("CheatSkey enable, return fake info")
		return &EncodeAuthInfo{IdType: "role", IdVal: roleId}, nil
	}
	segements := strings.Split(skey, ".")
	if segements == nil || len(segements) != 2 {
		elog.Error("decode skey error %s wrong segments", segements)
		return nil, errors.New("decode skey wrong segments")
	}
	payloadB64 := segements[0]
	payloadJson, err := base64.RawURLEncoding.DecodeString(payloadB64)
	if err != nil {
		elog.WithContext(ctx).WithField("payloadB64", payloadB64).Error("decode skey payload %s by base64", payloadB64)
		return nil, errors.New("decode skey base64 error: " + err.Error())
	}
	payload := EncodeData{}
	err = json.Unmarshal(payloadJson, &payload)
	if err != nil {
		return nil, errors.New("decode skey json error: " + err.Error())
	}

	signature := segements[1]
	signatureInput := JWT_SHA256_HEADER + "." + payloadB64
	expectedSignature := hmacSha256([]byte(signatureInput), []byte(s.conf.SkeySecret))
	if signature != expectedSignature {
		elog.WithFields(elog.Fields{"actual": signature, "expected": expectedSignature, "payloadB64": payload}).Warn("ValidateSkeyFail")
		return nil, errors.New("skey signature mismatch")
	}

	time, ok := payload[0].(float64)
	if !ok {
		return nil, fmt.Errorf("decode skey parse time fail: %v", payload[0])
	}

	idType, ok := payload[1].(string)
	if !ok {
		return nil, fmt.Errorf("decode skey parse idType fail: %v", payload[1])
	}

	idValStr, ok := payload[2].(string)
	if !ok {
		return nil, fmt.Errorf("decode skey parse idVal fail: %v, %T", payload[2], payload[2])
	}
	idVal, err := strconv.ParseInt(idValStr, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("decode skey parse idVal fail: %v, %T", payload[2], payload[2])
	}

	authInfo := EncodeAuthInfo{
		Time:   int64(time),
		IdType: idType,
		IdVal:  idVal,
	}
	return &authInfo, nil
}

func (s *AuthService) ValidateLogin(ctx context.Context, authParams *schema.BaseAuthReq) (*EncodeAuthInfo, error) {
	roleId := authParams.RoleId
	authInfo, err := s.DecodeSkey(ctx, authParams.Skey, roleId)
	if err != nil {
		return authInfo, err
	}
	idType := authInfo.IdType
	switch idType {
	case AuthIdRoleType:
		if authInfo.IdVal != roleId {
			elog.WithContext(ctx).WithField("idInSkey", authInfo.IdVal).WithField("roleIdInForm", authParams.RoleId).Error("idVal not match")
			return authInfo, errors.New("idVal not match")
		}
	default:
		return authInfo, errors.New("idType not support")
	}
	return authInfo, nil
}
