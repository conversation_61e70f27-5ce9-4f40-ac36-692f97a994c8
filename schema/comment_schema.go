package schema

type CommentShow struct {
	// 评论id
	Id int64 `json:"id"`
	// 评论内容
	Text string `json:"text"`
	// 评论点赞数
	LikeCount int64 `json:"likeCount"`
	// 评论所属的动态id
	MomentId int64 `json:"momentId"`
	// 评论所属的角色id
	RoleId int64 `json:"roleId"`
	// 评论所属的角色信息
	RoleInfo TinyRoleInfo `json:"roleInfo"`
	// 是否点赞
	IsLike bool `json:"isLike"`
	// 创建时间
	CreateTime int64 `json:"createTime"`
}

type CommentListResp struct {
	List  []CommentShow `json:"list"`
	Total int64         `json:"total"`
}

type CommentListReq struct {
	BaseAuthReq
	MomentId int64 `json:"momentId" form:"momentId" binding:"required"`
	Pagination
}

type CommentActionReq struct {
	BaseAuthReq
	// 评论id
	CommentId int64 `json:"commentId"`
}

type CommentLikeReq = CommentActionReq

type CommentLikeResp struct {
	BaseActionResp
	LikeCount int64 `json:"likeCount"`
}

type CommentAddReq struct {
	BaseAuthReq
	MomentId int64  `json:"momentId"`
	Text     string `json:"text" binding:"required,max=50"`
}

type CommentAddResp struct {
	BaseAddResp
}

type CommentDeleteReq = CommentActionReq

type CommentDeleteResp struct {
	BaseDeleteResp
}
