package controller

import (
	"app/api"
	"app/config"
	"app/middleware"
	"app/service"
	"app/util"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	filepicker_tools "ccc-gitlab.leihuo.netease.com/pkgo/filepicker-tools"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
)

type AdminCommonController struct {
	dataExportService *service.DataExportService
	dataImportService *service.DataImportService
}

func NewAdminCommonController(dataExportService *service.DataExportService, dataImportService *service.DataImportService) *AdminCommonController {
	return &AdminCommonController{
		dataExportService: dataExportService,
		dataImportService: dataImportService,
	}
}

// GetFPToken godoc
// @Summary 获取FP Token
// @Tags admin_common
// @Accept  json
// @Produce  json
// @Param request query schema.GetFpTokenReq true "query"
// @Success 200 {object} api.RespBody{data=schema.GetFpTokenResp}
// @Router /admin/common/get_fp_token [get]
func (ac *AdminCommonController) GetFPToken(ctx *gin.Context) {
	client := service.GetFPClient()
	fpPolicy := config.C.Filepicker.Policy
	loginInfo := middleware.GetAdminLoginInfo(ctx)
	fpUid := loginInfo.CorpMail
	if fpUid == "" {
		fpUid = "l50-social-media-admin"
	}
	extraPolicy := &filepicker_tools.ExtraPolicy{}
	if fpPolicy != nil {
		extraPolicy = &filepicker_tools.ExtraPolicy{
			FsizeLimit: &fpPolicy.FsizeLimit,
			MimeLimit:  &fpPolicy.MimeLimit,
			UID:        &fpUid,
		}
	}
	resp, err := client.GetToken(extraPolicy)
	api.HandleResponse(ctx, resp, err)
}

// ExportToExcel godoc
// @Summary 导出Excel
// @Tags admin_common
// @Router /admin/common/export_to_excel [get]
func (ac *AdminCommonController) ExportToExcel(ctx *gin.Context) {
	// 创建一个新的 Excel 文件
	excelFile, err := ac.dataExportService.ExportToExcel(ctx)
	if err != nil {
		ctx.AbortWithStatus(http.StatusInternalServerError)
		return
	}

	// 设置响应头并写入文件
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Disposition", "attachment; filename=Tuite.xlsx")
	ctx.Header("Content-Transfer-Encoding", "binary")

	if err := excelFile.Write(ctx.Writer); err != nil {
		ctx.AbortWithStatus(http.StatusInternalServerError)
		return
	}
}

// Import godoc
// @Summary 导入配置
// @Tags admin_common
// @Router /admin/common/import [post]
func (ac *AdminCommonController) Import(ctx *gin.Context) {
	// 从请求中获取上传的文件
	file, header, err := ctx.Request.FormFile("file")
	if err != nil {
		api.HandleResponse(ctx, nil, err)
		return
	}
	defer file.Close()

	// 备份原始文件
	if err := ac.backupUploadedFile(ctx, file, header.Filename); err != nil {
		util.LogWithContext(ctx).WithError(err).Warn("Failed to backup uploaded file, continuing with import")
		// 不因为备份失败而终止导入过程，只记录警告
	}

	// 重新定位文件指针到开头，因为备份过程可能已经读取了文件
	if seeker, ok := file.(io.Seeker); ok {
		seeker.Seek(0, io.SeekStart)
	}

	f, err := excelize.OpenReader(file)
	if err != nil {
		api.HandleResponse(ctx, nil, err)
		return
	}

	// 创建一个新的 Excel 文件
	result, err := ac.dataImportService.ImportFromExcel(ctx, f)
	util.LogWithContext(ctx).WithField("result", result).WithError(err).Info("ImportFromExcel completed")

	if err != nil || result == nil {
		util.LogWithContext(ctx).WithField("result_is_nil", result == nil).WithError(err).Error("ImportFromExcel failed")
		api.HandleResponse(ctx, nil, err)
		return
	}

	util.LogWithContext(ctx).WithField("result", result).Info("ImportResultDetail")

	if result.ErrorCount > 0 {
		api.AdminJsonReturn(ctx, nil, api.ErrAdminImportFailed, errors.New(strings.Join(result.Errors, ",   ")))
		return
	}
	api.AdminJsonReturn(ctx, result, api.OK, nil)
}

// backupUploadedFile 备份上传的文件
func (ac *AdminCommonController) backupUploadedFile(ctx *gin.Context, file io.Reader, originalFilename string) error {
	// 创建备份目录
	backupDir := "imported_files"
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		util.LogWithContext(ctx).WithError(err).Error("Failed to create backup directory")
		return err
	}

	// 生成带时间戳的文件名，保留原文件扩展名
	timestamp := time.Now().Format("20060102150405")
	ext := filepath.Ext(originalFilename)
	baseFilename := strings.TrimSuffix(originalFilename, ext)
	backupFilename := fmt.Sprintf("%s_%s%s", baseFilename, timestamp, ext)
	backupPath := filepath.Join(backupDir, backupFilename)

	// 创建备份文件
	backupFile, err := os.Create(backupPath)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("Failed to create backup file")
		return err
	}
	defer backupFile.Close()

	// 复制文件内容
	_, err = io.Copy(backupFile, file)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("Failed to copy file content")
		return err
	}

	util.LogWithContext(ctx).WithField("backupPath", backupPath).WithField("originalFilename", originalFilename).Info("Successfully backed up uploaded file")
	return nil
}
