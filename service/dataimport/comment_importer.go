package dataimport

import (
	"app/model"
	"app/util"
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// CommentImporter handles Comment sheet import
type CommentImporter struct {
	importCtx *ImportContext
}

// NewCommentImporter creates a new Comment importer
func NewCommentImporter(importCtx *ImportContext) *CommentImporter {
	return &CommentImporter{
		importCtx: importCtx,
	}
}

// GetSheetName returns the sheet name for Comment import
func (c *CommentImporter) GetSheetName() string {
	return "Comment"
}

// ImportSheet imports comment data from the Comment sheet
func (c *CommentImporter) ImportSheet(ctx context.Context, file *excelize.File, tx *gorm.DB, result *ImportResult) error {
	stepStartTime := time.Now()
	util.LogWithContext(ctx).Info("startProcessingCommentSheet")
	errScope := "导入评论失败："

	commentRows, err := file.GetRows(c.GetSheetName())
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("getCommentRowsError")
		return err
	}
	util.LogWithContext(ctx).WithField("total_rows", len(commentRows)).Info("commentSheetTotalRows")

	if len(commentRows) < 3 {
		result.Errors = append(result.Errors, errScope+"Comment sheet has less than 3 rows, cannot find header row.")
		return nil
	}

	headerDefinitionRow := commentRows[2]
	headerMap := make(map[string]int)
	for idx, headerName := range headerDefinitionRow {
		headerMap[strings.TrimSpace(headerName)] = idx
	}

	// Define expected headers based on user's image and assumptions for other fields
	expectedHeaders := map[string]string{
		"id":     "Id",
		"roleId": "Publisher",
		"text":   "Txt",
	}

	for i, row := range commentRows {
		if i < ImportStartSheetRow-1 {
			continue
		}

		// Check if reached data end marker
		if len(row) > 0 && strings.Contains(row[0], SheetDataEndMarker) {
			util.LogWithContext(ctx).WithField("row", i+1).Info("commentSheetReachedEndMarker")
			break
		}

		if err := c.processCommentRow(ctx, tx, result, row, headerMap, expectedHeaders, i+1, errScope); err != nil {
			continue
		}
	}

	result.Comments.DurationMs = time.Since(stepStartTime).Milliseconds()
	util.LogWithContext(ctx).WithField("imported_count", result.Comments.Count).WithField("duration_ms", result.Comments.DurationMs).Info("commentSheetProcessingCompleted")
	return nil
}

// processCommentRow processes a single Comment row
func (c *CommentImporter) processCommentRow(ctx context.Context, tx *gorm.DB, result *ImportResult, row []string, headerMap map[string]int, expectedHeaders map[string]string, rowNum int, errScope string) error {
	idStr := row[headerMap[expectedHeaders["id"]]]
	util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", idStr).Debug("startProcessingCommentRow")

	id, err := CheckId(idStr)
	if err != nil {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", idStr).WithError(err).Error("commentIdParsingFailed")
		result.Errors = append(result.Errors, errScope+"Invalid Comment ID at row "+strconv.Itoa(rowNum))
		return err
	}
	if id == -1 {
		util.LogWithContext(ctx).WithField("row", rowNum).Debug("commentRowIsEmpty")
		return nil
	}
	if id == 0 {
		util.LogWithContext(ctx).WithField("row", rowNum).Debug("commentRowSkippedWithTilde")
		return nil
	}

	roleIdStr := row[headerMap[expectedHeaders["roleId"]]]
	roleId, err := strconv.ParseInt(roleIdStr, 10, 64)
	if err != nil {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("roleId", roleIdStr).WithError(err).Error("commentRoleIdParsingFailed")
		result.Errors = append(result.Errors, errScope+"Invalid Comment roleId at row "+strconv.Itoa(rowNum))
		return err
	}

	text := row[headerMap[expectedHeaders["text"]]]
	if text == "" {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).Error("commentTextCannotBeEmpty")
		result.Errors = append(result.Errors, errScope+"Comment text cannot be empty at row "+strconv.Itoa(rowNum))
		return errors.New("comment text cannot be empty")
	}
	util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithField("roleId", roleId).WithField("text_length", len(text)).Debug("preparingToSaveCommentData")

	return c.createOrUpdateComment(ctx, tx, result, id, roleId, text, rowNum, errScope)
}

// createOrUpdateComment creates or updates a Comment record
func (c *CommentImporter) createOrUpdateComment(ctx context.Context, tx *gorm.DB, result *ImportResult, id, roleId int64, text string, rowNum int, errScope string) error {
	var comment model.CommentModel
	if err := tx.Where("id = ?", id).First(&comment).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Comment ID will be linked to Moment later
			comment = model.CommentModel{
				BaseCoreVersionModel: model.BaseCoreVersionModel{
					BaseCoreModel: model.BaseCoreModel{
						BaseModel: model.BaseModel{
							ID: id,
						},
						DeletedAt: 0,
					},
				},
				RoleId: roleId,
				Text:   text,
			}
			if err := tx.Save(&comment).Error; err != nil {
				util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithError(err).Error("createCommentFailed")
				result.Errors = append(result.Errors, errScope+"Failed to create Comment at row "+strconv.Itoa(rowNum)+": "+err.Error())
				return err
			}
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithField("roleId", roleId).Info("commentCreatedSuccessfully")
		} else {
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithError(err).Error("commentDatabaseQueryError")
			result.Errors = append(result.Errors, errScope+"Database error for Comment at row "+strconv.Itoa(rowNum)+": "+err.Error())
			return err
		}
	} else {
		comment.RoleId = roleId
		comment.Text = text
		if err := tx.Save(&comment).Error; err != nil {
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithError(err).Error("updateCommentFailed")
			result.Errors = append(result.Errors, errScope+"Failed to update Comment at row "+strconv.Itoa(rowNum)+": "+err.Error())
			return err
		}
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithField("roleId", roleId).Info("commentUpdatedSuccessfully")
	}

	result.Comments.Count++
	return nil
}
