package api

import (
	"app/util"
	stdErrors "errors"
	"fmt"
	"strings"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/errors"
	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

type RespBody struct {
	Code int64       `json:"code"`
	Msg  string      `json:"msg,omitempty"`
	Data interface{} `json:"data"`
}

type OkRespBody struct {
	Code int64       `json:"code"`
	Data interface{} `json:"data"`
}

func NewResponse() *RespBody {
	return &RespBody{
		Code: 0,
		Msg:  "success",
	}
}

type fieldError struct {
	err validator.FieldError
}

func (q fieldError) String() string {
	var sb strings.Builder
	// OPTIMIZE : 实际这里filed还是返回的structField，目前json字段名按规范是struct名字的首字母小写, 先这样处理
	sb.WriteString("validation failed on field '" + util.FirstLetterToLower(q.err.Field()) + "'")
	sb.WriteString(", condition: " + q.err.ActualTag())

	// Print condition parameters, e.g. oneof=red blue -> { red blue }
	if q.err.Param() != "" {
		sb.WriteString(" { " + q.err.Param() + " }")
	}

	if q.err.Value() != nil && q.err.Value() != "" {
		sb.WriteString(fmt.Sprintf(", actual: %v", q.err.Value()))
	}

	return sb.String()
}

func JsonReturn(c *gin.Context, d interface{}, err *errors.Err) {
	res := OkRespBody{Code: 0}
	res.Data = d
	c.JSON(200, res)
}

func JsonReturnError(c *gin.Context, err *errors.Err) {
	res := NewResponse()
	res.Code = err.ErrCode
	res.Msg = err.ErrMsg
	c.JSON(200, res)
}

// AdminJsonReturn 向调用方按通用标准响应格式返回结果-显示更多错误信息
func AdminJsonReturn(ctx *gin.Context, data any, code Code, err error) {
	msg := GetBizErr(code)
	if err != nil {
		msg.ErrMsg += "【" + err.Error() + "】"
	}
	ctx.JSON(200, gin.H{
		"code": code,
		"msg":  msg.ErrMsg,
		"data": data, // 这里data是map类型，需要转换为json
	})
}

func HandleResponse(c *gin.Context, data interface{}, err error) {
	if err == nil {
		JsonReturn(c, data, nil)
		return
	}
	var bizErr *errors.Err
	if stdErrors.Is(err, gorm.ErrRecordNotFound) {
		JsonReturnError(c, errors.New(errors.DataNotFound))
	} else if stdErrors.As(err, &bizErr) {
		JsonReturnError(c, err.(*errors.Err))
	} else {
		util.LogWithContext(c).Error("unknown error: %s", err.Error())
		JsonReturnError(c, errors.New(errors.UnknownError))
	}
}

func BindAndCheck(ctx *gin.Context, data interface{}) bool {
	var err error
	if ctx.Request.Method == "GET" {
		err = ctx.ShouldBindQuery(data)
	} else {
		err = ctx.ShouldBindBodyWith(data, binding.JSON)
	}
	if err != nil {
		fieldErrors, ok := err.(validator.ValidationErrors)
		if ok {
			for _, fieldErr := range fieldErrors {
				HandleResponse(ctx, nil, &errors.Err{ErrCode: errors.ParameterInvalid, ErrMsg: fieldError{fieldErr}.String()})
				return true
			}
		} else {
			util.LogWithContext(ctx).Error("BindAndCheck error: %s", err.Error())
			HandleResponse(ctx, nil, errors.New(errors.ParameterInvalid))
			return true
		}

	}
	return false
}
