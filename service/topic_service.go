package service

import (
	"app/repo"
	"app/schema"
	"app/util"
	"context"
	"time"

	"github.com/samber/lo"
	"golang.org/x/exp/rand"
	"gorm.io/gorm"
)

type TopicService struct {
	topicRepo       *repo.TopicRepo
	topicIdCache    *repo.TopicIdCache
	momentTopicRepo *repo.MomentTopicRepo
}

func NewTopicService(topicRepo *repo.TopicRepo, topicIdCache *repo.TopicIdCache, momentTopicRepo *repo.MomentTopicRepo) *TopicService {
	return &TopicService{topicRepo: topicRepo, topicIdCache: topicIdCache, momentTopicRepo: momentTopicRepo}
}

func (s *TopicService) GetTrendsList(ctx context.Context, req *schema.TrendListReq) (schema.TrendListResp, error) {
	resp := schema.TrendListResp{List: []schema.TrendInfo{}}
	curRoleId := req.RoleId

	baseQuery := func() *gorm.DB {
		return s.momentTopicRepo.PlayerTopicHotScope(ctx, curRoleId, req.AreaId)
	}

	var topicHotList []schema.TopicHot
	offset := (req.Pagination.Page - 1) * req.Pagination.PageSize
	limit := req.Pagination.PageSize
	err := s.momentTopicRepo.PlayerTopicHotScope(ctx, curRoleId, req.AreaId).
		Order("hot desc").
		Offset(offset).Limit(limit).
		Scan(&topicHotList).Error

	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("req", req).Error("GetTrendsListError")
		return resp, err
	}

	topicIds := lo.Map(topicHotList, func(v schema.TopicHot, _ int) int64 {
		return v.TopicId
	})
	topicMap, err := s.topicRepo.GetTopicMapByIds(ctx, topicIds)
	if err != nil {
		return resp, err
	}
	showList := lo.Map(topicHotList, func(v schema.TopicHot, _ int) schema.TrendInfo {
		topic := topicMap[v.TopicId]
		return schema.TrendInfo{
			Id:     v.TopicId,
			Hot:    v.Hot,
			Name:   topic.Name,
			AreaId: topic.AreaId,
		}
	})

	resp.List = showList

	count, err := s.topicRepo.CountByQuery(ctx, baseQuery())
	if err != nil {
		return resp, err
	}

	resp.Total = count

	return resp, nil
}

func (s *TopicService) GetRandomTrendsList(ctx context.Context, req *schema.TrendListReq) (schema.TrendRandomListResp, error) {
	resp := schema.TrendRandomListResp{List: []schema.TrendInfo{}}
	size := int64(req.PageSize)
	curRoleId := req.RoleId
	maxAllowSearchId, err := s.momentTopicRepo.GetPlayerMaxSearchId(ctx, curRoleId)
	if err != nil {
		return resp, err
	}
	// 设置随机数种子
	rand.Seed(uint64(time.Now().UnixMilli()))

	randomMidId := rand.Int63n(maxAllowSearchId)
	searchSize := 5000
	poolSize := min(size*10, 200)
	minSearchId := max(0, randomMidId-int64(searchSize))
	maxSearchId := randomMidId + int64(searchSize)

	topicIdsPool, err := s.momentTopicRepo.GetTopicIdsByRange(ctx, curRoleId, minSearchId, maxSearchId, poolSize)
	topicIds := lo.Samples(topicIdsPool, int(size))
	if err != nil {
		return resp, err
	}

	var topicHotList []schema.TopicHot
	offset := (req.Pagination.Page - 1) * req.Pagination.PageSize
	limit := req.Pagination.PageSize
	err = s.momentTopicRepo.PlayerTopicHotScope(ctx, curRoleId, req.AreaId).
		Where("t.topic_id in (?)", topicIds).
		Order("hot desc").
		Offset(offset).Limit(limit).
		Scan(&topicHotList).Error

	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("req", req).Error("GetTrendsListError")
		return resp, err
	}

	topicMap, err := s.topicRepo.GetTopicMapByIds(ctx, topicIds)
	if err != nil {
		return resp, err
	}
	showList := lo.Map(topicHotList, func(v schema.TopicHot, _ int) schema.TrendInfo {
		topic := topicMap[v.TopicId]
		return schema.TrendInfo{
			Id:     v.TopicId,
			Hot:    v.Hot,
			Name:   topic.Name,
			AreaId: topic.AreaId,
		}
	})

	resp.List = showList
	return resp, nil
}

// check topic reference and auto clean topic when reference is 0
func (s *TopicService) CheckTopicReferenceAndClean(ctx context.Context, topicIds []int64, momentId int64) error {
	for _, topicId := range topicIds {
		isContainMoment, err := s.momentTopicRepo.IsTopicContainMoment(ctx, topicId)
		if err != nil {
			util.LogWithContext(ctx).WithError(err).WithField("topicId", topicId).Error("CheckTopicContainMomentError")
			continue
		}
		if !isContainMoment {
			_, err = s.topicRepo.DeleteByID(ctx, topicId)
			if err != nil {
				util.LogWithContext(ctx).WithError(err).WithField("topicId", topicId).Error("DelMomentTopicFail")
			} else {
				util.LogWithContext(ctx).WithField("topicId", topicId).WithField("momentId", momentId).Error("DelTopicOkCauseNotContainMoment")
			}
		}
	}
	return nil
}

// topic hot is sum of momentIds viewCount relation to this topic
func (s *TopicService) GetTopicHot(ctx context.Context, topicId int64) (int64, error) {
	var hot int64
	sql := `select COALESCE(sum(view_count), 0) as hot from  l50_moment where id in (SELECT moment_id from l50_moment_topic where topic_id = ? and deleted_at = 0) and deleted_at = 0;`
	err := s.topicRepo.DB.Raw(sql, topicId).Scan(&hot).Error
	if err != nil {
		return 0, err
	}
	return hot, nil
}
