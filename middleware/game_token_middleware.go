package middleware

import (
	"app/api"
	"app/config"
	"app/util"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// CheckGMIP 游戏服务端调用鉴权-中间件
func CheckGMIP(c *gin.Context) {
	ip := c.ClientIP()
	if config.C.TestCfg.SkipIpCheck {
		c.Next()
		return
	}
	if !lo.Contains(config.C.Biz.IPWhitList, ip) {
		util.LogWithContext(c).WithField("ip", ip).WithField("ipWhiteList", config.C.Biz.IPWhitList).Warn("CheckGmIpIpAuthFailed")
		api.HandleResponse(c, nil, api.GetBizErr(api.ErrServerToken))
		c.Abort()
		return
	}
	c.Next()
}
