// Package dataimport provides functionality for importing data from Excel files
// into the application's database. It handles the import process for various
// entity types such as NPCs, topics, comments, and moments.
package dataimport

import (
	"app/repo"
	"app/util"
	"context"
	"time"

	"github.com/xuri/excelize/v2"
)

// Service is the main data import service that coordinates all sheet importers
type Service struct {
	importCtx *ImportContext
	importers []SheetImporter
}

// NewService creates a new data import service
func NewService(npcRepo *repo.NpcRepo, momentRepo *repo.MomentRepo, topicRepo *repo.TopicRepo, commentRepo *repo.CommentRepo, momentTopicRepo *repo.MomentTopicRepo, roleInfoRepo *repo.RoleInfoRepo) *Service {
	importCtx := &ImportContext{
		NpcRepo:         npcRepo,
		MomentRepo:      momentRepo,
		TopicRepo:       topicRepo,
		CommentRepo:     commentRepo,
		MomentTopicRepo: momentTopicRepo,
		RoleInfoRepo:    roleInfoRepo,
	}

	// Initialize all importers in the correct order
	importers := []SheetImporter{
		NewNpcImporter(importCtx),
		NewTypeImporter(importCtx),
		NewTopicImporter(importCtx),
		NewCommentImporter(importCtx),
		NewTuiteImporter(importCtx),
	}

	return &Service{
		importCtx: importCtx,
		importers: importers,
	}
}

// ImportFromExcel imports data from an Excel file using all configured importers
func (s *Service) ImportFromExcel(ctx context.Context, file *excelize.File) (*ImportResult, error) {
	startTime := time.Now()
	result := &ImportResult{
		Errors: make([]string, 0),
		Warns:  make([]string, 0),
	}

	util.LogWithContext(ctx).Info("startImportingExcelData")

	// Start transaction
	tx := s.importCtx.NpcRepo.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Clean up existing NPC-related data before importing
	npcCleaner := NewNpcCleaner()
	if err := npcCleaner.CleanupNpcData(ctx, tx, result, "导入前清理NPC数据失败："); err != nil {
		util.LogWithContext(ctx).WithError(err).Error("npcDataCleanupFailed")
		tx.Rollback()
		return nil, err
	}

	// Execute all importers in sequence
	for _, importer := range s.importers {
		sheetName := importer.GetSheetName()
		util.LogWithContext(ctx).WithField("sheet_name", sheetName).Info("startProcessingSheet")

		if err := importer.ImportSheet(ctx, file, tx, result); err != nil {
			util.LogWithContext(ctx).WithField("sheet_name", sheetName).WithError(err).Error("sheetImportFailed")
			tx.Rollback()
			return nil, err
		}

		util.LogWithContext(ctx).WithField("sheet_name", sheetName).Info("sheetProcessingCompleted")
	}

	// Commit transaction
	util.LogWithContext(ctx).Info("preparingToCommitTransaction")
	if err := tx.Commit().Error; err != nil {
		util.LogWithContext(ctx).WithError(err).Error("importCommitTransactionError")
		return nil, err
	}
	util.LogWithContext(ctx).Info("transactionCommittedSuccessfully")

	// Set error and warn counts
	result.ErrorCount = len(result.Errors)
	result.WarnCount = len(result.Warns)

	// Output import summary information
	util.LogWithContext(ctx).
		WithField("cleanup_total", result.Cleanup.TotalCount).
		WithField("cleanup_moments", result.Cleanup.MomentsDeleted).
		WithField("cleanup_topics", result.Cleanup.TopicsDeleted).
		WithField("cleanup_comments", result.Cleanup.CommentsDeleted).
		WithField("cleanup_npcs", result.Cleanup.NpcsDeleted).
		WithField("cleanup_duration_ms", result.Cleanup.DurationMs).
		WithField("npcs_imported", result.Npcs.Count).
		WithField("npcs_duration_ms", result.Npcs.DurationMs).
		WithField("categories_imported", result.Categories.Count).
		WithField("categories_duration_ms", result.Categories.DurationMs).
		WithField("topics_imported", result.Topics.Count).
		WithField("topics_duration_ms", result.Topics.DurationMs).
		WithField("comments_imported", result.Comments.Count).
		WithField("comments_duration_ms", result.Comments.DurationMs).
		WithField("moments_imported", result.Moments.Count).
		WithField("moments_duration_ms", result.Moments.DurationMs).
		WithField("moment_topics_imported", result.MomentTopics.Count).
		WithField("moment_topics_duration_ms", result.MomentTopics.DurationMs).
		WithField("total_duration_ms", result.TotalDurationMs).
		WithField("error_count", result.ErrorCount).
		WithField("warn_count", result.WarnCount).
		Info("excelImportCompletedSummary")

	// Calculate total duration
	result.TotalDurationMs = time.Since(startTime).Milliseconds()

	return result, nil
}

// GetImporters returns all configured importers (useful for testing or inspection)
func (s *Service) GetImporters() []SheetImporter {
	return s.importers
}

// GetImportContext returns the import context (useful for testing)
func (s *Service) GetImportContext() *ImportContext {
	return s.importCtx
}
