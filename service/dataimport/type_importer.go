package dataimport

import (
	"app/model"
	"app/util"
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// TypeImporter handles Type sheet import
type TypeImporter struct {
	importCtx *ImportContext
}

// NewTypeImporter creates a new Type importer
func NewTypeImporter(importCtx *ImportContext) *TypeImporter {
	return &TypeImporter{
		importCtx: importCtx,
	}
}

// GetSheetName returns the sheet name for Type import
func (t *TypeImporter) GetSheetName() string {
	return "Type"
}

// ImportSheet imports category data from the Type sheet
func (t *TypeImporter) ImportSheet(ctx context.Context, file *excelize.File, tx *gorm.DB, result *ImportResult) error {
	stepStartTime := time.Now()
	util.LogWithContext(ctx).Info("startProcessingTypeSheet")

	categoryRows, err := file.GetRows(t.GetSheetName())
	errScope := "导入分类失败："
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("getCategoryRowsError")
		return err
	}
	util.LogWithContext(ctx).WithField("total_rows", len(categoryRows)).Info("typeSheetTotalRows")

	if len(categoryRows) < 3 {
		result.Errors = append(result.Errors, errScope+"Type sheet has less than 3 rows, cannot find header row.")
		return nil
	}

	headerDefinitionRow := categoryRows[2] // Actual header values are in the 3rd row (index 2)
	headerMap := make(map[string]int)
	for idx, headerName := range headerDefinitionRow {
		headerMap[strings.TrimSpace(headerName)] = idx
	}

	// Define expected headers based on user's image and assumptions for other fields
	expectedHeaders := map[string]string{
		"id":   "Id",
		"name": "TypeName",
	}

	for i, row := range categoryRows {
		if i < ImportStartSheetRow-1 {
			continue
		}

		// Check if reached data end marker
		if len(row) > 0 && strings.Contains(row[0], SheetDataEndMarker) {
			util.LogWithContext(ctx).WithField("row", i+1).Info("typeSheetReachedEndMarker")
			break
		}

		if err := t.processTypeRow(ctx, tx, result, row, headerMap, expectedHeaders, i+1, errScope); err != nil {
			continue
		}
	}

	result.Categories.DurationMs = time.Since(stepStartTime).Milliseconds()
	util.LogWithContext(ctx).WithField("imported_count", result.Categories.Count).WithField("duration_ms", result.Categories.DurationMs).Info("typeSheetProcessingCompleted")
	return nil
}

// processTypeRow processes a single Type row
func (t *TypeImporter) processTypeRow(ctx context.Context, tx *gorm.DB, result *ImportResult, row []string, headerMap map[string]int, expectedHeaders map[string]string, rowNum int, errScope string) error {
	idStr := row[headerMap[expectedHeaders["id"]]]
	util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", idStr).Debug("startProcessingTypeRow")

	id, err := CheckId(idStr)
	if err != nil {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", idStr).WithError(err).Error("typeIdParsingFailed")
		result.Errors = append(result.Errors, errScope+"Invalid Type ID at row "+strconv.Itoa(rowNum))
		return err
	}
	if id == -1 {
		util.LogWithContext(ctx).WithField("row", rowNum).Debug("typeRowIsEmpty")
		return nil
	}
	if id == 0 {
		util.LogWithContext(ctx).WithField("row", rowNum).Debug("typeRowSkippedWithTilde")
		return nil
	}

	name := row[headerMap[expectedHeaders["name"]]]
	if name == "" {
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).Error("typeNameCannotBeEmpty")
		result.Errors = append(result.Errors, errScope+"Type name cannot be empty at row "+strconv.Itoa(rowNum))
		return errors.New("type name cannot be empty")
	}
	util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithField("name", name).Debug("preparingToSaveTypeData")

	return t.createOrUpdateType(ctx, tx, result, id, name, rowNum, errScope)
}

// createOrUpdateType creates or updates a Type record
func (t *TypeImporter) createOrUpdateType(ctx context.Context, tx *gorm.DB, result *ImportResult, id int64, name string, rowNum int, errScope string) error {
	category := model.MomentCategoryModel{}
	if err := tx.Where("id = ?", id).First(&category).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			category = model.MomentCategoryModel{
				BaseCoreModel: model.BaseCoreModel{
					BaseModel: model.BaseModel{
						ID: id,
					},
				},
				Name: name,
			}
			if err := tx.Create(&category).Error; err != nil {
				util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithError(err).Error("createTypeFailed")
				result.Errors = append(result.Errors, errScope+"Failed to create Type at row "+strconv.Itoa(rowNum)+": "+err.Error())
				return err
			}
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithField("name", name).Info("typeCreatedSuccessfully")
		} else {
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithError(err).Error("typeDatabaseQueryError")
			result.Errors = append(result.Errors, errScope+"Database error for Type at row "+strconv.Itoa(rowNum)+": "+err.Error())
			return err
		}
	} else {
		category.Name = name
		if err := tx.Save(&category).Error; err != nil {
			util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithError(err).Error("updateTypeFailed")
			result.Errors = append(result.Errors, errScope+"Failed to update Type at row "+strconv.Itoa(rowNum)+": "+err.Error())
			return err
		}
		util.LogWithContext(ctx).WithField("row", rowNum).WithField("id", id).WithField("name", name).Info("typeUpdatedSuccessfully")
	}

	result.Categories.Count++
	return nil
}
