package router

import (
	"app/controller"
)

type AppController struct {
	// game controlers
	MomentController  *controller.MomentController
	CommentController *controller.CommentController
	UserController    *controller.UserController
	FollowController  *controller.FollowController
	TopicController   *controller.TrendController

	// admin controlers
	AdminNpcController        *controller.AdminNpcController
	AdminAssetController      *controller.AssertController
	AdminCommonController     *controller.AdminCommonController
	AdminNpcMomentController  *controller.AdminNpcMomentController
	AdminNpcCommentController *controller.AdminNpcCommentController
	AdminOperatorController   *controller.AdminOperatorController

	// web controlers
	WebController *controller.WebController
}

// NewAppControllers 创建app route
func NewAppController(momentController *controller.Moment<PERSON><PERSON>roller, commentController *controller.CommentController, userController *controller.UserC<PERSON>roller, followController *controller.Follow<PERSON><PERSON>roller, topicController *controller.Trend<PERSON><PERSON>roller, adminNpcController *controller.AdminNpc<PERSON>ontroller, adminAssetController *controller.AssertController, adminCommonControler *controller.AdminCommonController, adminNpcMomentController *controller.AdminNpcMomentController, adminNpcCommentController *controller.AdminNpcCommentController, AdminOperatorController *controller.AdminOperatorController, webController *controller.WebController) *AppController {
	appControllers := &AppController{
		// game controlers
		MomentController:  momentController,
		CommentController: commentController,
		UserController:    userController,
		FollowController:  followController,
		TopicController:   topicController,

		// admin controlers
		AdminNpcController:        adminNpcController,
		AdminAssetController:      adminAssetController,
		AdminCommonController:     adminCommonControler,
		AdminNpcMomentController:  adminNpcMomentController,
		AdminNpcCommentController: adminNpcCommentController,
		AdminOperatorController:   AdminOperatorController,

		// web controlers
		WebController: webController,
	}

	return appControllers
}
