package mapper

import (
	"app/model"
	"app/repo"
	"app/schema"
	"context"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
)

type CommentMapper struct {
	avatarRepo *repo.AvatarRepo
}

func NewCommentMapper(avatarRepo *repo.AvatarRepo) *CommentMapper {
	return &CommentMapper{
		avatarRepo: avatarRepo,
	}
}

func (m *CommentMapper) ToCommentShow(c model.CommentModel, moment model.MomentModel, roleInfo schema.TinyRoleInfo, isLike bool) (schema.CommentShow, error) {
	resp := schema.CommentShow{
		Id:         c.ID,
		Text:       c.Text,
		LikeCount:  c.GetLikeCount(),
		MomentId:   c.MomentId,
		RoleId:     c.RoleId,
		RoleInfo:   roleInfo,
		IsLike:     isLike,
		CreateTime: c.GetCommentTime(moment.Ctime),
	}
	return resp, nil
}

func (m *CommentMapper) ToCommentShowList(ctx context.Context, commentList []model.CommentModel, moment model.MomentModel, roleInfoMap map[int64]model.RoleInfoModel, isLikeMap map[int64]bool, npcMap map[int64]model.NpcModel) ([]schema.CommentShow, error) {
	respList := []schema.CommentShow{}
	roleInfoMapper := NewRoleInfoMapper(m.avatarRepo)
	for _, v := range commentList {
		tinyRoleInfo := schema.TinyRoleInfo{RoleId: v.RoleId, IsNpc: model.IsValidNpcId(v.RoleId)}
		roleInfo, isOk := roleInfoMap[v.RoleId]
		if isOk {
			npc, isNpc := npcMap[v.RoleId]
			if isNpc {
			} else {
				npc = model.NpcModel{}
			}
			roleInfo := roleInfoMapper.ToTinyRoleInfo(ctx, roleInfo, npc)

			tinyRoleInfo.RoleName = roleInfo.RoleName
			tinyRoleInfo.AvatarId = roleInfo.AvatarId
			tinyRoleInfo.AvatarUrl = roleInfo.AvatarUrl
			tinyRoleInfo.AccountId = roleInfo.AccountId
			tinyRoleInfo.IsCertified = npc.IsVip

		} else {
			elog.WithField("roleId", v.RoleId).Error("moment_mapper.ToCommentShowList RoleIdNotFound ")
		}
		_, isLike := isLikeMap[v.ID]
		comment, err := m.ToCommentShow(v, moment, tinyRoleInfo, isLike)
		if err != nil {
			return respList, err
		}
		respList = append(respList, comment)
	}
	return respList, nil
}
