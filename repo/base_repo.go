package repo

import (
	"app/schema"
	"app/util"
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type BaseRepo[T any] struct {
	DB *gorm.DB
}

func NewBaseRepo[T any](db *gorm.DB) *BaseRepo[T] {
	return &BaseRepo[T]{DB: db}
}

func (r *BaseRepo[T]) Create(ctx context.Context, entity *T) (*T, error) {
	if err := r.DB.Create(entity).Error; err != nil {
		return nil, err
	}
	return entity, nil
}

func (r *BaseRepo[T]) FindByID(ctx context.Context, id int64) (*T, error) {
	var entity T
	if err := r.DB.WithContext(ctx).First(&entity, id).Error; err != nil {
		return nil, err
	}
	return &entity, nil
}

func (r *BaseRepo[T]) FindOne(ctx context.Context, where interface{}, args ...interface{}) (T, error) {
	var entity T
	err := r.DB.WithContext(ctx).Where(where, args...).First(&entity).Error
	return entity, err
}

func (r *BaseRepo[T]) DeleteByID(ctx context.Context, id int64) (*T, error) {
	entity, err := r.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	err = r.DB.Delete(entity).Error
	return entity, err
}

func (r *BaseRepo[T]) DeleteByQuery(ctx context.Context, query interface{}, args ...interface{}) (int64, error) {
	var entnty T
	delRet := r.Scope(ctx).Where(query, args...).Delete(&entnty)
	if err := delRet.Error; err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("query", query).WithField("args", args).Error("DeleteByQueryError")
		return 0, err
	}
	return delRet.RowsAffected, nil
}

func (r *BaseRepo[T]) Count(ctx context.Context) (int64, error) {
	var entnty T
	var count int64
	err := r.DB.Model(&entnty).Count(&count).Error
	return count, err
}

func (r *BaseRepo[T]) CountByQuery(ctx context.Context, query *gorm.DB) (int64, error) {
	var count int64
	err := query.Count(&count).Error
	return count, err
}

// query with pagination
func (b *BaseRepo[T]) FindWithPagination(ctx context.Context, query *gorm.DB, pagination schema.Pagination) ([]T, error) {
	var records []T
	err := query.Offset((pagination.Page - 1) * pagination.PageSize).Limit(pagination.PageSize).Find(&records).Error
	if err != nil {
		return records, err
	}
	return records, nil
}

func (r *BaseRepo[T]) Scope(ctx context.Context) *gorm.DB {
	var entity T
	return r.DB.WithContext(ctx).Model(&entity)
}

func (r *BaseRepo[T]) FindOneByQuery(ctx context.Context, query *gorm.DB) (T, error) {
	var entity T
	err := query.First(&entity).Error
	return entity, err
}

func (r *BaseRepo[T]) Find(ctx context.Context, query interface{}, args ...interface{}) ([]T, error) {
	var records []T
	err := r.DB.WithContext(ctx).Where(query, args...).Find(&records).Error
	return records, err
}

// find by ids, keep ids order
func (r *BaseRepo[T]) FindByIds(ctx context.Context, ids []int64) ([]T, error) {
	var records []T
	if len(ids) == 0 {
		return records, nil
	}
	orderIds := lo.Map(ids, func(v int64, _ int) string {
		return strconv.Itoa(int(v))
	})
	orderClause := fmt.Sprintf("FIELD(id, %s)", strings.Join(orderIds, ","))
	query := r.DB.Where("id in (?)", ids).Order(orderClause)
	err := query.Find(&records).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("ids", ids).Error("FindByIdsFail")
		return records, err
	}
	return records, err
}

// just like find by ids, but not keep ids order
func (r *BaseRepo[T]) FindByIdsNoOrder(ctx context.Context, ids []int64) ([]T, error) {
	var records []T
	if len(ids) == 0 {
		return records, nil
	}
	query := r.DB.Where("id in (?)", ids)
	err := query.Find(&records).Error
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("ids", ids).Error("FindByIdsNoOrderFail")
		return records, err
	}
	return records, err
}

// update by id
func (r *BaseRepo[T]) UpdateById(ctx context.Context, id int64, data map[string]any) (schema.UpdateResult, error) {
	resp := schema.UpdateResult{
		RowsAffected: 0,
		RowsChanged:  0,
	}
	ret := r.Scope(ctx).WithContext(ctx).Where("id = ?", id).Updates(data)
	resp.RowsAffected = ret.RowsAffected
	resp.RowsChanged = ret.RowsAffected
	if err := ret.Error; err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("id", id).Error("UpdateByIdFail")
		return resp, err
	}
	return resp, nil
}
