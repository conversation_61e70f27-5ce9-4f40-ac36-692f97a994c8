#npc add
POST {{baseUrl}}/admin/npc/add
{
  "roleId": 100008,
  "name": "新启骑士",
  "avatarId": ********,
  "signature": "新启骑士前面",
  "baseFollowingCount": 16,
  "baseFollowerCount": 15,
  "accountId": "xqqs",
  "isVip": true
}

# npc add only with required params
POST {{baseUrl}}/admin/npc/add
{
  "roleId": 100008,
  "name": "新启骑士",
  "avatarId": 1001,
  "signature": "新启骑士前面",
  "accountId": "xqsq",
  "isVip": false
}


# npc list
GET {{baseUrl}}/admin/npc/list
[QueryStringParams]
page:1
pageSize:2
roleId:********

# npc update
POST {{baseUrl}}/admin/npc/update
{
  "roleId": 100008,
  "name": "新启骑士",
  "avatarId": 1003,
  "signature": "新启骑士后面",
  "AccountId": "xqss",
  "isVip": true
}

# npc show
GET {{baseUrl}}/admin/npc/show
[QueryStringParams]
roleId:100005

#np delete
POST {{baseUrl}}/admin/npc/delete
{
  "roleId": 100008
}
