package middleware

import (
	"app/api"
	"app/config"
	"app/schema"
	"app/service"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"github.com/gin-gonic/gin"
)

// UserLoginCheck 用户信息校验中间件，
// 此中间件依赖请求上下文中的role_id，一般需注册在LoginCheck中间件之后，
// 获取用户信息发生错误或用户信息不存在将直接向调用方响应对应标准错误，
// 获取用户信息成功时将在请求上下文中挂载用户信息 user。
func UserLoginCheck(conf *config.AuthConfig) func(c *gin.Context) {
	authService := service.NewAuthService(conf)

	return func(ctx *gin.Context) {
		authInfo := &schema.BaseAuthReq{}

		// 获取用户信息
		if api.BindAndCheck(ctx, authInfo) {
			elog.WithContext(ctx).Error("check basic auth params error, %v", authInfo)
			ctx.Abort()
			return
		}
		info, err := authService.ValidateLogin(ctx, authInfo)
		if err != nil {
			elog.WithContext(ctx).WithField("roleid", authInfo.RoleId).Error("userlogin valid fail by: error %s", err)
			api.HandleResponse(ctx, nil, api.GetBizErr(api.ErrAuthInvalidSkey))
			ctx.Abort()
			return
		}
		elog.Debug("user login parse skey ok, %+v", info)

		// 继续handlers chain
		ctx.Next()
	}
}
