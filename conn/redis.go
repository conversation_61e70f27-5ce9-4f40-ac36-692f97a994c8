package conn

import (
	"app/config"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/redis"
	"context"
	"fmt"
	"sync"
	"time"
)

var poolMap = make(map[string]*redis.Pools)

func GetRedis(kl ...string) *redis.Pools {
	key := Redis1
	if len(kl) > 0 {
		key = kl[0]
	}

	pool, exists := poolMap[key]
	if !exists {
		fmt.Println(fmt.Errorf("redis %s not connected", key))
		return nil
	}
	return pool
}

const Redis1 = "rdb1"

var redis1Once = sync.Once{}

func ConnectRedis1(conf *config.RedisConfig) {
	redis1Once.Do(func() {
		pool := &redis.Pools{
			Addr:        conf.Addr,
			Password:    conf.Password, // no password set
			DB:          conf.DB,       // use default DB
			PoolSize:    conf.MaxActive,
			IdleTimeout: 300 * time.Second,
			IsTLS:       conf.IsTLS,
		}
		pool.Start()
		err := pool.Ping(context.Background())
		if nil != err {
			fmt.Printf("redis %s conn error ! %v\n", Redis1, err)
		} else {
			fmt.Printf("redis %s conn success !\n", Redis1)
		}
		poolMap[Redis1] = pool
	})
}
