package mapper

import (
	"app/model"
	"app/repo"
	"app/schema"
)

type NpcMapper struct {
	avatarRepo *repo.AvatarRepo
}

func NewNpcMapper(avatarRepo *repo.AvatarRepo) *NpcMapper {
	return &NpcMapper{
		avatarRepo: avatarRepo,
	}
}

func (m *NpcMapper) ToNpcShow(npc model.NpcModel, avatarsMap map[int]string) schema.NpcShow {
	avatarUrl := m.avatarRepo.GetDefaultAvatarUrl()
	avatarIdUrl, ok := avatarsMap[npc.AvatarId]
	if ok {
		avatarUrl = avatarIdUrl
	}
	return schema.NpcShow{
		NpcAddReq: schema.NpcAddReq{
			RoleId:             npc.RoleId,
			AccountId:          npc.AccountId,
			Name:               npc.Name,
			AvatarId:           npc.AvatarId,
			BaseFollowingCount: npc.BaseFollowingCount,
			BaseFollowerCount:  npc.BaseFollowerCount,
			Signature:          npc.Signature,
			IsVip:              npc.IsVip,
		},
		NpcAddResp: schema.NpcAddResp{
			BaseAddResp: schema.BaseAddResp{
				Id:         npc.ID,
				CreateTime: npc.Ctime,
				UpdateTime: npc.Utime,
			},
		},
		AvatarUrl:   avatarUrl,
		IsPlayerNpc: npc.IsPlayerNpc > 0,
	}
}

func (m *NpcMapper) ToNpcShowList(npcList []model.NpcModel, avatarsMap map[int]string) []schema.NpcShow {
	npcShowList := []schema.NpcShow{}
	for _, npc := range npcList {
		npcShowList = append(npcShowList, m.ToNpcShow(npc, avatarsMap))
	}
	return npcShowList
}
