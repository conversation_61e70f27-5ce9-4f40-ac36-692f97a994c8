//go:build windows

package cmd

import (
	"app/config"
	"app/router"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"fmt"
	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(server)
}

var server = &cobra.Command{
	Use:   "server",
	Short: "Start HTTP Server",
	Long:  "Start HTTP Server",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()
		serverStart()
	},
}

func serverStart() {
	httpServer := router.InitRouter(router.RegisterRoute)
	fmt.Println("[windows]start "+config.C.App.AppName+" listen ", config.C.App.HttpListen)
	err := httpServer.Run(config.C.App.HttpListen)
	if err != nil {
		elog.Error(err.Error())
	}
}
