package service

import (
	"app/config"
	"app/conn"
	"app/util"
	"context"

	envsdk_tools "ccc-gitlab.leihuo.netease.com/pkgo/envsdk-tools"
)

type EnvSdkService struct {
	client *envsdk_tools.EnvsdkClient
}

func NewEnvSdkService(cfg config.EnvSdkCfg) *EnvSdkService {
	return &EnvSdkService{
		client: envsdk_tools.NewEnvsdkClient(&envsdk_tools.InitOptions{
			Host:       cfg.Host,
			Timeout:    cfg.Timeout,
			HttpClient: conn.GetClient(),
		}),
	}
}

func (s *EnvSdkService) GetEnvsdkClient() *envsdk_tools.EnvsdkClient {
	return s.client
}

func (s *EnvSdkService) ReviewWordsInChannelWithLevel(ctx context.Context, content string, level int, channel string) bool {
	ret, err := s.client.ReviewWords(ctx, content, channel, level)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("content", content).Error("EnvsdkReviewWordsError")
		return true
	}
	return ret.IsPass
}

func (s *EnvSdkService) ReviewWords(ctx context.Context, content string) bool {
	ret, err := s.client.ReviewWords(ctx, content, "world", 0)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("content", content).Error("EnvsdkReviewWordsError")
		return true
	}
	return ret.IsPass
}
