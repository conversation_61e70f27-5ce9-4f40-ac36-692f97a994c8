package service

import (
	"app/repo"
	"app/schema"
	"context"
)

type AssetsService struct {
	avatarRepo *repo.AvatarRepo
}

func NewAssetsService(avatarRepo *repo.AvatarRepo) *AssetsService {
	return &AssetsService{
		avatarRepo: avatarRepo,
	}
}

func (as *AssetsService) ListAvatars(ctx context.Context) ([]schema.AvatarItem, error) {
	return as.avatarRepo.ListAvatars(ctx)
}

// getAvatarUrl 获取头像url
func (as *AssetsService) GetAvatarUrl(ctx context.Context, avatarId int) string {
	return as.avatarRepo.GetAvatarUrl(ctx, avatarId)
}

// getAvatarUrlMap 获取头像url map[string][string]
func (as *AssetsService) GetAvatarUrlMap(ctx context.Context) map[int]string {
	return as.avatarRepo.GetAvatarUrlMap(ctx)
}
