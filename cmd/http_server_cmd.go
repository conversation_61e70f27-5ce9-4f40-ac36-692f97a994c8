//go:build !windows

package cmd

import (
	"app/config"
	"app/router"
	"fmt"
	"net/http"
	"os"

	"github.com/facebookgo/grace/gracehttp"
	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(server)
}

var server = &cobra.Command{
	Use:   "server",
	Short: "Start HTTP Server",
	Long:  "Start HTTP Server",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()
		serverStart()
	},
}

func serverStart() {
	httpServer := router.InitRouter(router.RegisterRoute)
	fmt.Println("start "+config.C.App.AppName+" listen ", config.C.App.HttpListen)
	srv1 := &http.Server{Addr: config.C.App.HttpListen, Handler: httpServer}
	servers := []*http.Server{srv1}
	if len(config.C.App.TcpListen) > 0 {
		fmt.Println("start "+config.C.App.AppName+" listen tcp ", config.C.App.TcpListen)
		srv2 := &http.Server{Addr: config.C.App.TcpListen, Handler: httpServer}
		servers = append(servers, srv2)
	}
	err := gracehttp.Serve(servers...)
	if err != nil {
		fmt.Printf("start http server failed: %v", err)
		os.Exit(-1)
	}
}
