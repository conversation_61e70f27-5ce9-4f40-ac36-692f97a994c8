package mapper

import (
	"app/model"
	"app/schema"

	"github.com/samber/lo"
)

type NpcCommentMapper struct{}

func NewNpcCommentMapper() *NpcCommentMapper {
	return &NpcCommentMapper{}
}

func (m *NpcCommentMapper) ToNpcCommentShow(comment model.CommentModel, tr schema.TinyRoleInfo, avatarsMap map[int]string) (schema.NpcCommentShow, error) {
	roleInfo := &schema.TinyRoleInfo{
		RoleId:    comment.RoleId,
		RoleName:  tr.RoleName,
		AvatarId:  tr.AvatarId,
		AvatarUrl: avatarsMap[tr.AvatarId],
		IsNpc:     model.IsValidNpcId(comment.RoleId),
	}

	show := schema.NpcCommentShow{
		NpcCommentAddReq: schema.NpcCommentAddReq{
			MomentId:      comment.MomentId,
			RoleId:        comment.RoleId,
			Text:          comment.Text,
			BaseLikeCount: comment.BaseLikeCount,
		},
		NpcCommentAddResp: schema.NpcCommentAddResp{
			BaseAddResp: schema.BaseAddResp{
				Id:         comment.ID,
				CreateTime: comment.Ctime,
				UpdateTime: comment.Utime,
			},
		},
		RoleInfo:      roleInfo,
		RealLikeCount: comment.RealLikeCount,
		LikeCount:     comment.GetLikeCount(),
	}
	return show, nil
}

func (m *NpcCommentMapper) ToNpcCommentShowList(comments []model.CommentModel, roleInfoList []schema.TinyRoleInfo, avatarsMap map[int]string) ([]schema.NpcCommentShow, error) {
	var list []schema.NpcCommentShow
	for _, comment := range comments {
		tr := schema.TinyRoleInfo{}
		roleInfo, isFind := lo.Find(roleInfoList, func(v schema.TinyRoleInfo) bool {
			return v.RoleId == comment.RoleId
		})
		if isFind {
			tr = roleInfo
		}
		show, err := m.ToNpcCommentShow(comment, tr, avatarsMap)
		if err != nil {
			return nil, err
		}
		list = append(list, show)
	}
	return list, nil
}
