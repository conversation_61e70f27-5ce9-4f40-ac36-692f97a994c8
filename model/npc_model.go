package model

const (
	NPC_MAX_ID = 1e6
	NPC_MIN_ID = 1e4
)

// npc用户表
type NpcModel struct {
	BaseCoreModel
	RoleId int64
	// 外显名称
	Name string
	// 账户id，类似推特账号id
	AccountId string
	// 是否为已验证账号
	IsVip              bool
	AvatarId           int
	Signature          string
	BaseFollowingCount int64
	BaseFollowerCount  int64
	// 是否是代号玩家npc
	IsPlayerNpc int8
}

func (m *NpcModel) TableName() string {
	return "l50_npc"
}

// is valid npc id
func IsValidNpcId(roleId int64) bool {
	return roleId >= NPC_MIN_ID && roleId <= NPC_MAX_ID
}
