package repo

import (
	"app/api"
	"app/model"
	"context"
	"errors"

	"gorm.io/gorm"
)

type CommentLikeRepo struct {
	*BaseRepo[model.CommentLikeModel]
}

func NewCommentLikeRepo(db *gorm.DB) *CommentLikeRepo {
	return &CommentLikeRepo{
		BaseRepo: NewBaseRepo[model.CommentLikeModel](db),
	}
}

// add like
func (r *CommentLikeRepo) AddLike(ctx context.Context, roleId int64, comment model.CommentModel) (*model.CommentLikeModel, error) {
	curLike, err := r.<PERSON>ne<PERSON>y<PERSON>uery(ctx, r.Scope(ctx).Unscoped().Where("role_id = ? and comment_id = ?", roleId, comment.ID))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		} else {
			return nil, err
		}
	}
	if curLike.ID > 0 && curLike.DeletedAt == 0 {
		return nil, api.GetBizErr(api.ErrBizCommentLiked)
	}
	addLike := &model.CommentLikeModel{
		RoleId:        roleId,
		CommentId:     comment.ID,
		CommentRoleId: comment.RoleId,
	}
	addLike.ID = curLike.ID
	err = r.DB.WithContext(ctx).Save(&addLike).Error
	if err != nil {
		return nil, err
	}
	return addLike, nil
}

// isLike
func (r *CommentLikeRepo) IsLike(ctx context.Context, roleId int64, commentId int64) (bool, error) {
	var entity model.CommentLikeModel
	err := r.DB.WithContext(ctx).Where("role_id = ? and comment_id= ?", roleId, commentId).First(&entity).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, err
	}
	return entity.ID > 0, nil
}

// delete like
func (r *CommentLikeRepo) DeleteLike(ctx context.Context, roleId int64, commentId int64) (int64, error) {
	return r.DeleteByQuery(ctx, "role_id = ? and comment_id = ?", roleId, commentId)
}

// get isLikeMap by roleId and commentIds
func (r *CommentLikeRepo) GetIsLikeMap(ctx context.Context, roleId int64, commentIds []int64) (map[int64]bool, error) {
	var entityList []model.CommentLikeModel
	query := r.Scope(ctx).Where("role_id = ?", roleId).Where("comment_id IN (?)", commentIds)
	err := query.Find(&entityList).Error
	if err != nil {
		return nil, err
	}
	isLikeMap := make(map[int64]bool)
	for _, v := range entityList {
		isLikeMap[v.CommentId] = true
	}
	return isLikeMap, nil
}
