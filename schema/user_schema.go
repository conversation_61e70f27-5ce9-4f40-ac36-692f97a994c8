package schema

type TinyRoleInfo struct {
	RoleId    int64  `json:"roleId"`
	RoleName  string `json:"name"`
	AvatarId  int    `json:"avatarId"`
	AvatarUrl string `json:"avatarUrl"`
	// 是否为NPC角色
	IsNpc bool `json:"isNpc"`
	// 是否加v认证
	IsCertified bool `json:"isCertified"`
	// 推特账号名字
	AccountId string `json:"account"`
}

type UserProfileResp struct {
	TinyRoleInfo
	// 粉丝数
	FollowerCount int64 `json:"followerCount"`
	// 关注数
	FollowingCount int64 `json:"followingCount"`
}

type UserProfileReq struct {
	BaseAuthReq
	// 查看的角色id
	ViewRoleId int64 `json:"viewRoleId" form:"viewRoleId" binding:"required"`
}

type CoreRoleInfo struct {
	TinyRoleInfo
	// 是否已关注
	IsFollow bool `json:"isFollow"`
}
