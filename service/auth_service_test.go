package service

import (
	"app/config"
	"app/schema"
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

// test decode skey function
func TestDecodeSkey(t *testing.T) {
	conf := &config.AuthConfig{
		SkeySecret: "jwt_token_secret",
		CheatSkey:  "CHEAT_SKEY_ONLY_FOR_TEST",
	}
	authService := NewAuthService(conf)
	ctx := context.Background()

	var roleId int64 = 7023086
	authInfo, err := authService.DecodeSkey(ctx, "WzUwOSwicm9sZSIsIjcwMjMwODYiXQ.JicN7FRr2nYd4HCgnfrjS95tR8HE7OHrraEearMcaw8", roleId)
	if err != nil {
		t.Error("decode skey error: ", err)
		return
	}
	assert.Equal(t, authInfo.IdType, "role")
	assert.Equal(t, authInfo.IdVal, roleId)
}

func TestValidateLogin(t *testing.T) {
	conf := &config.AuthConfig{
		SkeySecret: "jwt_token_secret",
		CheatSkey:  "CHEAT_SKEY_ONLY_FOR_TEST",
	}
	authService := NewAuthService(conf)
	ctx := context.Background()

	var roleId int64 = 7023086
	req := &schema.BaseAuthReq{
		Skey:   "WzUwOSwicm9sZSIsIjcwMjMwODYiXQ.JicN7FRr2nYd4HCgnfrjS95tR8HE7OHrraEearMcaw8",
		RoleId: roleId,
	}
	authInfo, err := authService.ValidateLogin(ctx, req)
	if err != nil {
		t.Error("validate login error: ", err)
		return
	}
	assert.Equal(t, authInfo.IdType, "role")
	assert.Equal(t, authInfo.IdVal, roleId)

	// fail case
	req.Skey = "WzUwOSwicm9sZSIsIjcwMjMwODYiXQ.JicN7FRr2nYd4HCgnfrjS95tR8HE7OHrraEearMcaw9"
	req2 := &schema.BaseAuthReq{
		Skey:   "WzUwOSwicm9sZSIsIjcwMjMwODYiXQ.JicN7FRr2nYd4HCgnfrjS95tR8HE7OHrraEearMcaw9",
		RoleId: roleId,
	}
	_, err = authService.ValidateLogin(ctx, req2)
	assert.NotNil(t, err)
}
