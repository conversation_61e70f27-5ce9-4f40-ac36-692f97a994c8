package mapper

import (
	"fmt"
	"regexp"
	"strconv"
)

type TopicMapper struct{}

func NewTopicMapper() *TopicMapper {
	return &TopicMapper{}
}

// topicId and topicName to generate a link
func (m *TopicMapper) ToTopicLink(topicId int64, topicName string) string {
	return fmt.Sprintf("<a socialHot=%d>#%s#</a>", topicId, topicName)
}

const SOCIAL_HOT_REGEX = `<a\s+socialHot=\d+>(#[^#]+#)<\/a>`

// RemoveSocialHotTag
func (m *TopicMapper) RemoveSocialHotTag(topicLink string) string {
	re := regexp.MustCompile(SOCIAL_HOT_REGEX)
	topicLink = re.ReplaceAllString(topicLink, "$1")
	return topicLink
}

// ExtractTopicIds extracts topic IDs from text matching SOCIAL_HOT_REGEX pattern
func (m *TopicMapper) ExtractTopicIds(text string) []int {
	re := regexp.MustCompile(`<a\s+socialHot=(\d+)>(#[^#]+#)<\/a>`)
	matches := re.FindAllStringSubmatch(text, -1)
	
	ids := make([]int, 0)
	for _, match := range matches {
		if len(match) >= 2 {
			if id, err := strconv.Atoi(match[1]); err == nil {
				ids = append(ids, id)
			}
		}
	}
	
	return ids
}
