package schema

type NpcCommentAddReq struct {
	// 动态id
	MomentId int64 `json:"momentId" binding:"required"`
	// 角色id
	RoleId int64 `json:"roleId" binding:"required"`
	// 评论内容
	Text string `json:"text" desc:"评论内容" binding:"required,max=100"`
	// 评论基础点赞数
	BaseLikeCount int64 `json:"baseLikeCount" desc:"评论点赞数"`
}

type NpcCommentAddResp struct {
	BaseAddResp
}

const (
	RoleTypeAll    = "all"
	RoleTypeNpc    = "npc"
	RoleTypePlayer = "player"
)

type NpcCommentListReq struct {
	// 动态id
	MomentId int64  `json:"momentId" form:"momentId"`
	RoleType string `json:"roleType" form:"roleType" binding:"omitempty,oneof=all npc player"`
	Pagination
}

type NpcCommentListResp struct {
	List  []NpcCommentShow `json:"list"`
	Total int64            `json:"total"`
}

type NpcCommentShow struct {
	NpcCommentAddReq
	NpcCommentAddResp
	RoleInfo *TinyRoleInfo `json:"roleInfo"`
	// 真实点赞数
	RealLikeCount int64 `json:"realLikeCount"`
	// 合计点赞数
	LikeCount int64 `json:"likeCount"`
}

type NpcCommentDeleteReq struct {
	// 评论id
	Id int64 `json:"id" form:"id" binding:"required"`
}

type NpcCommentDeleteResp struct {
	BaseDeleteResp
}

type NpcCommentUpdateReq struct {
	Id            int64   `json:"id" form:"id" binding:"required"`
	BaseLikeCount *int64  `json:"baseLikeCount" form:"baseLikeCount" binding:"omitempty,min=0"`
	Text          *string `json:"text" form:"text" binding:"omitempty,max=50"`
}
