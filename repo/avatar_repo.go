package repo

import (
	"app/assets"
	"app/schema"
	"app/util"
	"context"
	_ "embed"
	"encoding/json"
)

type AvatarRepo struct{}

func NewAvatarRepo() *AvatarRepo {
	return &AvatarRepo{}
}

const DEFAULT_AVATAR = "https://l18-md-cn.fp.ps.netease.com/file/666aadd6e9d60863fe75c726g0ZH6liW05"

func (as *AvatarRepo) ListAvatars(ctx context.Context) ([]schema.AvatarItem, error) {
	// 解析嵌入的 JSON 数据
	resp := []schema.AvatarItem{}
	npcAvatarsData := assets.NpcAvatarsData
	err := json.Unmarshal(npcAvatarsData, &resp)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("ParseNpcAvatarsDataFail")
		return resp, nil
	}
	return resp, nil
}

// check is valid avatar id
func (as *AvatarRepo) IsValidAvatarId(ctx context.Context, avatarId int) bool {
	return true
}

// getAvatarUrl 获取头像url
func (as *AvatarRepo) GetAvatarUrl(ctx context.Context, avatarId int) string {
	avatars, err := as.ListAvatars(ctx)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("avatarId", avatarId).Error("getAvatarUrlFail")
		return as.GetDefaultAvatarUrl()
	}
	for _, avatar := range avatars {
		if avatar.Id == avatarId {
			return avatar.Url
		}
	}
	return as.GetDefaultAvatarUrl()
}

// getAvatarUrlMap 获取头像url map[string][string]
func (as *AvatarRepo) GetAvatarUrlMap(ctx context.Context) map[int]string {
	avatars, err := as.ListAvatars(ctx)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).Error("getAvatarUrlMapFail")
		return nil
	}
	avatarUrlMap := make(map[int]string)
	for _, avatar := range avatars {
		avatarUrlMap[avatar.Id] = avatar.Url
	}
	return avatarUrlMap
}

// getDefaultAvatarUrl 获取默认头像url
func (as *AvatarRepo) GetDefaultAvatarUrl() string {
	avatarUrl := DEFAULT_AVATAR
	return avatarUrl
}
