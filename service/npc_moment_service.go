package service

import (
	"app/api"
	"app/conn"
	"app/mapper"
	"app/model"
	"app/repo"
	"app/schema"
	"app/util"
	"context"
	"errors"
	"strings"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type NpcMomentService struct {
	momentRepo        *repo.MomentRepo
	npcRepo           *repo.NpcRepo
	avatarRepo        *repo.AvatarRepo
	momentCollectRepo *repo.MomentCollectRepo
	momentLikeRepo    *repo.MomentLikeRepo
	momentTopicRepo   *repo.MomentTopicRepo
	topicRepo         *repo.TopicRepo
	topicIdCache      *repo.TopicIdCache
	topicService      *TopicService
}

func NewNpcMomentService(
	momentRepo *repo.MomentRepo,
	npcRepo *repo.NpcRepo,
	momentCollectRepo *repo.MomentCollectRepo,
	momentLikeRepo *repo.MomentLikeRepo,
	avatarRepo *repo.AvatarRepo,
	momentTopicRepo *repo.MomentTopicRepo,
	topicRepo *repo.TopicRepo,
	topicIdCache *repo.TopicIdCache,
	topicService *TopicService,
) *NpcMomentService {
	return &NpcMomentService{
		momentRepo:        momentRepo,
		npcRepo:           npcRepo,
		momentCollectRepo: momentCollectRepo,
		momentLikeRepo:    momentLikeRepo,
		avatarRepo:        avatarRepo,
		momentTopicRepo:   momentTopicRepo,
		topicRepo:         topicRepo,
		topicIdCache:      topicIdCache,
		topicService:      topicService,
	}
}

func (s *NpcMomentService) SetNpcMomentTopTime(ctx context.Context, momentId int64, topTime int64) (*schema.NpcMomentTopResp, error) {
	moment, err := s.momentRepo.FindByID(ctx, momentId)
	if err != nil {
		return nil, err
	}
	err = s.momentRepo.Scope(ctx).
		Model(&moment).
		Where("id = ?", moment.ID).
		Updates(map[string]any{"top_time": topTime}).Error
	if err != nil {
		return nil, err
	}
	resp := &schema.NpcMomentTopResp{
		Id:         moment.ID,
		UpdateTime: moment.Utime,
	}
	return resp, nil
}

func (s *NpcMomentService) SetNpcMomentTop(ctx context.Context, req *schema.NpcMomentTopReq) (*schema.NpcMomentTopResp, error) {
	return s.SetNpcMomentTopTime(ctx, req.Id, time.Now().UnixMilli())
}

func (s *NpcMomentService) SetNpcMomentUnTop(ctx context.Context, req *schema.NpcMomentTopReq) (*schema.NpcMomentTopResp, error) {
	return s.SetNpcMomentTopTime(ctx, req.Id, 0)
}

func (s *NpcMomentService) GetNpcMoment(ctx context.Context, req *schema.NpcMomentShowReq) (schema.NpcMomentShowResp, error) {
	resp := schema.NpcMomentShowResp{}
	moment, err := s.momentRepo.FindByID(ctx, req.Id)
	if err != nil {
		return resp, err
	}
	roleIds := []int64{moment.RoleId}
	roleInfoMap := s.npcRepo.GetRoleInfoMap(ctx, roleIds)
	avatarMap := s.avatarRepo.GetAvatarUrlMap(ctx)
	mapper := mapper.NewNpcMomentMapper()
	resp, err = mapper.ToNpcMomentShow(*moment, roleInfoMap, avatarMap)
	if err != nil {
		return resp, err
	}
	return resp, nil
}

func (s *NpcMomentService) ReplaceTopicInText(text string, topicList []model.TopicModel) string {
	mapper := mapper.NewTopicMapper()
	for _, topic := range topicList {
		text = strings.ReplaceAll(text, "#"+topic.Name+"#", mapper.ToTopicLink(topic.ID, topic.Name))
	}
	return text
}

func (s *NpcMomentService) UpdateNpcMoment(ctx context.Context, momentUpdate *schema.NpcMomentUpdateReq) (*schema.NpcMomentUpdateResp, error) {
	var relationTopicIds []int64
	moment, err := s.CheckNpcMomentExist(momentUpdate.Id)
	if err != nil {
		return nil, err
	}
	newText := moment.Text
	visible := moment.Visible
	// 是否需要重建话题动态关系
	isNeedRebuildTopicRelation := false
	areaId := moment.AreaId

	curTopicIdList, err := s.momentTopicRepo.GetTopicIdsByMomentId(ctx, moment.ID)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("momentId", moment.ID).Error("GetTopicIdsByMomentIdError")
	}

	updates := map[string]interface{}{}
	if momentUpdate.Text != nil && momentUpdate.Text != &moment.Text {
		updates["text"] = newText
		isNeedRebuildTopicRelation = true
	}

	if momentUpdate.ImgList != nil {
		if momentUpdate.VideoList != nil {
			return nil, api.GetBizErr(api.ErrBizImageVideoBothUnSupported)
		}
		if len(*momentUpdate.ImgList) > 0 {
			for _, img := range *momentUpdate.ImgList {
				if !util.IsValidUrl(img) {
					return nil, api.GetBizErr(api.ErrBizInvalidImageUrl)
				}
			}
		}
		updates["img_list"] = util.StrArr2JsonArr(*momentUpdate.ImgList)
	}
	if momentUpdate.VideoList != nil {
		for _, video := range *momentUpdate.VideoList {
			if !util.IsValidUrl(video) {
				return nil, api.GetBizErr(api.ErrBizInvalidVideoUrl)
			}
		}
		updates["video_list"] = util.StrArr2JsonArr(*momentUpdate.VideoList)

		if momentUpdate.VideoCoverList != nil {
			for _, videoCover := range *momentUpdate.VideoCoverList {
				if !util.IsValidUrl(videoCover) {
					return nil, api.GetBizErr(api.ErrBizInvalidVideoCoverUrl)
				}
			}
			updates["video_cover_list"] = util.StrArr2JsonArr(*momentUpdate.VideoCoverList)
		}
	}
	if momentUpdate.BaseLikeCount != nil {
		updates["base_like_count"] = *momentUpdate.BaseLikeCount
	}
	if momentUpdate.ViewCount != nil {
		updates["view_count"] = *momentUpdate.ViewCount
	}
	baseLikeCount, isSetBaseLikeCount := updates["base_like_count"]
	if !isSetBaseLikeCount {
		baseLikeCount = moment.BaseLikeCount
	}
	viewCount, isSetViewCount := updates["view_count"]
	if !isSetViewCount {
		viewCount = moment.ViewCount
	}
	if viewCount.(int64) < baseLikeCount.(int64) {
		return nil, api.GetBizErr(api.ErrBizViewCountLessThanLikeCount)
	}
	if momentUpdate.TaskId != nil {
		updates["task_id"] = *momentUpdate.TaskId
	}
	if momentUpdate.IsTemplate != nil {
		updates["is_template"] = *momentUpdate.IsTemplate
		if *momentUpdate.IsTemplate {
			visible = 0
			updates["visible"] = 0
		}
		isNeedRebuildTopicRelation = true
	}
	if momentUpdate.AreaId != nil {
		updates["area_id"] = *momentUpdate.AreaId
		areaId = *momentUpdate.AreaId
		isNeedRebuildTopicRelation = true
	}
	if momentUpdate.PublicTime > 0 {
		updates["public_time"] = momentUpdate.PublicTime
	}
	if momentUpdate.CategoryId != nil {
		updates["category_id"] = *momentUpdate.CategoryId
	}
	if momentUpdate.ParentId != nil {
		parentId := *momentUpdate.ParentId
		if err := s.momentRepo.IsValidParentMomentId(parentId); err != nil {
			return nil, err
		}
		updates["parent_id"] = *momentUpdate.ParentId
	}

	if isNeedRebuildTopicRelation {
		// delete moment and topicIds relation then rebuild
		s.momentTopicRepo.DeleteByMomentId(ctx, moment.ID)
		if momentUpdate.Text != nil {
			newText = *momentUpdate.Text
		}
		newMoment := model.MomentModel{Text: newText}
		var topicList []model.TopicModel
		topicTextList := newMoment.GetTopicTextList()
		if len(topicTextList) > 0 {
			// iterate topicTextList and init topic
			for _, topicText := range topicTextList {
				err := s.topicRepo.EnsureTopicCreated(ctx, topicText, moment.AreaId)
				if err != nil {
					return nil, err
				}
			}

			// get topic and id list find by textList
			topicList, err = s.topicRepo.FindByTextList(ctx, topicTextList, moment.AreaId)
			if err != nil {
				return nil, err
			}
		}

		if len(topicTextList) > 0 {
			// save topic and moment relation
			topicIds := lo.Map(topicList, func(v model.TopicModel, _ int) int64 { return v.ID })
			newMoment := model.MomentModel{
				Text:    newText,
				RoleId:  moment.RoleId,
				Visible: visible,
			}
			newMoment.ID = moment.ID
			err = s.momentTopicRepo.SaveMomentTopicsRelation(ctx, newMoment, topicIds)
			if err != nil {
				return nil, err
			}
		}

		topicMapper := mapper.NewTopicMapper()
		originText := topicMapper.RemoveSocialHotTag(newText)
		text := s.ReplaceTopicInText(originText, topicList)
		updates["text"] = text
	}

	err = conn.GetDB().Model(&model.MomentModel{}).Where("id = ?", momentUpdate.Id).Updates(updates).Error
	if err != nil {
		return nil, err
	}

	resp := &schema.NpcMomentUpdateResp{
		BaseUpdateResp: schema.BaseUpdateResp{
			Id:         moment.ID,
			UpdateTime: moment.Utime,
		},
	}

	if isNeedRebuildTopicRelation {
		newM := model.MomentModel{Text: newText}
		newTopicTextList := newM.GetTopicTextList()
		if len(newTopicTextList) > 0 {
			// iterate topicTextList and init topic
			for _, topicText := range newTopicTextList {
				err := s.topicRepo.EnsureTopicCreated(ctx, topicText, areaId)
				if err != nil {
					return nil, err
				}
			}
		}

		newTopicIdList, err := s.momentTopicRepo.GetTopicIdsByMomentId(ctx, moment.ID)
		if err != nil {
			util.LogWithContext(ctx).WithError(err).WithField("momentId", moment.ID).Error("GetTopicIdsByMomentIdError")
		}

		// relationTopicIds is set of newTopListIdList and curTopListIdList, use lo method
		relationTopicIds = lo.Union(curTopicIdList, newTopicIdList)
		// log this option add curTopicIdList and newTopicIdList
		util.LogWithContext(ctx).WithField("curTopicIdList", curTopicIdList).WithField("newTopicIdList", newTopicIdList).WithField("relationTopicIds", relationTopicIds).Info("relationTopicIds")

		_, delTopicIds := lo.Difference(newTopicIdList, curTopicIdList)

		if len(delTopicIds) > 0 {
			err = s.momentTopicRepo.DelMomentTopicsRelation(ctx, moment.ID, delTopicIds)
			if err != nil {
				util.LogWithContext(ctx).WithError(err).WithField("momentId", moment.ID).Error("DeleteMomentTopicErrorForDelTopicIds")
			}

			s.topicService.CheckTopicReferenceAndClean(ctx, delTopicIds, moment.ID)
		}
	}

	return resp, nil
}

func (s *NpcMomentService) GetNpcSubMomentList(ctx context.Context, req *schema.NpcSubMomentListReq) (schema.NpcSubMomentListResp, error) {
	resp := schema.NpcSubMomentListResp{List: []schema.NpcMomentShowResp{}}
	query := s.momentRepo.Scope(ctx).Where("parent_id = ?", req.MomentId)
	momentList, err := s.momentRepo.FindWithPagination(ctx, query, schema.Pagination{Page: 1, PageSize: repo.MOMENT_MAX_SUB_MOMENT_COUNT})
	if err != nil {
		return resp, err
	}
	mapper := mapper.NewNpcMomentMapper()

	roleIds := make([]int64, 0)
	for _, moment := range momentList {
		roleIds = append(roleIds, moment.RoleId)
	}
	npcInfoMap := s.npcRepo.GetRoleInfoMap(ctx, roleIds)
	avatarMap := s.avatarRepo.GetAvatarUrlMap(ctx)
	list, err := mapper.ToNpcMomentShowList(momentList, npcInfoMap, avatarMap, nil)
	if err != nil {
		return resp, err
	}
	resp.List = list
	return resp, nil
}

func (s *NpcMomentService) GetNpcMomentList(ctx context.Context, req *schema.NpcMomentListReq) (schema.NpcMomentListResp, error) {
	resp := schema.NpcMomentListResp{}
	if req.CategoryId != nil {
		if !s.momentRepo.IsValidCategoryId(*req.CategoryId) {
			return resp, api.GetBizErr(api.ErrBizInvalidMomentCategory)
		}
	}
	baseQuery := func() *gorm.DB {
		query := s.momentRepo.Scope(ctx).Where("parent_id = 0")
		if req.RoleId != nil {
			query = query.Where("role_id = ?", *req.RoleId)
		} else {
			if req.OnlyShowNpc != nil && !*req.OnlyShowNpc {
				// when onlyShowNpc is false
			} else {
				query = query.Where("role_id >= ? AND role_id < ?", model.NPC_MIN_ID, model.NPC_MAX_ID)
			}
		}
		if req.HasTask != nil {
			hasTask := *req.HasTask
			if hasTask {
				query = query.Where("task_id > 0")
			} else {
				query = query.Where("task_id = 0")
			}
		}
		if req.CategoryId != nil {
			query = query.Where("category_id = ?", *req.CategoryId)
		}
		if req.AreaId != nil {
			query = query.Where("area_id = ?", *req.AreaId)
		}
		return query
	}

	var momentList []model.MomentModel

	momentList, err := s.momentRepo.FindWithPagination(ctx, baseQuery().Order("top_time desc").Order("id desc"), schema.Pagination{Page: req.Page, PageSize: req.PageSize})
	if err != nil {
		return resp, err
	}

	momentIds := lo.Map(momentList, func(v model.MomentModel, _ int) int64 { return v.ID })
	subMomentList, err := s.momentRepo.GetSubListByMomentIds(ctx, momentIds)
	if err != nil {
		return resp, err
	}

	mapper := mapper.NewNpcMomentMapper()

	roleIds := make([]int64, 0)
	for _, moment := range momentList {
		roleIds = append(roleIds, moment.RoleId)
	}
	for _, moment := range subMomentList {
		roleIds = append(roleIds, moment.RoleId)
	}

	npcInfoMap := s.npcRepo.GetRoleInfoMap(ctx, roleIds)
	avatarMap := s.avatarRepo.GetAvatarUrlMap(ctx)
	list, err := mapper.ToNpcMomentShowList(momentList, npcInfoMap, avatarMap, subMomentList)
	if err != nil {
		return resp, err
	}

	total, err := s.momentRepo.CountByQuery(ctx, baseQuery())
	if err != nil {
		return resp, err
	}
	resp = schema.NpcMomentListResp{
		List:  list,
		Total: total,
	}
	return resp, nil
}

func (*NpcMomentService) CheckNpcMomentExist(id int64) (*model.MomentModel, error) {
	var moment model.MomentModel
	err := conn.GetDB().Where("id= ?", id).First(&moment).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, api.GetBizErr(api.ErrDataNotFound)
		}
		return nil, err
	}
	return &moment, nil
}

func (s *NpcMomentService) GetNpcMomentFilters(ctx context.Context) (*schema.NpcMomentListFilterResp, error) {
	categories, err := s.momentRepo.GetCategoryList()
	if err != nil {
		return nil, err
	}
	resp := &schema.NpcMomentListFilterResp{
		Categories: categories,
	}
	return resp, nil
}
