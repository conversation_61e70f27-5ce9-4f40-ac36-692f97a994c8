package service

import (
	"app/api"
	"app/config"
	"app/conn"
	"app/mapper"
	"app/model"
	"app/repo"
	"app/schema"
	"app/util"
	"context"
	"errors"

	"gorm.io/gorm"
)

type NpcService struct {
	npcRepo      *repo.NpcRepo
	momentRepo   *repo.MomentRepo
	commentRepo  *repo.CommentRepo
	avatarRepo   *repo.AvatarRepo
	roleInfoRepo *repo.RoleInfoRepo
}

func NewNpcService(npcRepo *repo.NpcRepo, momentRepo *repo.MomentRepo, commentRepo *repo.CommentRepo, avatarRepo *repo.AvatarRepo, roleinfoRepo *repo.RoleInfoRepo) *NpcService {
	return &NpcService{
		npcRepo:      npcRepo,
		momentRepo:   momentRepo,
		commentRepo:  commentRepo,
		avatarRepo:   avatarRepo,
		roleInfoRepo: roleinfoRepo,
	}
}

func (s *NpcService) AddNpc(ctx context.Context, npcAdd *schema.NpcAddReq) (*schema.NpcAddResp, error) {
	roleId := npcAdd.RoleId
	if !model.IsValidNpcId(roleId) {
		return nil, api.GetBizErr(api.ErrBizNpcIdInvalid)
	}
	if npcAdd.AvatarId != 0 {
		isValid := s.avatarRepo.IsValidAvatarId(ctx, npcAdd.AvatarId)
		if !isValid {
			return nil, api.GetBizErr(api.ErrBizAvatarIdInvalid)
		}
	}
	curNpc, err := s.npcRepo.FindOneByQuery(ctx, s.npcRepo.DB.Unscoped().Where("role_id", npcAdd.RoleId))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		} else {
			return nil, err
		}
	}
	if curNpc.ID > 0 && curNpc.DeletedAt == 0 {
		return nil, api.GetBizErr(api.ErrBizNpcCreated)
	}
	addNpc := &model.NpcModel{
		RoleId:             npcAdd.RoleId,
		Name:               npcAdd.Name,
		AvatarId:           npcAdd.AvatarId,
		Signature:          npcAdd.Signature,
		BaseFollowingCount: npcAdd.BaseFollowingCount,
		BaseFollowerCount:  npcAdd.BaseFollowerCount,
		AccountId:          npcAdd.AccountId,
		IsVip:              npcAdd.IsVip,
	}
	addNpc.ID = curNpc.ID
	err = conn.GetDB().Save(addNpc).Error
	if err != nil {
		util.LogWithContext(ctx).WithField("addNpc", addNpc).Warn("addNpcFail")
		return nil, err
	}

	// save npc roleinfo to db
	roleInfo, err := s.roleInfoRepo.SaveNpcRoleInfo(ctx, *addNpc)
	if err != nil {
		util.LogWithContext(ctx).WithField("roleInfo", roleInfo).Warn("SaveNpcRoleInfoFail")
		return nil, err
	}

	resp := &schema.NpcAddResp{
		BaseAddResp: schema.BaseAddResp{
			Id:         addNpc.ID,
			CreateTime: addNpc.Ctime,
			UpdateTime: addNpc.Utime,
		},
	}
	return resp, nil
}

func (s *NpcService) UpdateNpc(ctx context.Context, npcUpdate *schema.NpcUpdateReq) (schema.NpcUpdateResp, error) {
	resp := schema.NpcUpdateResp{}
	npc, err := s.npcRepo.FindByRoleId(ctx, npcUpdate.RoleId)
	if err != nil {
		return resp, err
	}

	updates := map[string]interface{}{}

	npcRoleUpdates := map[string]interface{}{}

	if npcUpdate.Name != nil {
		updates["name"] = *npcUpdate.Name
		npcRoleUpdates["RoleName"] = *npcUpdate.Name
	}
	if npcUpdate.AvatarId != nil {
		avatarId := *npcUpdate.AvatarId
		if !s.avatarRepo.IsValidAvatarId(ctx, avatarId) {
			return resp, api.GetBizErr(api.ErrBizAvatarIdInvalid)
		}
		updates["avatar_id"] = *npcUpdate.AvatarId
		npcRoleUpdates["Avatar"] = *npcUpdate.AvatarId
	}
	if npcUpdate.BaseFollowingCount != nil {
		updates["base_following_count"] = *npcUpdate.BaseFollowingCount
	}
	if npcUpdate.BaseFollowerCount != nil {
		updates["base_follower_count"] = *npcUpdate.BaseFollowerCount
	}
	if npcUpdate.Signature != nil {
		updates["signature"] = *npcUpdate.Signature
	}
	if npcUpdate.AccountId != nil {
		updates["account_id"] = *npcUpdate.AccountId
	}
	if npcUpdate.IsVip != nil {
		updates["is_vip"] = *npcUpdate.IsVip
	}
	err = s.npcRepo.Scope(ctx).Where("role_id = ?", npc.RoleId).Updates(updates).Error
	if err != nil {
		util.LogWithContext(ctx).WithField("roleId", npc.RoleId).WithField("updates", updates).Warn("updateNpcFail")
		return resp, err
	}
	err = s.roleInfoRepo.Scope(ctx).Where("RoleId = ?", npc.RoleId).Updates(npcRoleUpdates).Error
	if err != nil {
		util.LogWithContext(ctx).WithField("roleId", npc.RoleId).WithField("npcRoleUpdates", npcRoleUpdates).Warn("updateNpcRoleInfoFail")
		return resp, err
	}

	resp = schema.NpcUpdateResp{
		UpdateTime: npc.Ctime,
	}
	return resp, nil
}

func (s *NpcService) GetNpc(ctx context.Context, roleId int64) (schema.NpcShow, error) {
	resp := schema.NpcShow{}
	npc, err := s.npcRepo.FindByRoleId(ctx, roleId)
	if err != nil {
		return resp, err
	}
	avatarsMap := s.avatarRepo.GetAvatarUrlMap(ctx)
	npcMapper := mapper.NewNpcMapper(s.avatarRepo)
	resp = npcMapper.ToNpcShow(npc, avatarsMap)
	return resp, nil
}

func (s *NpcService) GetNpcList(ctx context.Context, req *schema.NpcListReq) (schema.NpcListResp, error) {
	resp := schema.NpcListResp{}
	npcMaxSize := config.C.Biz.NpcMaxSize
	pagination := schema.Pagination{
		Page:     1,
		PageSize: npcMaxSize,
	}
	if req.Page > 0 && req.PageSize > 0 {
		pagination.Page = req.Page
		pagination.PageSize = req.PageSize
	}
	baseQuery := func() *gorm.DB {
		query := s.npcRepo.Scope(ctx)
		if req.Kw != "" {
			searchKw := "%" + req.Kw + "%"
			query = query.Where("role_id LIKE ? or name LIKE ?", searchKw, searchKw)
		}
		if req.RoleId != 0 {
			query = query.Where("role_id = ?", req.RoleId)
		}
		return query
	}
	listQuery := baseQuery().Order("is_player_npc desc, utime desc")
	npcList, err := s.npcRepo.FindWithPagination(ctx, listQuery, pagination)
	if err != nil {
		return resp, err
	}
	total, err := s.npcRepo.CountByQuery(ctx, baseQuery())
	if err != nil {
		return resp, err
	}
	avatarsMap := s.avatarRepo.GetAvatarUrlMap(ctx)
	npcMapper := mapper.NewNpcMapper(s.avatarRepo)
	npcShowList := npcMapper.ToNpcShowList(npcList, avatarsMap)
	resp = schema.NpcListResp{
		List:  npcShowList,
		Total: total,
	}
	return resp, nil
}

func (s *NpcService) DeleteNpc(ctx context.Context, roleId int64) (*schema.NpcDeleteResp, error) {
	npc, err := s.npcRepo.FindByRoleId(ctx, roleId)
	if err != nil {
		return nil, err
	}
	delNpcRet := conn.GetDB().Delete(&npc)
	if err = delNpcRet.Error; err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).Error("deleteNpcFail")
		return nil, err
	}

	delMomentCount, err := s.momentRepo.DeleteByRoleId(ctx, roleId)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).Error("deleteNpcMomentFail")
		return nil, err
	}

	delCommentCount, err := s.commentRepo.DeleteByRoleId(ctx, roleId)
	if err != nil {
		util.LogWithContext(ctx).WithError(err).WithField("roleId", roleId).Error("deleteNpcCommentFail")
		return nil, err
	}

	return &schema.NpcDeleteResp{
		BaseDeleteResp: schema.BaseDeleteResp{
			Id:        npc.ID,
			DeletedAt: int64(npc.DeletedAt),
		},
		DelMomentCount:  delMomentCount,
		DelCommentCount: delCommentCount,
	}, nil
}
