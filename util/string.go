package util

import (
	"encoding/json"
	"net/url"
	"strconv"
	"strings"
	"unicode"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
)

func FirstLetterToLower(str string) string {
	runes := []rune(str)
	if len(runes) > 0 {
		runes[0] = unicode.ToLower(runes[0])
	}
	return string(runes)
}

func CsvToIntArray(str string) ([]int, error) {
	ret := make([]int, 0)
	for _, v := range strings.Split(str, ",") {
		i, err := strconv.Atoi(v)
		if err != nil {
			return nil, err
		}
		ret = append(ret, i)
	}
	return ret, nil
}

func IsValidUrl(str string) bool {
	u, err := url.ParseRequestURI(str)
	if err != nil {
		return false
	}
	return u.Scheme != "" && u.Host != ""
}

func StrArr2JsonArr(strArray []string) string {
	bytes, err := json.Marshal(strArray)
	if err != nil {
		elog.WithError(err).With<PERSON>ield("strArray", strArray).Error("json marshal strArray error")
		return ""
	}
	return string(bytes)
}
